# 📡 مرجع API للإضافة الديناميكية

## 🌐 نظرة عامة

API الإضافة الديناميكية يوفر واجهة RESTful لإدارة المكونات والبيانات. جميع الاستجابات بصيغة JSON مع دعم UTF-8.

### Base URL
```
http://localhost/Dynamic/api/
```

### Authentication
حالياً لا يتطلب API مصادقة، لكن يتم التحقق من المصدر عبر CORS.

## 📋 نقاط النهاية (Endpoints)

### 1. إعدادات النظام

#### GET `/config.php`
جلب إعدادات النظام العامة.

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم جلب الإعدادات بنجاح",
    "data": {
        "theme_color": "#667eea",
        "api_version": "1.0.0",
        "cache_duration": 3600,
        "debug_mode": false,
        "features_enabled": ["modal", "designer", "export"]
    },
    "timestamp": "2024-01-15 10:30:00",
    "version": "1.0.0"
}
```

### 2. إدارة المكونات

#### GET `/components.php`
جلب جميع المكونات النشطة.

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم جلب المكونات بنجاح",
    "data": {
        "styles": "/* CSS مجمع من جميع المكونات */",
        "templates": {
            "mainUI": "<div>HTML للواجهة الرئيسية</div>",
            "modal": "<div>HTML للموديل</div>"
        },
        "scripts": "/* JavaScript مجمع */",
        "metadata": {
            "total_components": 5,
            "last_update": "2024-01-15 10:25:00",
            "version": "1.0.0"
        }
    }
}
```

#### POST `/update-component.php`
تحديث أو إضافة مكون جديد.

**البيانات المطلوبة:**
```json
{
    "component_name": "newComponent",
    "component_type": "modal",
    "html_code": "<div>HTML Code</div>",
    "css_code": ".class { color: blue; }",
    "js_code": "function test() { console.log('test'); }",
    "description": "وصف المكون",
    "version": "1.0.0"
}
```

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم حفظ المكون بنجاح",
    "data": {
        "component_id": 123,
        "component_name": "newComponent",
        "created_at": "2024-01-15 10:30:00"
    }
}
```

### 3. بيانات المدارس

#### POST `/save-data.php`
حفظ بيانات مدرسة جديدة أو تحديث موجودة.

**البيانات المطلوبة:**
```json
{
    "type": "SCHOOL_DATA",
    "data": {
        "school_id": "ABC123DEF456GHI789JKL012MNO345PQ",
        "school_name": "مدرسة الأمل الابتدائية",
        "manager_name": "أحمد محمد السعدي",
        "students_count": 147,
        "students": [
            {
                "name": "محمد أحمد علي",
                "class": "الأول الابتدائي-أ",
                "national_id": "1234567890",
                "parent_phone": "966501234567"
            }
        ]
    },
    "timestamp": 1642234567890,
    "source": "kushoof_dynamic_extension"
}
```

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم حفظ البيانات بنجاح",
    "data": {
        "school_id": "ABC123DEF456GHI789JKL012MNO345PQ",
        "students_saved": 147,
        "last_sync": "2024-01-15 10:30:00"
    }
}
```

#### POST `/fetch-data.php`
جلب بيانات مدرسة محددة.

**البيانات المطلوبة:**
```json
{
    "school_id": "ABC123DEF456GHI789JKL012MNO345PQ",
    "include_students": true,
    "limit": 100
}
```

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم جلب البيانات بنجاح",
    "data": {
        "school_info": {
            "school_id": "ABC123DEF456GHI789JKL012MNO345PQ",
            "school_name": "مدرسة الأمل الابتدائية",
            "manager_name": "أحمد محمد السعدي",
            "students_count": 147,
            "last_sync": "2024-01-15 10:25:00"
        },
        "students": [
            {
                "name": "محمد أحمد علي",
                "class": "الأول الابتدائي-أ",
                "national_id": "1234567890",
                "parent_phone": "966501234567"
            }
        ]
    }
}
```

### 4. السجلات والإحصائيات

#### GET `/logs.php`
جلب سجلات النشاط.

**المعاملات (Query Parameters):**
- `level`: مستوى السجل (INFO, WARNING, ERROR)
- `limit`: عدد السجلات (افتراضي: 50)
- `offset`: البداية (افتراضي: 0)
- `date_from`: من تاريخ (YYYY-MM-DD)
- `date_to`: إلى تاريخ (YYYY-MM-DD)

**مثال:**
```
GET /logs.php?level=ERROR&limit=20&date_from=2024-01-01
```

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم جلب السجلات بنجاح",
    "data": {
        "logs": [
            {
                "id": 123,
                "action_type": "COMPONENT_UPDATE",
                "component_name": "mainUI",
                "school_id": null,
                "details": "تم تحديث المكون بنجاح",
                "created_at": "2024-01-15 10:30:00"
            }
        ],
        "total_count": 1250,
        "current_page": 1,
        "per_page": 20
    }
}
```

#### GET `/stats.php`
جلب إحصائيات النظام.

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم جلب الإحصائيات بنجاح",
    "data": {
        "components": {
            "total": 5,
            "active": 5,
            "inactive": 0
        },
        "schools": {
            "total": 12,
            "active_today": 8,
            "total_students": 1847
        },
        "usage": {
            "api_calls_today": 245,
            "most_used_component": "dataModal",
            "peak_hour": "10:00"
        },
        "system": {
            "uptime": "5 days, 3 hours",
            "cache_hit_rate": "85%",
            "database_size": "2.5 MB"
        }
    }
}
```

## 🔒 رموز الحالة (Status Codes)

| الرمز | المعنى | الوصف |
|-------|--------|--------|
| 200 | OK | تم تنفيذ الطلب بنجاح |
| 400 | Bad Request | خطأ في بيانات الطلب |
| 401 | Unauthorized | غير مصرح بالوصول |
| 403 | Forbidden | ممنوع الوصول |
| 404 | Not Found | المورد غير موجود |
| 500 | Internal Server Error | خطأ في الخادم |

## 🚨 معالجة الأخطاء

### هيكل رسالة الخطأ
```json
{
    "success": false,
    "message": "وصف الخطأ",
    "error_code": 400,
    "details": {
        "field": "component_name",
        "issue": "مطلوب"
    },
    "timestamp": "2024-01-15 10:30:00",
    "version": "1.0.0"
}
```

### أخطاء شائعة

#### خطأ في البيانات المطلوبة
```json
{
    "success": false,
    "message": "بيانات مطلوبة مفقودة",
    "error_code": 400,
    "details": {
        "missing_fields": ["component_name", "component_type"]
    }
}
```

#### خطأ في قاعدة البيانات
```json
{
    "success": false,
    "message": "خطأ في قاعدة البيانات",
    "error_code": 500,
    "details": {
        "sql_error": "Duplicate entry 'mainUI' for key 'component_name'"
    }
}
```

## 🔧 أمثلة الاستخدام

### JavaScript (Fetch API)
```javascript
// جلب المكونات
async function fetchComponents() {
    try {
        const response = await fetch('http://localhost/Dynamic/api/components.php');
        const result = await response.json();
        
        if (result.success) {
            console.log('المكونات:', result.data);
        } else {
            console.error('خطأ:', result.message);
        }
    } catch (error) {
        console.error('خطأ في الشبكة:', error);
    }
}

// حفظ بيانات مدرسة
async function saveSchoolData(schoolData) {
    try {
        const response = await fetch('http://localhost/Dynamic/api/save-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: 'SCHOOL_DATA',
                data: schoolData,
                timestamp: Date.now(),
                source: 'kushoof_dynamic_extension'
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('تم الحفظ:', result.data);
        } else {
            console.error('فشل الحفظ:', result.message);
        }
    } catch (error) {
        console.error('خطأ في الشبكة:', error);
    }
}
```

### PHP (cURL)
```php
// جلب المكونات
function fetchComponents() {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/Dynamic/api/components.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $result = json_decode($response, true);
        return $result;
    } else {
        throw new Exception("HTTP Error: $httpCode");
    }
}

// حفظ مكون جديد
function saveComponent($componentData) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/Dynamic/api/update-component.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($componentData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $result = json_decode($response, true);
        return $result;
    } else {
        throw new Exception("HTTP Error: $httpCode");
    }
}
```

## 📊 حدود الاستخدام (Rate Limiting)

حالياً لا توجد حدود للاستخدام، لكن يُنصح بـ:
- عدم تجاوز 100 طلب في الدقيقة
- تجنب الطلبات المتكررة للبيانات نفسها
- استخدام التخزين المؤقت عند الإمكان

## 🔄 إصدارات API

### الإصدار الحالي: v1.0.0
- دعم كامل لإدارة المكونات
- حفظ وجلب بيانات المدارس
- نظام السجلات والإحصائيات
- دعم CORS للمتصفحات

### التحديثات المستقبلية
- نظام المصادقة (v1.1.0)
- دعم WebSockets للتحديثات الفورية (v1.2.0)
- API للملفات والوسائط (v1.3.0)

---

**📞 للدعم الفني أو الاستفسارات حول API، يرجى مراجعة الوثائق أو التواصل مع فريق التطوير.**
