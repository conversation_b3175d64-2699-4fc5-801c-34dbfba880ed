# 🚀 دليل تثبيت نظام الإضافة الديناميكية

## 📋 متطلبات النظام

### الخادم (Server Requirements)
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache/Nginx**: مع mod_rewrite مفعل
- **SSL Certificate**: مطلوب للإنتاج

### المتصفح (Browser Requirements)
- **Chrome**: 88+ (مطلوب للإضافة)
- **Firefox**: 85+ (دعم محدود)
- **Edge**: 88+ (دعم محدود)

### أدوات التطوير (Development Tools)
- **Git**: لإدارة الإصدارات
- **Composer**: لإدارة مكتبات PHP (اختياري)
- **Node.js**: للأدوات المساعدة (اختياري)

## 🔧 خطوات التثبيت

### 1. إعد<PERSON> قاعدة البيانات

#### أ) إنشاء قاعدة البيانات
```sql
-- الاتصال بـ MySQL
mysql -u root -p

-- إنشاء قاعدة البيانات
CREATE DATABASE kushoof_dynamic CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم (اختياري)
CREATE USER 'kushoof_user'@'localhost' IDENTIFIED BY 'password_here';
GRANT ALL PRIVILEGES ON kushoof_dynamic.* TO 'kushoof_user'@'localhost';
FLUSH PRIVILEGES;
```

#### ب) استيراد الهيكل
```bash
# من مجلد المشروع
mysql -u root -p kushoof_dynamic < api/database/schema.sql
```

### 2. إعداد الخادم

#### أ) رفع الملفات
```bash
# نسخ المشروع إلى مجلد الخادم
cp -r Dynamic/ /var/www/html/
# أو
cp -r Dynamic/ /xampp/htdocs/
```

#### ب) تعديل الصلاحيات
```bash
# إعطاء صلاحيات الكتابة للمجلدات المطلوبة
chmod 755 /var/www/html/Dynamic/
chmod 777 /var/www/html/Dynamic/api/logs/
chmod 777 /var/www/html/Dynamic/api/cache/
```

#### ج) إعداد Apache (إذا لزم الأمر)
```apache
# إضافة إلى .htaccess في مجلد api/
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# إعدادات CORS
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
```

### 3. تكوين API

#### أ) نسخ ملف الإعدادات
```bash
# إنشاء ملف الإعدادات المحلي
cp api/config.example.php api/config.local.php
```

#### ب) تعديل إعدادات قاعدة البيانات
```php
// في ملف api/config.local.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'kushoof_dynamic');
define('DB_USER', 'kushoof_user');
define('DB_PASS', 'your_password_here');

// تحديث مفتاح الأمان
define('API_SECRET_KEY', 'your_unique_secret_key_here');
```

### 4. تثبيت الإضافة

#### أ) فتح Chrome Extensions
```
1. افتح Chrome
2. اذهب إلى: chrome://extensions/
3. فعل "Developer mode" في الزاوية العلوية اليمنى
```

#### ب) تحميل الإضافة
```
1. اضغط "Load unpacked"
2. اختر مجلد: Dynamic/extension/
3. تأكد من ظهور الإضافة في القائمة
```

#### ج) تكوين الإضافة
```javascript
// في ملف extension/dynamic-loader.js
// تحديث رابط API إذا لزم الأمر
this.apiBase = 'http://your-domain.com/Dynamic/api';
```

### 5. اختبار التثبيت

#### أ) اختبار API
```bash
# اختبار الاتصال بقاعدة البيانات
curl http://localhost/Dynamic/api/config.php

# اختبار جلب المكونات
curl http://localhost/Dynamic/api/components.php
```

#### ب) اختبار لوحة التحكم
```
1. افتح: http://localhost/Dynamic/admin/
2. تحقق من ظهور لوحة التحكم
3. اختبر الاتصال من داخل اللوحة
```

#### ج) اختبار الإضافة
```
1. اذهب إلى: https://schools.madrasati.sa
2. تحقق من ظهور أزرار الإضافة
3. اختبر فتح الموديل والمصمم
```

## 🔒 إعدادات الأمان

### 1. تشفير البيانات
```php
// في config.php
define('ENCRYPTION_ENABLED', true);
define('API_SECRET_KEY', 'your-very-secure-key-here');
```

### 2. تقييد الوصول
```php
// تحديد المصادر المسموحة
define('ALLOWED_ORIGINS', [
    'https://schools.madrasati.sa',
    'https://your-domain.com'
]);
```

### 3. حماية قاعدة البيانات
```sql
-- إنشاء مستخدم محدود الصلاحيات
CREATE USER 'kushoof_readonly'@'localhost' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON kushoof_dynamic.* TO 'kushoof_readonly'@'localhost';
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```
الخطأ: "فشل في الاتصال بقاعدة البيانات"
الحل:
- تحقق من إعدادات قاعدة البيانات في config.php
- تأكد من تشغيل خدمة MySQL
- تحقق من صلاحيات المستخدم
```

#### 2. مشكلة CORS
```
الخطأ: "Access to fetch blocked by CORS policy"
الحل:
- تحقق من إعدادات CORS في config.php
- أضف النطاق إلى ALLOWED_ORIGINS
- تأكد من إعدادات Apache/Nginx
```

#### 3. الإضافة لا تظهر
```
الخطأ: الإضافة لا تظهر في مدرستي
الحل:
- تحقق من تفعيل الإضافة في Chrome
- تأكد من صحة رابط API
- افحص console للأخطاء
```

#### 4. لوحة التحكم لا تعمل
```
الخطأ: لوحة التحكم فارغة أو لا تحمل
الحل:
- تحقق من صلاحيات المجلدات
- تأكد من تشغيل خدمة الويب
- افحص ملفات السجلات
```

## 📊 مراقبة الأداء

### 1. تفعيل السجلات
```php
// في config.php
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO');
```

### 2. مراقبة قاعدة البيانات
```sql
-- فحص حالة الجداول
SHOW TABLE STATUS FROM kushoof_dynamic;

-- مراقبة الاستعلامات البطيئة
SHOW VARIABLES LIKE 'slow_query_log';
```

### 3. مراقبة الخادم
```bash
# مراقبة استخدام الذاكرة
free -h

# مراقبة استخدام القرص
df -h

# مراقبة العمليات
top
```

## 🔄 التحديث

### تحديث الإضافة
```bash
# نسخ احتياطية
cp -r Dynamic/ Dynamic_backup_$(date +%Y%m%d)/

# تحديث الملفات
git pull origin main

# تحديث قاعدة البيانات (إذا لزم الأمر)
mysql -u root -p kushoof_dynamic < api/database/updates.sql
```

### تحديث المكونات
```
1. افتح لوحة التحكم
2. اذهب إلى تبويب "المكونات"
3. اضغط "تحديث الكل"
4. تحقق من عمل الإضافة
```

## 📞 الدعم الفني

### معلومات مفيدة للدعم
```
- إصدار PHP: <?php echo PHP_VERSION; ?>
- إصدار MySQL: SELECT VERSION();
- إصدار الإضافة: 1.0.0
- نظام التشغيل: uname -a
```

### ملفات السجلات
```
- سجلات API: api/logs/api.log
- سجلات Apache: /var/log/apache2/error.log
- سجلات MySQL: /var/log/mysql/error.log
```

## ✅ قائمة التحقق النهائية

- [ ] قاعدة البيانات تعمل بشكل صحيح
- [ ] API يستجيب للطلبات
- [ ] لوحة التحكم تحمل بدون أخطاء
- [ ] الإضافة تظهر في مدرستي
- [ ] جميع المكونات تعمل
- [ ] السجلات تُسجل بشكل صحيح
- [ ] إعدادات الأمان مفعلة
- [ ] النسخ الاحتياطية جاهزة

---

**🎉 تهانينا! تم تثبيت نظام الإضافة الديناميكية بنجاح!**

للمساعدة أو الاستفسارات، يرجى مراجعة الوثائق أو التواصل مع فريق الدعم.
