-- 🗄️ Kushoof Dynamic Extension Database Schema
-- هيكل قاعدة البيانات للإضافة الديناميكية

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS kushoof_dynamic 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE kushoof_dynamic;

-- جدول مكونات الإضافة
CREATE TABLE IF NOT EXISTS extension_components (
    id INT AUTO_INCREMENT PRIMARY KEY,
    component_name VARCHAR(100) NOT NULL UNIQUE,
    component_type ENUM('ui', 'modal', 'designer', 'function', 'style', 'script') NOT NULL,
    html_code TEXT,
    css_code TEXT,
    js_code TEXT,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0.0',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    
    INDEX idx_component_type (component_type),
    INDEX idx_is_active (is_active),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات الإضافة
CREATE TABLE IF NOT EXISTS extension_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type ENUM('string', 'number', 'boolean', 'json', 'array') DEFAULT 'string',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بيانات المدارس
CREATE TABLE IF NOT EXISTS school_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    school_id VARCHAR(32) NOT NULL,
    school_name VARCHAR(255),
    manager_name VARCHAR(255),
    students_count INT DEFAULT 0,
    data_json LONGTEXT,
    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_school (school_id),
    INDEX idx_school_id (school_id),
    INDEX idx_last_sync (last_sync)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجلات النشاط
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    action_type VARCHAR(50) NOT NULL,
    component_name VARCHAR(100),
    school_id VARCHAR(32),
    user_agent TEXT,
    ip_address VARCHAR(45),
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_action_type (action_type),
    INDEX idx_component_name (component_name),
    INDEX idx_school_id (school_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إحصائيات الاستخدام
CREATE TABLE IF NOT EXISTS usage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date_recorded DATE NOT NULL,
    component_name VARCHAR(100),
    usage_count INT DEFAULT 1,
    unique_schools INT DEFAULT 0,
    total_students INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_date_component (date_recorded, component_name),
    INDEX idx_date_recorded (date_recorded),
    INDEX idx_component_name (component_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج المكونات الافتراضية
INSERT INTO extension_components (component_name, component_type, html_code, css_code, js_code, description) VALUES

-- الواجهة الرئيسية
('mainUI', 'ui', 
'<div class="kushoof-dynamic-ui">
    <button id="kushoof-show-data-btn" class="kushoof-btn">
        📊 عرض البيانات
    </button>
    <button id="kushoof-designer-btn" class="kushoof-btn">
        🎨 مصمم الكشوف
    </button>
    <button id="kushoof-collect-btn" class="kushoof-btn">
        📥 جمع البيانات
    </button>
</div>',
'.kushoof-dynamic-ui {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    font-family: Arial, sans-serif;
}

.kushoof-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    margin: 5px;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
}

.kushoof-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}',
'// الواجهة الرئيسية - يتم ربط الأحداث تلقائياً',
'الواجهة الرئيسية للإضافة الديناميكية'),

-- موديل عرض البيانات
('dataModal', 'modal',
'<div class="kushoof-modal">
    <div class="kushoof-modal-content">
        <div class="kushoof-modal-header">
            <h2>📊 عرض البيانات الديناميكية</h2>
            <button class="kushoof-close-btn" onclick="this.closest(\'.kushoof-modal\').remove()">✕</button>
        </div>
        <div class="kushoof-modal-body">
            <div id="data-content">
                <h3>🎉 مرحباً بك في الإضافة الديناميكية!</h3>
                <p>هذا المحتوى يتم جلبه من قاعدة البيانات ويمكن تحديثه دون إعادة تثبيت الإضافة.</p>
                <div class="features-grid">
                    <div class="feature-card">
                        <h4>✅ تحديث فوري</h4>
                        <p>تحديث المكونات دون إعادة تثبيت</p>
                    </div>
                    <div class="feature-card">
                        <h4>⚙️ إدارة مركزية</h4>
                        <p>لوحة تحكم شاملة</p>
                    </div>
                    <div class="feature-card">
                        <h4>🎯 تخصيص متقدم</h4>
                        <p>تخصيص حسب المدرسة</p>
                    </div>
                    <div class="feature-card">
                        <h4>🛡️ أمان محسن</h4>
                        <p>تشفير وحماية البيانات</p>
                    </div>
                </div>
                <button onclick="alert(\'تم النقر من الكود الديناميكي!\')" class="test-btn">
                    🚀 اختبار التفاعل
                </button>
            </div>
        </div>
    </div>
</div>',
'.kushoof-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kushoof-modal-content {
    background: white;
    width: 90%;
    max-width: 800px;
    height: 85%;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
}

.kushoof-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.kushoof-modal-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.kushoof-close-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.feature-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.test-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    margin-top: 20px;
}',
'function createModal() {
    console.log("🎨 إنشاء موديل من قاعدة البيانات");
    
    const modal = document.createElement("div");
    modal.innerHTML = `[HTML من قاعدة البيانات]`;
    document.body.appendChild(modal);
}',
'موديل عرض البيانات مع التصميم المتقدم');

-- إدراج الإعدادات الافتراضية
INSERT INTO extension_config (config_key, config_value, config_type, description) VALUES
('theme_color', '#667eea', 'string', 'اللون الأساسي للإضافة'),
('api_version', '1.0.0', 'string', 'إصدار API'),
('cache_duration', '3600', 'number', 'مدة التخزين المؤقت بالثواني'),
('debug_mode', 'false', 'boolean', 'وضع التطوير'),
('allowed_schools', '[]', 'json', 'قائمة المدارس المسموحة'),
('features_enabled', '["modal", "designer", "export"]', 'json', 'المميزات المفعلة'),
('auto_update', 'true', 'boolean', 'التحديث التلقائي للمكونات'),
('encryption_enabled', 'true', 'boolean', 'تفعيل التشفير'),
('log_level', 'INFO', 'string', 'مستوى السجلات'),
('max_students_per_school', '1000', 'number', 'الحد الأقصى للطلاب لكل مدرسة');

-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- CREATE USER 'kushoof_user'@'localhost' IDENTIFIED BY 'kushoof_password_2024';
-- GRANT ALL PRIVILEGES ON kushoof_dynamic.* TO 'kushoof_user'@'localhost';
-- FLUSH PRIVILEGES;

-- إنشاء فهارس إضافية للأداء
CREATE INDEX idx_components_active_type ON extension_components (is_active, component_type);
CREATE INDEX idx_school_data_sync ON school_data (last_sync, school_id);
CREATE INDEX idx_logs_date_action ON activity_logs (created_at, action_type);

-- إضافة قيود إضافية
ALTER TABLE extension_components 
ADD CONSTRAINT chk_component_name_length CHECK (LENGTH(component_name) >= 3);

ALTER TABLE school_data 
ADD CONSTRAINT chk_school_id_format CHECK (LENGTH(school_id) = 32);

-- عرض ملخص قاعدة البيانات
SELECT 
    'extension_components' as table_name,
    COUNT(*) as record_count,
    'مكونات الإضافة' as description
FROM extension_components
UNION ALL
SELECT 
    'extension_config' as table_name,
    COUNT(*) as record_count,
    'إعدادات الإضافة' as description
FROM extension_config
UNION ALL
SELECT 
    'school_data' as table_name,
    COUNT(*) as record_count,
    'بيانات المدارس' as description
FROM school_data
UNION ALL
SELECT 
    'activity_logs' as table_name,
    COUNT(*) as record_count,
    'سجلات النشاط' as description
FROM activity_logs
UNION ALL
SELECT 
    'usage_stats' as table_name,
    COUNT(*) as record_count,
    'إحصائيات الاستخدام' as description
FROM usage_stats;
