<?php
/**
 * 🔧 Kushoof Dynamic Extension API Configuration
 * ملف إعدادات API للإضافة الديناميكية
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'kushoofa_db');
define('DB_USER', 'kushoofa_db');
define('DB_PASS', 'Ali123456@@');
define('DB_CHARSET', 'utf8mb4');

// إعدادات API
define('API_VERSION', '1.0.0');
define('API_BASE_URL', 'http://localhost/Dynamic/api');
define('ADMIN_BASE_URL', 'http://localhost/Dynamic/admin');

// إعدادات الأمان
define('API_SECRET_KEY', 'kushoof_dynamic_secret_2024');
define('ENCRYPTION_METHOD', 'AES-256-CBC');
define('HASH_ALGORITHM', 'sha256');

// إعدادات CORS
define('ALLOWED_ORIGINS', [
    'https://schools.madrasati.sa',
    'chrome-extension://*',
    'http://localhost'
]);

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // ساعة واحدة

// إعدادات السجلات
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', __DIR__ . '/logs/api.log');

/**
 * فئة قاعدة البيانات
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]);
            
            Logger::log('تم الاتصال بقاعدة البيانات بنجاح', 'INFO');
            
        } catch (PDOException $e) {
            Logger::log('فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage(), 'ERROR');
            throw new Exception('فشل في الاتصال بقاعدة البيانات');
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
}

/**
 * فئة السجلات
 */
class Logger {
    public static function log($message, $level = 'INFO') {
        if (!LOG_ENABLED) return;
        
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        // إنشاء مجلد السجلات إذا لم يكن موجوداً
        $logDir = dirname(LOG_FILE);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents(LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
    }
}

/**
 * فئة الاستجابة
 */
class ApiResponse {
    public static function success($data = null, $message = 'تم بنجاح') {
        self::sendResponse([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => API_VERSION
        ]);
    }
    
    public static function error($message = 'حدث خطأ', $code = 400, $details = null) {
        http_response_code($code);
        self::sendResponse([
            'success' => false,
            'message' => $message,
            'error_code' => $code,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => API_VERSION
        ]);
    }
    
    private static function sendResponse($data) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
}

/**
 * فئة الأمان
 */
class Security {
    public static function validateOrigin() {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        foreach (ALLOWED_ORIGINS as $allowedOrigin) {
            if (fnmatch($allowedOrigin, $origin)) {
                header("Access-Control-Allow-Origin: $origin");
                return true;
            }
        }
        
        return false;
    }
    
    public static function encrypt($data) {
        $key = hash('sha256', API_SECRET_KEY);
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, ENCRYPTION_METHOD, $key, 0, $iv);
        return base64_encode($encrypted . '::' . $iv);
    }
    
    public static function decrypt($data) {
        $key = hash('sha256', API_SECRET_KEY);
        list($encrypted_data, $iv) = explode('::', base64_decode($data), 2);
        return openssl_decrypt($encrypted_data, ENCRYPTION_METHOD, $key, 0, $iv);
    }
    
    public static function generateToken() {
        return bin2hex(random_bytes(32));
    }
    
    public static function validateToken($token) {
        // تحقق من صحة الرمز المميز
        return !empty($token) && strlen($token) === 64;
    }
}

/**
 * فئة التخزين المؤقت
 */
class Cache {
    private static $cacheDir = __DIR__ . '/cache/';
    
    public static function get($key) {
        if (!CACHE_ENABLED) return null;
        
        $filename = self::$cacheDir . md5($key) . '.cache';
        
        if (!file_exists($filename)) {
            return null;
        }
        
        $data = unserialize(file_get_contents($filename));
        
        if ($data['expires'] < time()) {
            unlink($filename);
            return null;
        }
        
        return $data['value'];
    }
    
    public static function set($key, $value, $duration = CACHE_DURATION) {
        if (!CACHE_ENABLED) return;
        
        if (!is_dir(self::$cacheDir)) {
            mkdir(self::$cacheDir, 0755, true);
        }
        
        $filename = self::$cacheDir . md5($key) . '.cache';
        $data = [
            'value' => $value,
            'expires' => time() + $duration
        ];
        
        file_put_contents($filename, serialize($data));
    }
    
    public static function delete($key) {
        $filename = self::$cacheDir . md5($key) . '.cache';
        if (file_exists($filename)) {
            unlink($filename);
        }
    }
    
    public static function clear() {
        if (is_dir(self::$cacheDir)) {
            $files = glob(self::$cacheDir . '*.cache');
            foreach ($files as $file) {
                unlink($file);
            }
        }
    }
}

// إعداد CORS
if (isset($_SERVER['HTTP_ORIGIN'])) {
    Security::validateOrigin();
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تسجيل الطلب
Logger::log("طلب API: {$_SERVER['REQUEST_METHOD']} {$_SERVER['REQUEST_URI']}", 'INFO');
?>
