<?php
/**
 * 📦 Kushoof Dynamic Extension Components API
 * API لجلب مكونات الإضافة من قاعدة البيانات
 */

require_once 'config.php';

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        ApiResponse::error('طريقة طلب غير مدعومة', 405);
    }
    
    // جلب المكونات من التخزين المؤقت أولاً
    $cacheKey = 'components_all';
    $cachedComponents = Cache::get($cacheKey);
    
    if ($cachedComponents !== null) {
        Logger::log('تم جلب المكونات من التخزين المؤقت', 'INFO');
        ApiResponse::success($cachedComponents, 'تم جلب المكونات من التخزين المؤقت');
    }
    
    // الاتصال بقاعدة البيانات
    $db = Database::getInstance()->getConnection();
    
    // جلب المكونات النشطة
    $stmt = $db->prepare("
        SELECT 
            component_name,
            component_type,
            html_code,
            css_code,
            js_code,
            version,
            updated_at
        FROM extension_components 
        WHERE is_active = 1 
        ORDER BY component_type, component_name
    ");
    
    $stmt->execute();
    $components = $stmt->fetchAll();
    
    if (empty($components)) {
        // إرجاع مكونات افتراضية إذا لم توجد في قاعدة البيانات
        $defaultComponents = getDefaultComponents();
        Cache::set($cacheKey, $defaultComponents, 300); // 5 دقائق للمكونات الافتراضية
        ApiResponse::success($defaultComponents, 'تم إرجاع المكونات الافتراضية');
    }
    
    // تنظيم المكونات
    $organizedComponents = [
        'styles' => '',
        'templates' => [],
        'scripts' => '',
        'metadata' => [
            'total_components' => count($components),
            'last_update' => null,
            'version' => API_VERSION
        ]
    ];
    
    $lastUpdate = null;
    
    foreach ($components as $component) {
        // تجميع CSS
        if (!empty($component['css_code'])) {
            $organizedComponents['styles'] .= "\n/* {$component['component_name']} */\n";
            $organizedComponents['styles'] .= $component['css_code'] . "\n";
        }
        
        // تجميع HTML Templates
        if (!empty($component['html_code'])) {
            $organizedComponents['templates'][$component['component_name']] = $component['html_code'];
        }
        
        // تجميع JavaScript
        if (!empty($component['js_code'])) {
            $organizedComponents['scripts'] .= "\n/* {$component['component_name']} */\n";
            $organizedComponents['scripts'] .= $component['js_code'] . "\n";
        }
        
        // تتبع آخر تحديث
        if ($lastUpdate === null || $component['updated_at'] > $lastUpdate) {
            $lastUpdate = $component['updated_at'];
        }
    }
    
    $organizedComponents['metadata']['last_update'] = $lastUpdate;
    
    // إضافة الواجهة الرئيسية إذا لم تكن موجودة
    if (!isset($organizedComponents['templates']['mainUI'])) {
        $organizedComponents['templates']['mainUI'] = getDefaultMainUI();
    }
    
    // حفظ في التخزين المؤقت
    Cache::set($cacheKey, $organizedComponents);
    
    Logger::log("تم جلب {$organizedComponents['metadata']['total_components']} مكون بنجاح", 'INFO');
    ApiResponse::success($organizedComponents, 'تم جلب المكونات بنجاح');
    
} catch (Exception $e) {
    Logger::log('خطأ في جلب المكونات: ' . $e->getMessage(), 'ERROR');
    ApiResponse::error('فشل في جلب المكونات: ' . $e->getMessage(), 500);
}

/**
 * الحصول على مكونات افتراضية
 */
function getDefaultComponents() {
    return [
        'styles' => getDefaultCSS(),
        'templates' => [
            'mainUI' => getDefaultMainUI(),
            'modal' => getDefaultModal(),
            'designer' => getDefaultDesigner()
        ],
        'scripts' => getDefaultJavaScript(),
        'metadata' => [
            'total_components' => 3,
            'last_update' => date('Y-m-d H:i:s'),
            'version' => API_VERSION,
            'source' => 'default'
        ]
    ];
}

/**
 * CSS افتراضي
 */
function getDefaultCSS() {
    return '
/* Kushoof Dynamic Extension Default Styles */
.kushoof-dynamic-ui {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    font-family: Arial, sans-serif;
}

.kushoof-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    margin: 5px;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
}

.kushoof-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.kushoof-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kushoof-modal-content {
    background: white;
    width: 90%;
    height: 85%;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.kushoof-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.kushoof-close-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
}
';
}

/**
 * الواجهة الرئيسية الافتراضية
 */
function getDefaultMainUI() {
    return '
<div class="kushoof-dynamic-ui">
    <button id="kushoof-show-data-btn" class="kushoof-btn">
        📊 عرض البيانات
    </button>
    <button id="kushoof-designer-btn" class="kushoof-btn">
        🎨 مصمم الكشوف
    </button>
    <button id="kushoof-collect-btn" class="kushoof-btn">
        📥 جمع البيانات
    </button>
</div>
';
}

/**
 * موديل افتراضي
 */
function getDefaultModal() {
    return '
<div class="kushoof-modal">
    <div class="kushoof-modal-content">
        <div class="kushoof-modal-header">
            <h2>📊 عرض البيانات</h2>
            <button class="kushoof-close-btn" onclick="this.closest(\'.kushoof-modal\').remove()">✕</button>
        </div>
        <div style="padding: 20px;">
            <p>مرحباً بك في الإضافة الديناميكية!</p>
            <p>هذا المحتوى يتم جلبه من قاعدة البيانات.</p>
        </div>
    </div>
</div>
';
}

/**
 * مصمم افتراضي
 */
function getDefaultDesigner() {
    return '
<div class="kushoof-modal">
    <div class="kushoof-modal-content">
        <div class="kushoof-modal-header">
            <h2>🎨 مصمم الكشوف</h2>
            <button class="kushoof-close-btn" onclick="this.closest(\'.kushoof-modal\').remove()">✕</button>
        </div>
        <div style="padding: 20px;">
            <p>مصمم الكشوف الديناميكي</p>
            <p>يمكن تخصيص هذا المحتوى من لوحة التحكم.</p>
        </div>
    </div>
</div>
';
}

/**
 * JavaScript افتراضي
 */
function getDefaultJavaScript() {
    return '
// Kushoof Dynamic Extension Default Functions

function createModal() {
    console.log("🎨 إنشاء موديل من قاعدة البيانات");
    
    const modal = document.createElement("div");
    modal.innerHTML = `
        <div class="kushoof-modal">
            <div class="kushoof-modal-content">
                <div class="kushoof-modal-header">
                    <h2>📊 عرض البيانات الديناميكية</h2>
                    <button class="kushoof-close-btn" onclick="this.closest(\'.kushoof-modal\').remove()">✕</button>
                </div>
                <div style="padding: 20px;">
                    <h3>🎉 مرحباً بك في الإضافة الديناميكية!</h3>
                    <p>هذا المحتوى يتم جلبه من قاعدة البيانات ويمكن تحديثه دون إعادة تثبيت الإضافة.</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <h4>المميزات:</h4>
                        <ul>
                            <li>✅ تحديث فوري للمكونات</li>
                            <li>✅ إدارة مركزية من لوحة التحكم</li>
                            <li>✅ تخصيص حسب المدرسة</li>
                            <li>✅ أمان محسن</li>
                        </ul>
                    </div>
                    <button onclick="alert(\'تم النقر من الكود الديناميكي!\')" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        🚀 اختبار التفاعل
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

function createDesigner() {
    console.log("🎨 إنشاء مصمم الكشوف من قاعدة البيانات");
    
    const designer = document.createElement("div");
    designer.innerHTML = `
        <div class="kushoof-modal">
            <div class="kushoof-modal-content">
                <div class="kushoof-modal-header">
                    <h2>🎨 مصمم الكشوف الديناميكي</h2>
                    <button class="kushoof-close-btn" onclick="this.closest(\'.kushoof-modal\').remove()">✕</button>
                </div>
                <div style="padding: 20px;">
                    <h3>🎯 مصمم الكشوف المتقدم</h3>
                    <p>يمكن تطوير وتحديث هذا المصمم من لوحة التحكم دون الحاجة لإعادة تثبيت الإضافة.</p>
                    <div style="display: flex; gap: 20px; margin: 20px 0;">
                        <div style="flex: 1; background: #e3f2fd; padding: 15px; border-radius: 8px;">
                            <h4>📋 أنواع الكشوف</h4>
                            <ul>
                                <li>كشف حضور وغياب</li>
                                <li>كشف درجات</li>
                                <li>كشف أرقام الهواتف</li>
                                <li>قائمة الطلاب</li>
                            </ul>
                        </div>
                        <div style="flex: 1; background: #f3e5f5; padding: 15px; border-radius: 8px;">
                            <h4>⚙️ الإعدادات</h4>
                            <ul>
                                <li>اختيار الحقول</li>
                                <li>تصفية الفصول</li>
                                <li>تخصيص التصميم</li>
                                <li>تصدير وطباعة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(designer);
}

function collectData() {
    console.log("📥 جمع البيانات من قاعدة البيانات");
    
    alert("🚀 بدء عملية جمع البيانات الديناميكية!\\n\\nهذه الوظيفة يتم تحديثها من قاعدة البيانات.");
}

function exportData() {
    console.log("📤 تصدير البيانات");
    
    alert("📊 تصدير البيانات\\n\\nيتم تطوير هذه الوظيفة ديناميكياً من لوحة التحكم.");
}
';
}
?>
