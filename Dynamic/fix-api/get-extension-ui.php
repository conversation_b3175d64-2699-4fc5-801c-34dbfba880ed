<?php
/**
 * 🎨 API لجلب أكواد الإضافة (HTML, CSS, JS)
 * يحتوي على جميع أكواد الواجهة والوظائف
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // CSS للإضافة
    $cssCode = "
        .kushoof-real-ui {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .kushoof-real-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: block;
            width: 200px;
            text-align: center;
        }
        
        .kushoof-real-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .kushoof-real-btn.export {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .kushoof-real-btn.export:hover {
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .kushoof-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .kushoof-modal-content {
            background: white;
            width: 95%;
            height: 90%;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        }
        
        .kushoof-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .kushoof-modal-close {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
        }
        
        .kushoof-modal-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .kushoof-success-notice {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            z-index: 999998;
            font-family: Arial, sans-serif;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .kushoof-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .kushoof-stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .kushoof-data-container {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .kushoof-data-pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    ";
    
    // HTML للإضافة
    $htmlCode = '
        <div class="kushoof-real-ui">
            <button class="kushoof-real-btn" onclick="showRealStudentsData()">
                📊 عرض البيانات الحقيقية
            </button>
            <button class="kushoof-real-btn export" onclick="exportRealData()">
                📤 تصدير البيانات
            </button>
        </div>

        <div class="kushoof-success-notice">
            ✅ جلب الأكواد من الموقع<br>
            <small>HTML + CSS + JS من قاعدة البيانات</small>
        </div>
    ';
    
    // JavaScript للإضافة
    $jsCode = '
        // متغير عام للبيانات
        window.kushoofRealData = window.kushoofRealData || null;
        window.kushoofApiUrl = window.kushoofApiUrl || "";

        // دالة عرض البيانات الحقيقية
        window.showRealStudentsData = function() {';
            if (!window.kushoofRealData || window.kushoofRealData.length === 0) {
                alert('❌ لا توجد بيانات حقيقية متاحة');
                return;
            }
            
            console.log('📊 عرض البيانات الحقيقية من الموقع');
            
            const modal = document.createElement('div');
            modal.className = 'kushoof-modal';
            
            modal.innerHTML = \`
                <div class='kushoof-modal-content'>
                    <div class='kushoof-modal-header'>
                        <div>
                            <h1 style='margin: 0; font-size: 24px;'>📊 البيانات الحقيقية من kushoofapp.com</h1>
                            <p style='margin: 5px 0 0 0; opacity: 0.9;'>
                                تم جلب \${window.kushoofRealData.length} طالب • \${new Date().toLocaleString('ar-SA')}
                            </p>
                        </div>
                        <button class='kushoof-modal-close' onclick='this.closest(\".kushoof-modal\").remove()'>✕</button>
                    </div>
                    
                    <div class='kushoof-modal-body'>
                        <div style='background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;'>
                            <h3 style='margin: 0 0 10px 0; color: #155724;'>✅ بيانات حقيقية من قاعدة البيانات</h3>
                            <p style='margin: 0; color: #155724;'>
                                تم جلب البيانات مباشرة من قاعدة البيانات في kushoofapp.com
                            </p>
                            <p style='margin: 5px 0 0 0; color: #155724; font-size: 12px;'>
                                API: \${window.kushoofApiUrl}
                            </p>
                        </div>
                        
                        <div class='kushoof-stats-grid'>
                            <div class='kushoof-stat-card'>
                                <h4 style='margin: 0; color: #667eea;'>📊 إجمالي الطلاب</h4>
                                <p style='margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #333;'>
                                    \${window.kushoofRealData.length}
                                </p>
                            </div>
                            <div class='kushoof-stat-card'>
                                <h4 style='margin: 0; color: #28a745;'>🔗 المصدر</h4>
                                <p style='margin: 5px 0 0 0; font-size: 12px; color: #333;'>
                                    قاعدة البيانات الحقيقية
                                </p>
                            </div>
                            <div class='kushoof-stat-card'>
                                <h4 style='margin: 0; color: #764ba2;'>⏰ وقت الجلب</h4>
                                <p style='margin: 5px 0 0 0; font-size: 12px; color: #333;'>
                                    \${new Date().toLocaleTimeString('ar-SA')}
                                </p>
                            </div>
                        </div>
                        
                        <h3>📋 البيانات الحقيقية من قاعدة البيانات:</h3>
                        <div class='kushoof-data-container'>
                            <pre class='kushoof-data-pre'>\${JSON.stringify(window.kushoofRealData, null, 2)}</pre>
                        </div>
                        
                        <div style='margin-top: 20px;'>
                            <h3>🔍 تحليل البيانات:</h3>
                            <div style='background: #e3f2fd; padding: 15px; border-radius: 8px;'>
                                <p><strong>عدد الطلاب:</strong> \${window.kushoofRealData.length}</p>
                                <p><strong>الحقول المتاحة:</strong> \${window.kushoofRealData.length > 0 ? Object.keys(window.kushoofRealData[0]).length : 0}</p>
                                <p><strong>أول طالب:</strong> \${window.kushoofRealData.length > 0 ? window.kushoofRealData[0].name || 'غير محدد' : 'لا يوجد'}</p>
                                <p><strong>آخر طالب:</strong> \${window.kushoofRealData.length > 0 ? window.kushoofRealData[window.kushoofRealData.length - 1].name || 'غير محدد' : 'لا يوجد'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            \`;
            
            document.body.appendChild(modal);
        };

        // دالة تصدير البيانات
        window.exportRealData = function() {
            if (!window.kushoofRealData || window.kushoofRealData.length === 0) {
                alert('❌ لا توجد بيانات حقيقية للتصدير');
                return;
            }
            
            console.log('📤 تصدير البيانات الحقيقية من الموقع');
            
            const exportData = {
                exportDate: new Date().toISOString(),
                source: 'قاعدة البيانات الحقيقية - kushoofapp.com',
                api: window.kushoofApiUrl,
                totalStudents: window.kushoofRealData.length,
                realDatabaseData: window.kushoofRealData,
                metadata: {
                    exportedBy: 'Kushoof Real Data Extension',
                    version: '2.0.0',
                    exportTime: new Date().toLocaleString('ar-SA'),
                    note: 'هذه بيانات حقيقية من قاعدة البيانات - تم جلب الأكواد من الموقع'
                }
            };
            
            const jsonString = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = \`kushoof-real-data-\${new Date().toISOString().split('T')[0]}.json\`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert(\`📊 تم تصدير البيانات الحقيقية بنجاح!\\n\\nعدد الطلاب: \${window.kushoofRealData.length}\\nالمصدر: kushoofapp.com\\nالملف: kushoof-real-data.json\`);
        };

        // تأكيد تحميل الدوال
        console.log('✅ تم تحميل جميع أكواد الإضافة من الموقع');
        console.log('✅ تم تعريف دالة showRealStudentsData:', typeof window.showRealStudentsData);
        console.log('✅ تم تعريف دالة exportRealData:', typeof window.exportRealData);
    ";
    
    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'message' => 'تم جلب أكواد الإضافة من الموقع',
        'ui' => [
            'css' => $cssCode,
            'html' => $htmlCode,
            'javascript' => $jsCode
        ],
        'metadata' => [
            'version' => '2.0.0',
            'last_update' => date('Y-m-d H:i:s'),
            'source' => 'kushoofapp.com',
            'components' => ['CSS', 'HTML', 'JavaScript']
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في جلب أكواد الإضافة: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
