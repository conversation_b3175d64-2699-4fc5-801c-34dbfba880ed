# 📤 دليل رفع ملف get-extension.php

## 🎯 المطلوب
رفع ملف `get-extension.php` إلى موقع kushoofapp.com ليعمل كـ API للإضافة.

## 📍 المكان الصحيح للرفع

### **المسار المطلوب:**
```
https://kushoofapp.com/js/api/get-extension.php
```

### **مجلد الرفع في cPanel:**
```
public_html/js/api/get-extension.php
```

## 🔧 خطوات الرفع

### **1. الدخول إلى cPanel:**
```
- اذهب إلى لوحة تحكم kushoofapp.com
- أو استخدم FTP client مثل FileZilla
```

### **2. التنقل إلى المجلد:**
```
public_html/
└── js/
    └── api/
        ├── receive-data.php  (موجود)
        └── get-extension.php (ارفع هنا)
```

### **3. رفع الملف:**
```
- ارفع ملف get-extension.php من مجلد Dynamic/upload-to-kushoofapp/
- ضعه في: public_html/js/api/
- تأكد من أن المسار النهائي: public_html/js/api/get-extension.php
```

### **4. تعيين الصلاحيات:**
```
chmod 644 get-extension.php
```

## 🧪 اختبار الملف

### **1. اختبار مباشر:**
```
افتح في المتصفح: https://kushoofapp.com/js/api/get-extension.php
```

### **2. النتيجة المتوقعة:**
```json
{
  "success": true,
  "message": "تم جلب مكونات الإضافة من kushoofapp.com",
  "styles": "/* CSS code */",
  "templates": {
    "mainUI": "/* HTML code */"
  },
  "scripts": "/* JavaScript code */",
  "metadata": {
    "total_students": 10,
    "last_update": "2024-12-19 15:30:00",
    "source": "kushoofapp.com",
    "version": "1.0.0"
  }
}
```

## 🎊 بعد الرفع

### **في الإضافة:**
```
🚀 [Kushoof] بدء تحميل البيانات الحقيقية من kushoofapp.com
📡 [Kushoof] محاولة جلب البيانات من APIs متعددة...
🔄 [Kushoof] جرب API 1: https://kushoofapp.com/js/api/receive-data.php
⚠️ [Kushoof] API 1 فشل: HTTP 500
🔄 [Kushoof] جرب API 2: https://kushoofapp.com/js/api/get-extension.php
✅ [Kushoof] تم جلب 10 عنصر من https://kushoofapp.com/js/api/get-extension.php
🎨 [Kushoof] تحميل الواجهة من الموقع...
✅ [Kushoof] تم تحميل الواجهة بنجاح
✅ [Kushoof] تم تحميل الإضافة بنجاح
```

### **الإشعار:**
```
✅ متصل بـ kushoofapp.com
10 عنصر من البيانات
```

## 🔄 الحالة الحالية (بدون رفع الملف)

### **ما يحدث الآن:**
```
🔄 [Kushoof] جرب API 1: https://kushoofapp.com/js/api/receive-data.php
⚠️ [Kushoof] API 1 فشل: HTTP 500
🔄 [Kushoof] جرب API 2: https://kushoofapp.com/js/api/get-extension.php
⚠️ [Kushoof] API 2 فشل: HTTP 404 (لأن الملف غير موجود)
🔄 [Kushoof] جرب API 3: https://jsonplaceholder.typicode.com/users
✅ [Kushoof] تم جلب 10 عنصر من API احتياطي
```

### **الإشعار الحالي:**
```
✅ متصل بـ API احتياطي
10 عنصر من البيانات
```

## 📞 استكشاف الأخطاء

### **إذا لم يعمل الملف بعد الرفع:**

1. **تحقق من المسار:**
   ```
   https://kushoofapp.com/js/api/get-extension.php
   ```

2. **تحقق من الصلاحيات:**
   ```
   chmod 644 get-extension.php
   ```

3. **تحقق من error logs:**
   ```
   cPanel → Error Logs
   ```

4. **تحقق من syntax PHP:**
   ```
   php -l get-extension.php
   ```

## 🎯 الخلاصة

- **الملف:** `Dynamic/upload-to-kushoofapp/get-extension.php`
- **المكان:** `public_html/js/api/get-extension.php`
- **الرابط:** `https://kushoofapp.com/js/api/get-extension.php`
- **النتيجة:** الإضافة ستجلب البيانات من kushoofapp.com بدلاً من API احتياطي

**🚀 بعد رفع الملف، الإضافة ستعمل مع البيانات الحقيقية من kushoofapp.com!**
