/**
 * 🎛️ Kushoof Dynamic Extension Admin Panel
 * سكريبت لوحة التحكم الرئيسية
 */

class AdminPanel {
    constructor() {
        this.apiBase = '../api';
        this.currentTab = 'dashboard';
        this.components = [];
        this.config = {};
        
        console.log('🎛️ [Admin] تهيئة لوحة التحكم');
        this.init();
    }
    
    /**
     * تهيئة لوحة التحكم
     */
    async init() {
        try {
            // ربط الأحداث
            this.bindEvents();
            
            // تحميل البيانات الأولية
            await this.loadDashboardData();
            
            // تفعيل التبويب الافتراضي
            this.switchTab('dashboard');
            
            console.log('✅ [Admin] تم تحميل لوحة التحكم بنجاح');
            
        } catch (error) {
            console.error('❌ [Admin] فشل في تهيئة لوحة التحكم:', error);
            this.showToast('فشل في تحميل لوحة التحكم', 'error');
        }
    }
    
    /**
     * ربط الأحداث
     */
    bindEvents() {
        // أحداث التنقل
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = link.getAttribute('data-tab');
                this.switchTab(tab);
            });
        });
        
        // أحداث الأزرار الرئيسية
        document.getElementById('refresh-all-btn')?.addEventListener('click', () => this.refreshAll());
        document.getElementById('test-connection-btn')?.addEventListener('click', () => this.testConnection());
        
        // أحداث المكونات
        document.getElementById('add-component-btn')?.addEventListener('click', () => this.showComponentModal());
        
        // أحداث الموديل
        document.querySelector('.modal-close')?.addEventListener('click', () => this.closeModal());
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal();
            }
        });
        
        // أحداث محرر الكود
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const editor = e.target.getAttribute('data-editor');
                this.switchCodeEditor(editor);
            });
        });
        
        console.log('🔗 [Admin] تم ربط الأحداث');
    }
    
    /**
     * تبديل التبويب
     */
    switchTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // إزالة التفعيل من جميع الروابط
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // تفعيل التبويب المطلوب
        const targetTab = document.getElementById(`${tabName}-tab`);
        const targetLink = document.querySelector(`[data-tab="${tabName}"]`);
        
        if (targetTab && targetLink) {
            targetTab.classList.add('active');
            targetLink.classList.add('active');
            this.currentTab = tabName;
            
            // تحميل بيانات التبويب
            this.loadTabData(tabName);
        }
        
        console.log(`📋 [Admin] تم التبديل إلى تبويب: ${tabName}`);
    }
    
    /**
     * تحميل بيانات التبويب
     */
    async loadTabData(tabName) {
        try {
            switch (tabName) {
                case 'dashboard':
                    await this.loadDashboardData();
                    break;
                case 'components':
                    await this.loadComponents();
                    break;
                case 'config':
                    await this.loadConfig();
                    break;
                case 'schools':
                    await this.loadSchools();
                    break;
                case 'logs':
                    await this.loadLogs();
                    break;
            }
        } catch (error) {
            console.error(`❌ [Admin] فشل في تحميل بيانات ${tabName}:`, error);
            this.showToast(`فشل في تحميل بيانات ${tabName}`, 'error');
        }
    }
    
    /**
     * تحميل بيانات لوحة المعلومات
     */
    async loadDashboardData() {
        try {
            this.showLoading(true);
            
            // جلب الإحصائيات
            const stats = await this.fetchStats();
            this.updateDashboardStats(stats);
            
            // جلب النشاط الأخير
            const activity = await this.fetchRecentActivity();
            this.updateRecentActivity(activity);
            
            this.showLoading(false);
            
        } catch (error) {
            console.error('❌ [Admin] فشل في تحميل بيانات لوحة المعلومات:', error);
            this.showLoading(false);
        }
    }
    
    /**
     * جلب الإحصائيات
     */
    async fetchStats() {
        try {
            // محاكاة البيانات (يمكن استبدالها بـ API حقيقي)
            return {
                totalComponents: 5,
                totalSchools: 12,
                totalStudents: 1847,
                lastSync: new Date().toLocaleString('ar-SA')
            };
        } catch (error) {
            console.error('❌ [Admin] فشل في جلب الإحصائيات:', error);
            return {
                totalComponents: 0,
                totalSchools: 0,
                totalStudents: 0,
                lastSync: '--'
            };
        }
    }
    
    /**
     * تحديث إحصائيات لوحة المعلومات
     */
    updateDashboardStats(stats) {
        document.getElementById('total-components').textContent = stats.totalComponents;
        document.getElementById('total-schools').textContent = stats.totalSchools;
        document.getElementById('total-students').textContent = stats.totalStudents.toLocaleString('ar-SA');
        document.getElementById('last-sync').textContent = stats.lastSync;
    }
    
    /**
     * جلب النشاط الأخير
     */
    async fetchRecentActivity() {
        try {
            // محاكاة البيانات
            return [
                {
                    icon: 'fas fa-plus',
                    title: 'إضافة مكون جديد',
                    description: 'تم إضافة مكون "مصمم الكشوف المتقدم"',
                    time: 'منذ 5 دقائق'
                },
                {
                    icon: 'fas fa-sync',
                    title: 'مزامنة البيانات',
                    description: 'تم مزامنة بيانات 3 مدارس جديدة',
                    time: 'منذ 15 دقيقة'
                },
                {
                    icon: 'fas fa-edit',
                    title: 'تحديث الإعدادات',
                    description: 'تم تحديث إعدادات التخزين المؤقت',
                    time: 'منذ ساعة'
                }
            ];
        } catch (error) {
            console.error('❌ [Admin] فشل في جلب النشاط الأخير:', error);
            return [];
        }
    }
    
    /**
     * تحديث النشاط الأخير
     */
    updateRecentActivity(activities) {
        const activityList = document.getElementById('activity-list');
        
        if (activities.length === 0) {
            activityList.innerHTML = '<div class="loading">لا توجد أنشطة حديثة</div>';
            return;
        }
        
        const activityHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <h4>${activity.title}</h4>
                    <p>${activity.description}</p>
                </div>
                <div class="activity-time">
                    ${activity.time}
                </div>
            </div>
        `).join('');
        
        activityList.innerHTML = activityHTML;
    }
    
    /**
     * تحميل المكونات
     */
    async loadComponents() {
        try {
            const response = await fetch(`${this.apiBase}/components.php`);
            const result = await response.json();
            
            if (result.success) {
                this.components = result.data;
                this.renderComponents();
            } else {
                throw new Error(result.message);
            }
            
        } catch (error) {
            console.error('❌ [Admin] فشل في تحميل المكونات:', error);
            document.getElementById('components-list').innerHTML = 
                '<div class="loading">فشل في تحميل المكونات</div>';
        }
    }
    
    /**
     * عرض المكونات
     */
    renderComponents() {
        const componentsList = document.getElementById('components-list');
        
        // محاكاة بيانات المكونات
        const mockComponents = [
            {
                name: 'mainUI',
                type: 'ui',
                description: 'الواجهة الرئيسية للإضافة',
                version: '1.0.0',
                isActive: true,
                updatedAt: new Date().toLocaleDateString('ar-SA')
            },
            {
                name: 'dataModal',
                type: 'modal',
                description: 'موديل عرض البيانات',
                version: '1.0.0',
                isActive: true,
                updatedAt: new Date().toLocaleDateString('ar-SA')
            },
            {
                name: 'designerModal',
                type: 'designer',
                description: 'مصمم الكشوف المتقدم',
                version: '1.0.0',
                isActive: true,
                updatedAt: new Date().toLocaleDateString('ar-SA')
            }
        ];
        
        const componentsHTML = mockComponents.map(component => `
            <div class="component-card">
                <div class="component-header">
                    <h3>${component.name}</h3>
                    <span class="component-type">${component.type}</span>
                </div>
                <div class="component-body">
                    <p class="component-description">${component.description}</p>
                    <div class="component-meta">
                        <span>الإصدار: ${component.version}</span>
                        <span>آخر تحديث: ${component.updatedAt}</span>
                    </div>
                    <div class="component-actions">
                        <button class="btn btn-primary btn-sm" onclick="admin.editComponent('${component.name}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="admin.duplicateComponent('${component.name}')">
                            <i class="fas fa-copy"></i> نسخ
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="admin.deleteComponent('${component.name}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
        
        componentsList.innerHTML = componentsHTML;
    }
    
    /**
     * إظهار موديل المكون
     */
    showComponentModal(componentName = null) {
        const modal = document.getElementById('component-modal');
        const title = document.getElementById('modal-title');
        
        if (componentName) {
            title.textContent = 'تعديل المكون';
            // تحميل بيانات المكون للتعديل
            this.loadComponentForEdit(componentName);
        } else {
            title.textContent = 'إضافة مكون جديد';
            // مسح النموذج
            document.getElementById('component-form').reset();
        }
        
        modal.classList.add('active');
    }
    
    /**
     * إغلاق الموديل
     */
    closeModal() {
        document.getElementById('component-modal').classList.remove('active');
    }
    
    /**
     * تبديل محرر الكود
     */
    switchCodeEditor(editorType) {
        // إزالة التفعيل من جميع الأزرار
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // إخفاء جميع المحررات
        document.querySelectorAll('.code-editor').forEach(editor => {
            editor.classList.remove('active');
        });
        
        // تفعيل المحرر المطلوب
        document.querySelector(`[data-editor="${editorType}"]`).classList.add('active');
        document.getElementById(`${editorType}-editor`).classList.add('active');
    }
    
    /**
     * تحديث الكل
     */
    async refreshAll() {
        try {
            this.showLoading(true);
            
            // تحديث البيانات حسب التبويب الحالي
            await this.loadTabData(this.currentTab);
            
            this.showToast('تم تحديث البيانات بنجاح', 'success');
            this.showLoading(false);
            
        } catch (error) {
            console.error('❌ [Admin] فشل في تحديث البيانات:', error);
            this.showToast('فشل في تحديث البيانات', 'error');
            this.showLoading(false);
        }
    }
    
    /**
     * اختبار الاتصال
     */
    async testConnection() {
        try {
            this.showLoading(true);
            
            const response = await fetch(`${this.apiBase}/config.php`);
            const isConnected = response.ok;
            
            if (isConnected) {
                this.showToast('الاتصال يعمل بشكل طبيعي', 'success');
            } else {
                this.showToast('فشل في الاتصال بالخادم', 'error');
            }
            
            this.showLoading(false);
            
        } catch (error) {
            console.error('❌ [Admin] فشل في اختبار الاتصال:', error);
            this.showToast('خطأ في اختبار الاتصال', 'error');
            this.showLoading(false);
        }
    }
    
    /**
     * إظهار/إخفاء شاشة التحميل
     */
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.add('active');
        } else {
            overlay.classList.remove('active');
        }
    }
    
    /**
     * إظهار رسالة تنبيه
     */
    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icon = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';
        
        toast.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;
        
        container.appendChild(toast);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
    
    // دوال مساعدة للمكونات (ستتم إضافتها لاحقاً)
    async loadConfig() {
        document.getElementById('config-list').innerHTML = '<div class="loading">جاري تحميل الإعدادات...</div>';
    }
    
    async loadSchools() {
        document.getElementById('schools-list').innerHTML = '<div class="loading">جاري تحميل بيانات المدارس...</div>';
    }
    
    async loadLogs() {
        document.getElementById('logs-list').innerHTML = '<div class="loading">جاري تحميل السجلات...</div>';
    }
    
    editComponent(name) {
        this.showComponentModal(name);
    }
    
    duplicateComponent(name) {
        this.showToast(`تم نسخ المكون: ${name}`, 'success');
    }
    
    deleteComponent(name) {
        if (confirm(`هل أنت متأكد من حذف المكون: ${name}؟`)) {
            this.showToast(`تم حذف المكون: ${name}`, 'success');
            this.loadComponents();
        }
    }
}

// تشغيل لوحة التحكم
let admin;
document.addEventListener('DOMContentLoaded', () => {
    admin = new AdminPanel();
});

// دوال عامة
function createNewComponent() {
    admin.showComponentModal();
}

function exportComponents() {
    admin.showToast('جاري تصدير المكونات...', 'info');
}

function importComponents() {
    admin.showToast('جاري استيراد المكونات...', 'info');
}

function clearCache() {
    if (confirm('هل أنت متأكد من مسح التخزين المؤقت؟')) {
        admin.showToast('تم مسح التخزين المؤقت', 'success');
    }
}

function closeModal() {
    admin.closeModal();
}

function saveComponent() {
    admin.showToast('تم حفظ المكون بنجاح', 'success');
    admin.closeModal();
    admin.loadComponents();
}
