<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ لوحة تحكم الإضافة الديناميكية</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="admin-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>🚀 Kushoof Dynamic</h1>
                    <span>لوحة التحكم</span>
                </div>
                <div class="header-actions">
                    <button id="refresh-all-btn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        تحديث الكل
                    </button>
                    <button id="test-connection-btn" class="btn btn-secondary">
                        <i class="fas fa-wifi"></i>
                        اختبار الاتصال
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="admin-nav">
        <div class="container">
            <ul class="nav-tabs">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-tab="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة المعلومات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#components" class="nav-link" data-tab="components">
                        <i class="fas fa-puzzle-piece"></i>
                        إدارة المكونات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#config" class="nav-link" data-tab="config">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#schools" class="nav-link" data-tab="schools">
                        <i class="fas fa-school"></i>
                        بيانات المدارس
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#logs" class="nav-link" data-tab="logs">
                        <i class="fas fa-list-alt"></i>
                        السجلات
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            
            <!-- Dashboard Tab -->
            <div id="dashboard-tab" class="tab-content active">
                <div class="dashboard-grid">
                    <!-- Stats Cards -->
                    <div class="stats-row">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-puzzle-piece"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-components">0</h3>
                                <p>إجمالي المكونات</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-school"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-schools">0</h3>
                                <p>المدارس المتصلة</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-students">0</h3>
                                <p>إجمالي الطلاب</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="last-sync">--</h3>
                                <p>آخر مزامنة</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h2>🚀 إجراءات سريعة</h2>
                        <div class="actions-grid">
                            <button class="action-btn" onclick="createNewComponent()">
                                <i class="fas fa-plus"></i>
                                إضافة مكون جديد
                            </button>
                            <button class="action-btn" onclick="exportComponents()">
                                <i class="fas fa-download"></i>
                                تصدير المكونات
                            </button>
                            <button class="action-btn" onclick="importComponents()">
                                <i class="fas fa-upload"></i>
                                استيراد المكونات
                            </button>
                            <button class="action-btn" onclick="clearCache()">
                                <i class="fas fa-trash"></i>
                                مسح التخزين المؤقت
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="recent-activity">
                        <h2>📊 النشاط الأخير</h2>
                        <div id="activity-list" class="activity-list">
                            <div class="loading">جاري تحميل النشاط...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Components Tab -->
            <div id="components-tab" class="tab-content">
                <div class="tab-header">
                    <h2>📦 إدارة المكونات</h2>
                    <button id="add-component-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة مكون جديد
                    </button>
                </div>
                
                <div class="components-grid">
                    <div id="components-list" class="loading">
                        جاري تحميل المكونات...
                    </div>
                </div>
            </div>

            <!-- Config Tab -->
            <div id="config-tab" class="tab-content">
                <div class="tab-header">
                    <h2>⚙️ إعدادات النظام</h2>
                    <button id="save-config-btn" class="btn btn-success">
                        <i class="fas fa-save"></i>
                        حفظ الإعدادات
                    </button>
                </div>
                
                <div class="config-sections">
                    <div id="config-list" class="loading">
                        جاري تحميل الإعدادات...
                    </div>
                </div>
            </div>

            <!-- Schools Tab -->
            <div id="schools-tab" class="tab-content">
                <div class="tab-header">
                    <h2>🏫 بيانات المدارس</h2>
                    <div class="header-actions">
                        <button id="sync-schools-btn" class="btn btn-primary">
                            <i class="fas fa-sync"></i>
                            مزامنة البيانات
                        </button>
                        <button id="export-schools-btn" class="btn btn-secondary">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>
                
                <div class="schools-table">
                    <div id="schools-list" class="loading">
                        جاري تحميل بيانات المدارس...
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div id="logs-tab" class="tab-content">
                <div class="tab-header">
                    <h2>📋 سجلات النظام</h2>
                    <div class="header-actions">
                        <select id="log-level-filter" class="form-select">
                            <option value="">جميع المستويات</option>
                            <option value="INFO">معلومات</option>
                            <option value="WARNING">تحذيرات</option>
                            <option value="ERROR">أخطاء</option>
                        </select>
                        <button id="clear-logs-btn" class="btn btn-danger">
                            <i class="fas fa-trash"></i>
                            مسح السجلات
                        </button>
                    </div>
                </div>
                
                <div class="logs-container">
                    <div id="logs-list" class="loading">
                        جاري تحميل السجلات...
                    </div>
                </div>
            </div>

        </div>
    </main>

    <!-- Modals -->
    <div id="component-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">إضافة مكون جديد</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="component-form">
                    <div class="form-group">
                        <label for="component-name">اسم المكون:</label>
                        <input type="text" id="component-name" name="component_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="component-type">نوع المكون:</label>
                        <select id="component-type" name="component_type" required>
                            <option value="ui">واجهة مستخدم</option>
                            <option value="modal">نافذة منبثقة</option>
                            <option value="designer">مصمم</option>
                            <option value="function">دالة</option>
                            <option value="style">تنسيق</option>
                            <option value="script">سكريبت</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="component-description">الوصف:</label>
                        <textarea id="component-description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="code-editors">
                        <div class="editor-tab">
                            <button type="button" class="tab-btn active" data-editor="html">HTML</button>
                            <button type="button" class="tab-btn" data-editor="css">CSS</button>
                            <button type="button" class="tab-btn" data-editor="js">JavaScript</button>
                        </div>
                        
                        <div class="editor-content">
                            <textarea id="html-editor" class="code-editor active" name="html_code" placeholder="أدخل كود HTML هنا..."></textarea>
                            <textarea id="css-editor" class="code-editor" name="css_code" placeholder="أدخل كود CSS هنا..."></textarea>
                            <textarea id="js-editor" class="code-editor" name="js_code" placeholder="أدخل كود JavaScript هنا..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveComponent()">حفظ المكون</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner"></div>
        <p>جاري المعالجة...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script src="js/admin.js"></script>
    <script src="js/components.js"></script>
    <script src="js/config.js"></script>
    <script src="js/schools.js"></script>
    <script src="js/logs.js"></script>
</body>
</html>
