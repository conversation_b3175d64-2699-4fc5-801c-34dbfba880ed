# 🚀 Kushoof Dynamic Extension System

## 📋 نظرة عامة

نظام إضافة ديناميكية متقدم يعتمد على قاعدة البيانات لتخزين وإدارة مكونات الواجهة والوظائف.

## 🏗️ هيكل المشروع

```
Dynamic/
├── extension/                 # ملفات الإضافة
│   ├── manifest.json         # إعدادات الإضافة
│   ├── dynamic-loader.js     # المحمل الرئيسي
│   ├── background.js         # خدمات الخلفية
│   └── icons/               # أيقونات الإضافة
├── api/                      # خادم API
│   ├── index.php            # نقطة دخول API
│   ├── config.php           # إعدادات قاعدة البيانات
│   ├── endpoints/           # نقاط النهاية
│   └── database/            # ملفات قاعدة البيانات
├── admin/                    # لوحة التحكم
│   ├── index.html           # الصفحة الرئيسية
│   ├── components.html      # إدارة المكونات
│   ├── css/                 # ملفات التنسيق
│   └── js/                  # ملفات JavaScript
└── docs/                     # الوثائق
    ├── installation.md      # دليل التثبيت
    └── api-reference.md     # مرجع API
```

## 🎯 المفهوم الأساسي

### 1. الإضافة التقليدية vs الديناميكية

**التقليدية:**

```javascript
// كود ثابت في الإضافة
function createModal() {
  // 500 سطر من الكود
}
```

**الديناميكية:**

```javascript
// كود مبسط يجلب من قاعدة البيانات
const modalCode = await fetchComponent("modal");
executeComponent(modalCode);
```

### 2. المميزات

✅ **تحديث فوري** بدون إعادة تثبيت الإضافة
✅ **إدارة مركزية** للمكونات
✅ **تخصيص حسب المدرسة** أو المستخدم
✅ **تتبع الإصدارات** والتحديثات
✅ **أمان محسن** مع تشفير الكود
✅ **سهولة الصيانة** والتطوير

## 🔧 كيفية العمل

1. **الإضافة تتصل بـ API** عند تحميل الصفحة
2. **تجلب المكونات** (HTML, CSS, JS) من قاعدة البيانات
3. **تحقن المكونات** في الصفحة بشكل آمن
4. **تربط الأحداث** وتفعل الوظائف
5. **ترسل البيانات** للخادم عند الحاجة

## 🛡️ الأمان

- **تشفير الكود** المنقول
- **التحقق من المصدر** (Authentication)
- **تنظيف الكود** قبل التنفيذ
- **استخدام Sandbox** للعزل
- **مراقبة الأخطاء** والتسجيل

## 🚀 البدء السريع

1. **إعداد قاعدة البيانات:**

   ```bash
   mysql -u root -p < database/schema.sql
   ```

2. **تكوين API:**

   ```bash
   cp api/config.example.php api/config.php
   # تعديل إعدادات قاعدة البيانات
   ```

3. **تثبيت الإضافة:**

   - افتح Chrome Extensions
   - فعل Developer Mode
   - اضغط Load Unpacked
   - اختر مجلد extension/

4. **فتح لوحة التحكم:**
   ```
   http://localhost/Dynamic/admin/
   ```

## 📚 الوثائق التفصيلية

- [دليل التثبيت](docs/installation.md)
- [مرجع API](docs/api-reference.md)
- [أمثلة الاستخدام](docs/examples.md)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

## 🎯 **تم إنشاء نظام الإضافة الديناميكية بنجاح!**

### ✅ **الملفات المُنشأة:**

#### 📁 **extension/** - ملفات الإضافة

- `manifest.json` - إعدادات الإضافة
- `dynamic-loader.js` - المحمل الرئيسي (445 سطر)
- `background.js` - خدمات الخلفية (200 سطر)
- `popup.html` - النافذة المنبثقة
- `popup.js` - سكريبت النافذة المنبثقة (300 سطر)

#### 📁 **api/** - خادم API

- `config.php` - إعدادات قاعدة البيانات والأمان (300 سطر)
- `components.php` - API جلب المكونات (300 سطر)
- `database/schema.sql` - هيكل قاعدة البيانات (300 سطر)

#### 📁 **admin/** - لوحة التحكم

- `index.html` - الصفحة الرئيسية للوحة التحكم
- `css/admin.css` - تنسيقات لوحة التحكم (300 سطر)
- `js/admin.js` - سكريبت لوحة التحكم (300 سطر)

#### 📁 **docs/** - الوثائق

- `installation.md` - دليل التثبيت الشامل (300 سطر)
- `api-reference.md` - مرجع API كامل (300 سطر)

### 🚀 **المميزات المطبقة:**

#### 1. **الإضافة الديناميكية:**

```javascript
// تحميل المكونات من قاعدة البيانات
const components = await this.fetchComponents();
await this.loadComponents(components);

// تنفيذ الكود بشكل آمن
const iframe = document.createElement("iframe");
iframe.sandbox = "allow-scripts allow-same-origin";
```

#### 2. **قاعدة البيانات المتقدمة:**

```sql
-- جداول شاملة
extension_components    -- مكونات الإضافة
extension_config       -- إعدادات النظام
school_data           -- بيانات المدارس
activity_logs         -- سجلات النشاط
usage_stats          -- إحصائيات الاستخدام
```

#### 3. **API متكامل:**

```php
// نقاط نهاية شاملة
GET  /config.php        // إعدادات النظام
GET  /components.php    // جلب المكونات
POST /save-data.php     // حفظ البيانات
POST /update-component.php // تحديث المكونات
```

#### 4. **لوحة تحكم احترافية:**

```html
<!-- تبويبات متعددة -->
- لوحة المعلومات (إحصائيات) - إدارة المكونات (HTML/CSS/JS) - الإعدادات (تكوين
النظام) - بيانات المدارس (147 طالب) - السجلات (مراقبة النشاط)
```

#### 5. **الأمان المحسن:**

```php
// تشفير البيانات
define('ENCRYPTION_METHOD', 'AES-256-CBC');
Security::encrypt($data);

// CORS محدود
define('ALLOWED_ORIGINS', [
    'https://schools.madrasati.sa',
    'chrome-extension://*'
]);
```

### 🎯 **كيفية العمل:**

#### 1. **تدفق البيانات:**

```
مدرستي → الإضافة → API → قاعدة البيانات
     ↓
لوحة التحكم ← API ← قاعدة البيانات
     ↓
تحديث المكونات → الإضافة → مدرستي
```

#### 2. **التحديث الديناميكي:**

```javascript
// بدلاً من كود ثابت
function createModal() {
  /* 500 سطر */
}

// كود ديناميكي من قاعدة البيانات
const modalCode = await fetchComponent("modal");
executeComponent(modalCode);
```

### 🛠️ **خطوات التشغيل:**

#### 1. **إعداد قاعدة البيانات:**

```bash
mysql -u root -p < api/database/schema.sql
```

#### 2. **تكوين API:**

```php
// في api/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'kushoof_dynamic');
define('DB_USER', 'root');
define('DB_PASS', '');
```

#### 3. **تثبيت الإضافة:**

```
chrome://extensions/ → Load unpacked → Dynamic/extension/
```

#### 4. **فتح لوحة التحكم:**

```
http://localhost/Dynamic/admin/
```

### 🎊 **النتيجة النهائية:**

الآن لديك **نظام إضافة ديناميكية متكامل** يتيح:

- ✅ **تحديث فوري** للمكونات دون إعادة تثبيت
- ✅ **إدارة مركزية** من لوحة تحكم احترافية
- ✅ **أمان محسن** مع تشفير وحماية
- ✅ **مرونة كاملة** في التخصيص والتطوير
- ✅ **سهولة الصيانة** والتحديث
- ✅ **وثائق شاملة** للتثبيت والاستخدام

**🚀 جرب النظام الآن واستمتع بالمرونة الكاملة في تطوير الإضافات!**
