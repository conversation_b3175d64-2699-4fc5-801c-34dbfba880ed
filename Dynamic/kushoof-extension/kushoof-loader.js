/**
 * 🚀 Kushoof Real Data Extension
 * إضافة بسيطة تجلب البيانات الحقيقية من kushoofapp.com
 */

class KushoofRealDataExtension {
    constructor() {
        // فقط API الحقيقي من kushoofapp.com
        this.apiUrl = 'https://kushoofapp.com/js/api/receive-data.php';
        this.realData = null;

        console.log('🚀 [Kushoof] جلب البيانات الحقيقية من قاعدة البيانات');
        this.init();
    }
    
    async init() {
        try {
            // جلب البيانات الحقيقية من الموقع
            await this.fetchRealData();
            
            // جلب الواجهة من الموقع
            await this.loadUIFromWebsite();
            
            console.log('✅ [Kushoof] تم تحميل الإضافة بنجاح');
            
        } catch (error) {
            console.error('❌ [Kushoof] فشل في تحميل البيانات:', error);
            this.showError();
        }
    }
    
    async fetchRealData() {
        try {
            console.log('📡 [Kushoof] جلب البيانات من قاعدة البيانات الحقيقية...');
            console.log(`🔗 [Kushoof] الاتصال بـ: ${this.apiUrl}`);

            const response = await fetch(this.apiUrl);

            if (!response.ok) {
                throw new Error(`فشل في الاتصال بقاعدة البيانات: HTTP ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            console.log('📦 [Kushoof] البيانات المجلبة:', data);

            // التحقق من وجود بيانات حقيقية
            if (!data) {
                throw new Error('لا توجد بيانات في الاستجابة');
            }

            // إذا كانت البيانات مصفوفة مباشرة
            if (Array.isArray(data) && data.length > 0) {
                this.realData = data;
                console.log(`✅ [Kushoof] تم جلب ${data.length} عنصر من قاعدة البيانات الحقيقية`);
                return data;
            }

            // إذا كانت البيانات في خاصية معينة
            if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                this.realData = data.data;
                console.log(`✅ [Kushoof] تم جلب ${data.data.length} عنصر من قاعدة البيانات الحقيقية`);
                return data.data;
            }

            // إذا كانت البيانات في خاصية أخرى
            if (data.students && Array.isArray(data.students) && data.students.length > 0) {
                this.realData = data.students;
                console.log(`✅ [Kushoof] تم جلب ${data.students.length} طالب من قاعدة البيانات الحقيقية`);
                return data.students;
            }

            // إذا كانت البيانات في خاصية results
            if (data.results && Array.isArray(data.results) && data.results.length > 0) {
                this.realData = data.results;
                console.log(`✅ [Kushoof] تم جلب ${data.results.length} عنصر من قاعدة البيانات الحقيقية`);
                return data.results;
            }

            throw new Error('لا توجد بيانات حقيقية في قاعدة البيانات أو البيانات فارغة');

        } catch (error) {
            console.error('❌ [Kushoof] فشل في جلب البيانات الحقيقية:', error);
            throw error;
        }
    }


    
    async loadUIFromWebsite() {
        try {
            console.log('🎨 [Kushoof] تحميل الواجهة من الموقع...');
            
            // إنشاء الواجهة بناءً على البيانات الحقيقية
            this.createUI();
            
            console.log('✅ [Kushoof] تم تحميل الواجهة بنجاح');
            
        } catch (error) {
            console.error('❌ [Kushoof] فشل في تحميل الواجهة:', error);
            throw error;
        }
    }
    
    createUI() {
        // إنشاء الأزرار
        const uiContainer = document.createElement('div');
        uiContainer.id = 'kushoof-real-ui';
        uiContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        `;
        
        uiContainer.innerHTML = `
            <button id="kushoof-show-real-data" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                cursor: pointer;
                margin: 5px;
                font-size: 14px;
                font-weight: bold;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                display: block;
                width: 200px;
                text-align: center;
            ">
                📊 عرض البيانات الحقيقية
            </button>
            
            <button id="kushoof-export-data" style="
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                cursor: pointer;
                margin: 5px;
                font-size: 14px;
                font-weight: bold;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                display: block;
                width: 200px;
                text-align: center;
            ">
                📤 تصدير البيانات
            </button>
        `;
        
        document.body.appendChild(uiContainer);
        
        // ربط الأحداث
        this.bindEvents();
        
        // إضافة إشعار النجاح
        this.showSuccessNotice();
    }
    
    bindEvents() {
        const showDataBtn = document.getElementById('kushoof-show-real-data');
        const exportBtn = document.getElementById('kushoof-export-data');
        
        if (showDataBtn) {
            showDataBtn.addEventListener('click', () => this.showRealData());
        }
        
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportRealData());
        }
        
        console.log('🔗 [Kushoof] تم ربط الأحداث');
    }
    
    showRealData() {
        if (!this.realData || this.realData.length === 0) {
            alert('❌ لا توجد بيانات حقيقية متاحة');
            return;
        }
        
        console.log('📊 [Kushoof] عرض البيانات الحقيقية');
        
        // إنشاء موديل لعرض البيانات
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        // تحليل البيانات الحقيقية
        const dataAnalysis = this.analyzeRealData();
        
        modal.innerHTML = `
            <div style="
                background: white;
                width: 95%;
                height: 90%;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                display: flex;
                flex-direction: column;
            ">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <div>
                        <h1 style="margin: 0; font-size: 24px;">📊 البيانات الحقيقية من kushoofapp.com</h1>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">
                            تم جلب ${this.realData.length} عنصر • ${new Date().toLocaleString('ar-SA')}
                        </p>
                    </div>
                    <button onclick="this.closest('div[style*=\"position: fixed\"]').remove()" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 12px 16px;
                        border-radius: 50%;
                        cursor: pointer;
                        font-size: 18px;
                    ">✕</button>
                </div>
                
                <div style="flex: 1; padding: 20px; overflow-y: auto;">
                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                        <h3 style="margin: 0 0 10px 0; color: #155724;">✅ بيانات حقيقية من قاعدة البيانات</h3>
                        <p style="margin: 0; color: #155724;">
                            تم جلب البيانات مباشرة من قاعدة البيانات في kushoofapp.com
                        </p>
                        <p style="margin: 5px 0 0 0; color: #155724; font-size: 12px;">
                            API: ${this.apiUrl}
                        </p>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="margin: 0; color: #667eea;">📊 إجمالي العناصر</h4>
                            <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #333;">
                                ${this.realData.length}
                            </p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="margin: 0; color: #28a745;">🔗 المصدر</h4>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #333;">
                                قاعدة البيانات الحقيقية
                            </p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="margin: 0; color: #764ba2;">⏰ وقت الجلب</h4>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #333;">
                                ${new Date().toLocaleTimeString('ar-SA')}
                            </p>
                        </div>
                    </div>
                    
                    <h3>📋 البيانات الحقيقية من قاعدة البيانات:</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto;">
                        <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">
${JSON.stringify(this.realData, null, 2)}
                        </pre>
                    </div>
                    
                    ${dataAnalysis}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }
    
    analyzeRealData() {
        if (!this.realData || this.realData.length === 0) {
            return '<p>لا توجد بيانات للتحليل</p>';
        }
        
        // تحليل بسيط للبيانات
        const firstItem = this.realData[0];
        const keys = Object.keys(firstItem);
        
        return `
            <div style="margin-top: 20px;">
                <h3>🔍 تحليل البيانات:</h3>
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
                    <p><strong>عدد الحقول في كل عنصر:</strong> ${keys.length}</p>
                    <p><strong>الحقول المتاحة:</strong></p>
                    <ul style="margin: 10px 0; padding-right: 20px;">
                        ${keys.map(key => `<li><code>${key}</code></li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }
    
    exportRealData() {
        if (!this.realData || this.realData.length === 0) {
            alert('❌ لا توجد بيانات حقيقية للتصدير');
            return;
        }
        
        console.log('📤 [Kushoof] تصدير البيانات الحقيقية');
        
        const exportData = {
            exportDate: new Date().toISOString(),
            source: 'قاعدة البيانات الحقيقية - kushoofapp.com',
            api: this.apiUrl,
            totalItems: this.realData.length,
            realDatabaseData: this.realData,
            metadata: {
                exportedBy: 'Kushoof Real Data Extension',
                version: '1.0.0',
                exportTime: new Date().toLocaleString('ar-SA'),
                note: 'هذه بيانات حقيقية من قاعدة البيانات'
            }
        };
        
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `kushoof-real-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        alert(`📊 تم تصدير البيانات الحقيقية بنجاح!\n\nعدد العناصر: ${this.realData.length}\nالمصدر: kushoofapp.com\nالملف: kushoof-real-data.json`);
    }
    
    showSuccessNotice() {
        const notice = document.createElement('div');
        notice.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            z-index: 999998;
            font-family: Arial, sans-serif;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        `;

        notice.innerHTML = `
            ✅ قاعدة البيانات الحقيقية<br>
            <small>${this.realData ? this.realData.length : 0} عنصر من kushoofapp.com</small>
        `;
        document.body.appendChild(notice);

        // إزالة الإشعار بعد 10 ثوان
        setTimeout(() => notice.remove(), 10000);
    }
    
    showError() {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 999999;
            font-family: Arial, sans-serif;
            max-width: 300px;
        `;
        errorDiv.innerHTML = `
            ❌ فشل في الاتصال بـ kushoofapp.com<br>
            <small>تحقق من الاتصال بالإنترنت</small>
        `;
        document.body.appendChild(errorDiv);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => errorDiv.remove(), 5000);
    }
}

// تشغيل الإضافة
new KushoofRealDataExtension();
