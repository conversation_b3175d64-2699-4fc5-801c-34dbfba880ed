/**
 * 🚀 Kushoof Real Data Extension
 * إضافة بسيطة تجلب البيانات الحقيقية من kushoofapp.com
 */

class KushoofRealDataExtension {
    constructor() {
        // APIs للبيانات
        this.dataApiUrls = [
            'https://kushoofapp.com/js/api/receive-data-fixed.php',  // الملف المُصحح
            'https://kushoofapp.com/js/api/receive-data.php'         // الملف الأصلي
        ];

        // API لأكواد الإضافة
        this.uiApiUrl = 'https://kushoofapp.com/js/api/get-extension-ui.php';

        this.realData = null;
        this.workingApiUrl = '';
        this.uiComponents = null;

        console.log('🚀 [Kushoof] جلب البيانات والأكواد من الموقع');
        this.init();
    }
    
    async init() {
        try {
            // جلب البيانات الحقيقية من الموقع
            await this.fetchRealData();

            // جلب أكواد الإضافة من الموقع
            await this.fetchUIComponents();

            // تطبيق الأكواد المجلبة
            await this.applyUIComponents();

            console.log('✅ [Kushoof] تم تحميل الإضافة والأكواد من الموقع بنجاح');

        } catch (error) {
            console.error('❌ [Kushoof] فشل في تحميل البيانات أو الأكواد:', error);
            this.showError();
        }
    }
    
    async fetchRealData() {
        console.log('📡 [Kushoof] جلب البيانات من قاعدة البيانات الحقيقية...');

        // جرب كل API حتى تجد واحد يعمل
        for (let i = 0; i < this.dataApiUrls.length; i++) {
            const apiUrl = this.dataApiUrls[i];

            try {
                console.log(`🔄 [Kushoof] جرب API ${i + 1}: ${apiUrl}`);

                const response = await fetch(apiUrl);

                if (!response.ok) {
                    console.warn(`⚠️ [Kushoof] API ${i + 1} فشل: HTTP ${response.status}`);
                    continue;
                }

                const data = await response.json();
                console.log(`📦 [Kushoof] البيانات المجلبة من API ${i + 1}:`, data);

                // التحقق من وجود بيانات حقيقية
                let realData = null;

                // إذا كانت البيانات مصفوفة مباشرة
                if (Array.isArray(data) && data.length > 0) {
                    realData = data;
                }
                // إذا كانت البيانات في خاصية معينة
                else if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                    realData = data.data;
                }
                // إذا كانت البيانات في خاصية أخرى
                else if (data.students && Array.isArray(data.students) && data.students.length > 0) {
                    realData = data.students;
                }
                // إذا كانت البيانات في خاصية results
                else if (data.results && Array.isArray(data.results) && data.results.length > 0) {
                    realData = data.results;
                }

                if (realData && realData.length > 0) {
                    this.realData = realData;
                    this.workingApiUrl = apiUrl;
                    console.log(`✅ [Kushoof] تم جلب ${realData.length} عنصر من قاعدة البيانات الحقيقية`);
                    console.log(`🎯 [Kushoof] API الناجح: ${apiUrl}`);
                    return realData;
                }

                console.warn(`⚠️ [Kushoof] API ${i + 1} لا يحتوي على بيانات`);

            } catch (error) {
                console.warn(`⚠️ [Kushoof] خطأ في API ${i + 1}:`, error);
                continue;
            }
        }

        throw new Error('فشل في جلب البيانات الحقيقية من جميع APIs');
    }

    async fetchUIComponents() {
        try {
            console.log('🎨 [Kushoof] جلب أكواد الإضافة من الموقع...');
            console.log(`🔗 [Kushoof] الاتصال بـ: ${this.uiApiUrl}`);

            const response = await fetch(this.uiApiUrl);

            if (!response.ok) {
                throw new Error(`فشل في جلب أكواد الإضافة: HTTP ${response.status}`);
            }

            const data = await response.json();
            console.log('📦 [Kushoof] أكواد الإضافة المجلبة:', data);

            if (data.success && data.ui) {
                this.uiComponents = data.ui;
                console.log('✅ [Kushoof] تم جلب أكواد الإضافة من الموقع بنجاح');
                return data.ui;
            } else {
                throw new Error('فشل في جلب أكواد الإضافة من الموقع');
            }

        } catch (error) {
            console.error('❌ [Kushoof] فشل في جلب أكواد الإضافة:', error);
            throw error;
        }
    }

    async applyUIComponents() {
        if (!this.uiComponents) {
            throw new Error('لا توجد أكواد إضافة لتطبيقها');
        }

        try {
            console.log('🔧 [Kushoof] تطبيق أكواد الإضافة المجلبة من الموقع...');

            // تطبيق CSS
            if (this.uiComponents.css) {
                this.injectCSS(this.uiComponents.css);
                console.log('🎨 [Kushoof] تم تطبيق CSS من الموقع');
            }

            // تطبيق HTML
            if (this.uiComponents.html) {
                this.injectHTML(this.uiComponents.html);
                console.log('🏗️ [Kushoof] تم تطبيق HTML من الموقع');
            }

            // تطبيق JavaScript
            if (this.uiComponents.javascript) {
                this.executeJavaScript(this.uiComponents.javascript);
                console.log('⚡ [Kushoof] تم تطبيق JavaScript من الموقع');
            }

            // تمرير البيانات للدوال المجلبة
            this.passDataToUI();

            console.log('✅ [Kushoof] تم تطبيق جميع أكواد الإضافة من الموقع');

        } catch (error) {
            console.error('❌ [Kushoof] فشل في تطبيق أكواد الإضافة:', error);
            throw error;
        }
    }

    injectCSS(cssCode) {
        const style = document.createElement('style');
        style.textContent = cssCode;
        document.head.appendChild(style);
    }

    injectHTML(htmlCode) {
        const container = document.createElement('div');
        container.innerHTML = htmlCode;
        document.body.appendChild(container);
    }

    executeJavaScript(jsCode) {
        try {
            const script = document.createElement('script');
            script.textContent = jsCode;
            document.head.appendChild(script);
        } catch (error) {
            console.error('❌ [Kushoof] فشل في تنفيذ JavaScript:', error);
        }
    }

    passDataToUI() {
        // تمرير البيانات للدوال المجلبة من الموقع
        window.kushoofRealData = this.realData;
        window.kushoofApiUrl = this.workingApiUrl;
        console.log('📊 [Kushoof] تم تمرير البيانات للواجهة المجلبة من الموقع');
    }


    

    

    

    

    
    showError() {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 999999;
            font-family: Arial, sans-serif;
            max-width: 300px;
        `;
        errorDiv.innerHTML = `
            ❌ فشل في الاتصال بـ kushoofapp.com<br>
            <small>تحقق من الاتصال بالإنترنت</small>
        `;
        document.body.appendChild(errorDiv);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => errorDiv.remove(), 5000);
    }
}

// تشغيل الإضافة
new KushoofRealDataExtension();
