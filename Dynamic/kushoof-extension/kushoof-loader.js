class KushoofRealDataExtension {
    constructor() {
        this.dataApiUrls = [
            'https://kushoofapp.com/js/api/receive-data-fixed.php',
            'https://kushoofapp.com/js/api/receive-data.php'
        ];
        this.uiApiUrl = 'https://kushoofapp.com/js/api/get-extension-ui.php';
        this.realData = null;
        this.workingApiUrl = '';
        this.init();
    }

    async init() {
        try {
            await this.fetchRealData();
            await this.fetchUIComponents();
            await this.applyUIComponents();
        } catch (error) {
            this.showError();
        }
    }

    async fetchRealData() {
        for (let i = 0; i < this.dataApiUrls.length; i++) {
            try {
                const response = await fetch(this.dataApiUrls[i]);
                if (!response.ok) continue;
                
                const data = await response.json();
                let realData = null;
                
                if (Array.isArray(data) && data.length > 0) {
                    realData = data;
                } else if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                    realData = data.data;
                } else if (data.students && Array.isArray(data.students) && data.students.length > 0) {
                    realData = data.students;
                } else if (data.results && Array.isArray(data.results) && data.results.length > 0) {
                    realData = data.results;
                }
                
                if (realData && realData.length > 0) {
                    this.realData = realData;
                    this.workingApiUrl = this.dataApiUrls[i];
                    return realData;
                }
            } catch (error) {
                continue;
            }
        }
        throw new Error('فشل في جلب البيانات');
    }

    async fetchUIComponents() {
        const response = await fetch(this.uiApiUrl);
        if (!response.ok) throw new Error('فشل في جلب الأكواد');
        
        const data = await response.json();
        if (data.success && data.ui) {
            this.uiComponents = data.ui;
            return data.ui;
        }
        throw new Error('فشل في جلب الأكواد');
    }

    async applyUIComponents() {
        if (!this.uiComponents) throw new Error('لا توجد أكواد');
        
        if (this.uiComponents.css) {
            const style = document.createElement('style');
            style.textContent = this.uiComponents.css;
            document.head.appendChild(style);
        }
        
        if (this.uiComponents.html) {
            const container = document.createElement('div');
            container.innerHTML = this.uiComponents.html;
            
            const buttons = container.querySelectorAll('button[onclick]');
            buttons.forEach(button => {
                const onclickValue = button.getAttribute('onclick');
                button.removeAttribute('onclick');
                
                if (onclickValue.includes('showRealStudentsData')) {
                    button.addEventListener('click', () => this.showRealData());
                } else if (onclickValue.includes('exportRealData')) {
                    button.addEventListener('click', () => this.exportRealData());
                }
            });
            
            document.body.appendChild(container);
        }
        
        if (this.uiComponents.javascript) {
            try {
                // تنفيذ JavaScript بطريقة آمنة
                eval(this.uiComponents.javascript);
            } catch (error) {
                // إذا فشل، عرّف دوال بسيطة
                this.defineSimpleFunctions();
            }
        } else {
            this.defineSimpleFunctions();
        }
        
        window.kushoofRealData = this.realData;
        window.kushoofApiUrl = this.workingApiUrl;
    }

    showRealData() {
        if (typeof window.showRealStudentsData === 'function') {
            window.showRealStudentsData();
        }
    }

    exportRealData() {
        if (typeof window.exportRealData === 'function') {
            window.exportRealData();
        }
    }

    defineSimpleFunctions() {
        // تعريف دوال بسيطة إذا فشل تحميل الدوال من الموقع
        window.showRealStudentsData = () => {
            if (!this.realData || this.realData.length === 0) {
                alert('❌ لا توجد بيانات');
                return;
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 1000000;
                display: flex; align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; width: 95%; height: 90%; border-radius: 15px; overflow: hidden; display: flex; flex-direction: column;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h1 style="margin: 0; font-size: 24px;">📊 البيانات الحقيقية من kushoofapp.com</h1>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">تم جلب ${this.realData.length} طالب</p>
                        </div>
                        <button onclick="this.closest('div[style*=\"position: fixed\"]').remove()" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 12px 16px; border-radius: 50%; cursor: pointer; font-size: 18px;">✕</button>
                    </div>
                    <div style="flex: 1; padding: 20px; overflow-y: auto;">
                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                            <h3 style="margin: 0 0 10px 0; color: #155724;">✅ بيانات حقيقية من قاعدة البيانات</h3>
                            <p style="margin: 0; color: #155724;">تم جلب البيانات مباشرة من قاعدة البيانات في kushoofapp.com</p>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #667eea;">📊 إجمالي الطلاب</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #333;">${this.realData.length}</p>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #28a745;">🔗 المصدر</h4>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #333;">قاعدة البيانات الحقيقية</p>
                            </div>
                        </div>
                        <h3>📋 البيانات الحقيقية من قاعدة البيانات:</h3>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto;">
                            <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">${JSON.stringify(this.realData, null, 2)}</pre>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        };

        window.exportRealData = () => {
            if (!this.realData || this.realData.length === 0) {
                alert('❌ لا توجد بيانات للتصدير');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                source: 'قاعدة البيانات الحقيقية - kushoofapp.com',
                api: this.workingApiUrl,
                totalStudents: this.realData.length,
                realDatabaseData: this.realData,
                metadata: {
                    exportedBy: 'Kushoof Real Data Extension',
                    version: '3.0.0',
                    exportTime: new Date().toLocaleString('ar-SA')
                }
            };

            const jsonString = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `kushoof-real-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert(`📊 تم تصدير البيانات بنجاح!\n\nعدد الطلاب: ${this.realData.length}\nالمصدر: kushoofapp.com`);
        };
    }

    showError() {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed; top: 20px; right: 20px; background: #dc3545; color: white;
            padding: 15px; border-radius: 8px; z-index: 999999; font-family: Arial, sans-serif;
        `;
        errorDiv.innerHTML = `❌ فشل في الاتصال بـ kushoofapp.com`;
        document.body.appendChild(errorDiv);
        setTimeout(() => errorDiv.remove(), 5000);
    }
}

new KushoofRealDataExtension();
