/**
 * 🚀 Kushoof Real Data Extension
 * إضافة بسيطة تجلب البيانات الحقيقية من kushoofapp.com
 */

class KushoofRealDataExtension {
    constructor() {
        // APIs للبيانات
        this.dataApiUrls = [
            'https://kushoofapp.com/js/api/receive-data-fixed.php',  // الملف المُصحح
            'https://kushoofapp.com/js/api/receive-data.php'         // الملف الأصلي
        ];

        // API لأكواد الإضافة
        this.uiApiUrl = 'https://kushoofapp.com/js/api/get-extension-ui.php';

        this.realData = null;
        this.workingApiUrl = '';
        this.uiComponents = null;

        console.log('🚀 [Kushoof] جلب البيانات والأكواد من الموقع');
        this.init();
    }
    
    async init() {
        try {
            // جلب البيانات الحقيقية من الموقع
            await this.fetchRealData();

            // جلب أكواد الإضافة من الموقع
            await this.fetchUIComponents();

            // تطبيق الأكواد المجلبة
            await this.applyUIComponents();

            console.log('✅ [Kushoof] تم تحميل الإضافة والأكواد من الموقع بنجاح');

        } catch (error) {
            console.error('❌ [Kushoof] فشل في تحميل البيانات أو الأكواد:', error);
            this.showError();
        }
    }
    
    async fetchRealData() {
        console.log('📡 [Kushoof] جلب البيانات من قاعدة البيانات الحقيقية...');

        // جرب كل API حتى تجد واحد يعمل
        for (let i = 0; i < this.dataApiUrls.length; i++) {
            const apiUrl = this.dataApiUrls[i];

            try {
                console.log(`🔄 [Kushoof] جرب API ${i + 1}: ${apiUrl}`);

                const response = await fetch(apiUrl);

                if (!response.ok) {
                    console.warn(`⚠️ [Kushoof] API ${i + 1} فشل: HTTP ${response.status}`);
                    continue;
                }

                const data = await response.json();
                console.log(`📦 [Kushoof] البيانات المجلبة من API ${i + 1}:`, data);

                // التحقق من وجود بيانات حقيقية
                let realData = null;

                // إذا كانت البيانات مصفوفة مباشرة
                if (Array.isArray(data) && data.length > 0) {
                    realData = data;
                }
                // إذا كانت البيانات في خاصية معينة
                else if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                    realData = data.data;
                }
                // إذا كانت البيانات في خاصية أخرى
                else if (data.students && Array.isArray(data.students) && data.students.length > 0) {
                    realData = data.students;
                }
                // إذا كانت البيانات في خاصية results
                else if (data.results && Array.isArray(data.results) && data.results.length > 0) {
                    realData = data.results;
                }

                if (realData && realData.length > 0) {
                    this.realData = realData;
                    this.workingApiUrl = apiUrl;
                    console.log(`✅ [Kushoof] تم جلب ${realData.length} عنصر من قاعدة البيانات الحقيقية`);
                    console.log(`🎯 [Kushoof] API الناجح: ${apiUrl}`);
                    return realData;
                }

                console.warn(`⚠️ [Kushoof] API ${i + 1} لا يحتوي على بيانات`);

            } catch (error) {
                console.warn(`⚠️ [Kushoof] خطأ في API ${i + 1}:`, error);
                continue;
            }
        }

        throw new Error('فشل في جلب البيانات الحقيقية من جميع APIs');
    }

    async fetchUIComponents() {
        try {
            console.log('🎨 [Kushoof] جلب أكواد الإضافة من الموقع...');
            console.log(`🔗 [Kushoof] الاتصال بـ: ${this.uiApiUrl}`);

            const response = await fetch(this.uiApiUrl);

            if (!response.ok) {
                throw new Error(`فشل في جلب أكواد الإضافة: HTTP ${response.status}`);
            }

            const data = await response.json();
            console.log('📦 [Kushoof] أكواد الإضافة المجلبة:', data);

            if (data.success && data.ui) {
                this.uiComponents = data.ui;
                console.log('✅ [Kushoof] تم جلب أكواد الإضافة من الموقع بنجاح');
                return data.ui;
            } else {
                throw new Error('فشل في جلب أكواد الإضافة من الموقع');
            }

        } catch (error) {
            console.error('❌ [Kushoof] فشل في جلب أكواد الإضافة:', error);
            throw error;
        }
    }

    async applyUIComponents() {
        if (!this.uiComponents) {
            throw new Error('لا توجد أكواد إضافة لتطبيقها');
        }

        try {
            console.log('🔧 [Kushoof] تطبيق أكواد الإضافة المجلبة من الموقع...');

            // تطبيق CSS
            if (this.uiComponents.css) {
                this.injectCSS(this.uiComponents.css);
                console.log('🎨 [Kushoof] تم تطبيق CSS من الموقع');
            }

            // تطبيق HTML
            if (this.uiComponents.html) {
                this.injectHTML(this.uiComponents.html);
                console.log('🏗️ [Kushoof] تم تطبيق HTML من الموقع');
            }

            // تطبيق JavaScript
            if (this.uiComponents.javascript) {
                this.executeJavaScript(this.uiComponents.javascript);
                console.log('⚡ [Kushoof] تم تطبيق JavaScript من الموقع');
            }

            // تمرير البيانات للدوال المجلبة
            this.passDataToUI();

            // إضافة إشعار النجاح
            this.showSuccessNotice();

            console.log('✅ [Kushoof] تم تطبيق جميع أكواد الإضافة من الموقع');

        } catch (error) {
            console.error('❌ [Kushoof] فشل في تطبيق أكواد الإضافة:', error);
            throw error;
        }
    }

    injectCSS(cssCode) {
        const style = document.createElement('style');
        style.textContent = cssCode;
        document.head.appendChild(style);
    }

    injectHTML(htmlCode) {
        const container = document.createElement('div');
        container.innerHTML = htmlCode;

        // إزالة onclick attributes وإضافة event listeners بدلاً منها
        const buttons = container.querySelectorAll('button[onclick]');
        buttons.forEach(button => {
            const onclickValue = button.getAttribute('onclick');
            button.removeAttribute('onclick');

            if (onclickValue.includes('showRealStudentsData')) {
                button.addEventListener('click', () => {
                    if (typeof window.showRealStudentsData === 'function') {
                        window.showRealStudentsData();
                    } else {
                        console.log('📊 [Kushoof] استخدام الدالة الاحتياطية لعرض البيانات');
                        this.showRealDataBackup();
                    }
                });
            } else if (onclickValue.includes('exportRealData')) {
                button.addEventListener('click', () => {
                    if (typeof window.exportRealData === 'function') {
                        window.exportRealData();
                    } else {
                        console.log('📤 [Kushoof] استخدام الدالة الاحتياطية لتصدير البيانات');
                        this.exportRealDataBackup();
                    }
                });
            }
        });

        document.body.appendChild(container);
        console.log('🔗 [Kushoof] تم ربط الأحداث بالأزرار');
    }

    executeJavaScript(jsCode) {
        try {
            console.log('🔧 [Kushoof] تنفيذ JavaScript المجلب من الموقع...');

            // إنشاء script element وإضافته للصفحة
            const script = document.createElement('script');
            script.textContent = jsCode;
            script.type = 'text/javascript';

            // إضافة معرف فريد للتتبع
            script.id = 'kushoof-dynamic-script-' + Date.now();

            // إضافة Script للصفحة
            document.head.appendChild(script);

            console.log('✅ [Kushoof] تم إضافة JavaScript المجلب للصفحة');

            // التحقق من تعريف الدوال بعد فترة قصيرة
            setTimeout(() => {
                if (typeof window.showRealStudentsData === 'function') {
                    console.log('✅ [Kushoof] دالة showRealStudentsData معرّفة بنجاح');
                } else {
                    console.warn('⚠️ [Kushoof] دالة showRealStudentsData غير معرّفة، استخدام الدوال الاحتياطية');
                    this.defineBackupFunctions();
                }

                if (typeof window.exportRealData === 'function') {
                    console.log('✅ [Kushoof] دالة exportRealData معرّفة بنجاح');
                } else {
                    console.warn('⚠️ [Kushoof] دالة exportRealData غير معرّفة');
                }
            }, 200);

        } catch (error) {
            console.error('❌ [Kushoof] فشل في تنفيذ JavaScript:', error);
            this.defineBackupFunctions();
        }
    }

    defineBackupFunctions() {
        console.log('🔄 [Kushoof] تعريف دوال احتياطية...');

        // تعريف دالة عرض البيانات كاحتياط
        window.showRealStudentsData = () => {
            if (!window.kushoofRealData || window.kushoofRealData.length === 0) {
                alert('❌ لا توجد بيانات حقيقية متاحة');
                return;
            }

            console.log('📊 [Kushoof] عرض البيانات الحقيقية (دالة احتياطية)');

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 1000000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    width: 95%;
                    height: 90%;
                    border-radius: 15px;
                    overflow: hidden;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    display: flex;
                    flex-direction: column;
                ">
                    <div style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <div>
                            <h1 style="margin: 0; font-size: 24px;">📊 البيانات الحقيقية من kushoofapp.com</h1>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">
                                تم جلب ${window.kushoofRealData.length} طالب • ${new Date().toLocaleString('ar-SA')}
                            </p>
                        </div>
                        <button onclick="this.closest('div[style*=\"position: fixed\"]').remove()" style="
                            background: rgba(255,255,255,0.2);
                            color: white;
                            border: none;
                            padding: 12px 16px;
                            border-radius: 50%;
                            cursor: pointer;
                            font-size: 18px;
                        ">✕</button>
                    </div>

                    <div style="flex: 1; padding: 20px; overflow-y: auto;">
                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                            <h3 style="margin: 0 0 10px 0; color: #155724;">✅ بيانات حقيقية من قاعدة البيانات</h3>
                            <p style="margin: 0; color: #155724;">
                                تم جلب البيانات مباشرة من قاعدة البيانات في kushoofapp.com
                            </p>
                            <p style="margin: 5px 0 0 0; color: #155724; font-size: 12px;">
                                API: ${window.kushoofApiUrl}
                            </p>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #667eea;">📊 إجمالي الطلاب</h4>
                                <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #333;">
                                    ${window.kushoofRealData.length}
                                </p>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #28a745;">🔗 المصدر</h4>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #333;">
                                    قاعدة البيانات الحقيقية
                                </p>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <h4 style="margin: 0; color: #764ba2;">⏰ وقت الجلب</h4>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #333;">
                                    ${new Date().toLocaleTimeString('ar-SA')}
                                </p>
                            </div>
                        </div>

                        <h3>📋 البيانات الحقيقية من قاعدة البيانات:</h3>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto;">
                            <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">
${JSON.stringify(window.kushoofRealData, null, 2)}
                            </pre>
                        </div>

                        <div style="margin-top: 20px;">
                            <h3>🔍 تحليل البيانات:</h3>
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
                                <p><strong>عدد الطلاب:</strong> ${window.kushoofRealData.length}</p>
                                <p><strong>الحقول المتاحة:</strong> ${window.kushoofRealData.length > 0 ? Object.keys(window.kushoofRealData[0]).length : 0}</p>
                                <p><strong>أول طالب:</strong> ${window.kushoofRealData.length > 0 ? (window.kushoofRealData[0].name || window.kushoofRealData[0].student_name || 'غير محدد') : 'لا يوجد'}</p>
                                <p><strong>آخر طالب:</strong> ${window.kushoofRealData.length > 0 ? (window.kushoofRealData[window.kushoofRealData.length - 1].name || window.kushoofRealData[window.kushoofRealData.length - 1].student_name || 'غير محدد') : 'لا يوجد'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        };

        // تعريف دالة تصدير البيانات كاحتياط
        window.exportRealData = () => {
            if (!window.kushoofRealData || window.kushoofRealData.length === 0) {
                alert('❌ لا توجد بيانات حقيقية للتصدير');
                return;
            }

            console.log('📤 [Kushoof] تصدير البيانات الحقيقية (دالة احتياطية)');

            const exportData = {
                exportDate: new Date().toISOString(),
                source: 'قاعدة البيانات الحقيقية - kushoofapp.com',
                api: window.kushoofApiUrl,
                totalStudents: window.kushoofRealData.length,
                realDatabaseData: window.kushoofRealData,
                metadata: {
                    exportedBy: 'Kushoof Real Data Extension',
                    version: '2.0.0',
                    exportTime: new Date().toLocaleString('ar-SA'),
                    note: 'هذه بيانات حقيقية من قاعدة البيانات - تم جلب الأكواد من الموقع'
                }
            };

            const jsonString = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `kushoof-real-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert(`📊 تم تصدير البيانات الحقيقية بنجاح!\\n\\nعدد الطلاب: ${window.kushoofRealData.length}\\nالمصدر: kushoofapp.com\\nالملف: kushoof-real-data.json`);
        };

        console.log('✅ [Kushoof] تم تعريف الدوال الاحتياطية بنجاح');
    }

    showRealDataBackup() {
        if (!window.kushoofRealData || window.kushoofRealData.length === 0) {
            alert('❌ لا توجد بيانات حقيقية متاحة');
            return;
        }

        console.log('📊 [Kushoof] عرض البيانات الحقيقية (دالة احتياطية مباشرة)');

        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                width: 95%;
                height: 90%;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                display: flex;
                flex-direction: column;
            ">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <div>
                        <h1 style="margin: 0; font-size: 24px;">📊 البيانات الحقيقية من kushoofapp.com</h1>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">
                            تم جلب ${window.kushoofRealData.length} طالب • ${new Date().toLocaleString('ar-SA')}
                        </p>
                    </div>
                    <button onclick="this.closest('div[style*=\"position: fixed\"]').remove()" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 12px 16px;
                        border-radius: 50%;
                        cursor: pointer;
                        font-size: 18px;
                    ">✕</button>
                </div>

                <div style="flex: 1; padding: 20px; overflow-y: auto;">
                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                        <h3 style="margin: 0 0 10px 0; color: #155724;">✅ بيانات حقيقية من قاعدة البيانات</h3>
                        <p style="margin: 0; color: #155724;">
                            تم جلب البيانات مباشرة من قاعدة البيانات في kushoofapp.com
                        </p>
                        <p style="margin: 5px 0 0 0; color: #155724; font-size: 12px;">
                            API: ${window.kushoofApiUrl}
                        </p>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="margin: 0; color: #667eea;">📊 إجمالي الطلاب</h4>
                            <p style="margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #333;">
                                ${window.kushoofRealData.length}
                            </p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="margin: 0; color: #28a745;">🔗 المصدر</h4>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #333;">
                                قاعدة البيانات الحقيقية
                            </p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="margin: 0; color: #764ba2;">⏰ وقت الجلب</h4>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #333;">
                                ${new Date().toLocaleTimeString('ar-SA')}
                            </p>
                        </div>
                    </div>

                    <h3>📋 البيانات الحقيقية من قاعدة البيانات:</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto;">
                        <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">
${JSON.stringify(window.kushoofRealData, null, 2)}
                        </pre>
                    </div>

                    <div style="margin-top: 20px;">
                        <h3>🔍 تحليل البيانات:</h3>
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
                            <p><strong>عدد الطلاب:</strong> ${window.kushoofRealData.length}</p>
                            <p><strong>الحقول المتاحة:</strong> ${window.kushoofRealData.length > 0 ? Object.keys(window.kushoofRealData[0]).length : 0}</p>
                            <p><strong>أول طالب:</strong> ${window.kushoofRealData.length > 0 ? (window.kushoofRealData[0].name || window.kushoofRealData[0].student_name || 'غير محدد') : 'لا يوجد'}</p>
                            <p><strong>آخر طالب:</strong> ${window.kushoofRealData.length > 0 ? (window.kushoofRealData[window.kushoofRealData.length - 1].name || window.kushoofRealData[window.kushoofRealData.length - 1].student_name || 'غير محدد') : 'لا يوجد'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    exportRealDataBackup() {
        if (!window.kushoofRealData || window.kushoofRealData.length === 0) {
            alert('❌ لا توجد بيانات حقيقية للتصدير');
            return;
        }

        console.log('📤 [Kushoof] تصدير البيانات الحقيقية (دالة احتياطية مباشرة)');

        const exportData = {
            exportDate: new Date().toISOString(),
            source: 'قاعدة البيانات الحقيقية - kushoofapp.com',
            api: window.kushoofApiUrl,
            totalStudents: window.kushoofRealData.length,
            realDatabaseData: window.kushoofRealData,
            metadata: {
                exportedBy: 'Kushoof Real Data Extension',
                version: '2.0.0',
                exportTime: new Date().toLocaleString('ar-SA'),
                note: 'هذه بيانات حقيقية من قاعدة البيانات - دالة احتياطية'
            }
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `kushoof-real-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        alert(`📊 تم تصدير البيانات الحقيقية بنجاح!\\n\\nعدد الطلاب: ${window.kushoofRealData.length}\\nالمصدر: kushoofapp.com\\nالملف: kushoof-real-data.json`);
    }

    showSuccessNotice() {
        const notice = document.createElement('div');
        notice.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            z-index: 999998;
            font-family: Arial, sans-serif;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        `;

        notice.innerHTML = `
            ✅ الأكواد من الموقع + البيانات الحقيقية<br>
            <small>${this.realData ? this.realData.length : 0} طالب من kushoofapp.com</small>
        `;
        document.body.appendChild(notice);

        // إزالة الإشعار بعد 10 ثوان
        setTimeout(() => notice.remove(), 10000);
    }

    passDataToUI() {
        // تمرير البيانات للدوال المجلبة من الموقع
        window.kushoofRealData = this.realData;
        window.kushoofApiUrl = this.workingApiUrl;
        console.log('📊 [Kushoof] تم تمرير البيانات للواجهة المجلبة من الموقع');
    }


    

    

    

    

    
    showError() {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 999999;
            font-family: Arial, sans-serif;
            max-width: 300px;
        `;
        errorDiv.innerHTML = `
            ❌ فشل في الاتصال بـ kushoofapp.com<br>
            <small>تحقق من الاتصال بالإنترنت</small>
        `;
        document.body.appendChild(errorDiv);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => errorDiv.remove(), 5000);
    }
}

// تشغيل الإضافة
new KushoofRealDataExtension();
