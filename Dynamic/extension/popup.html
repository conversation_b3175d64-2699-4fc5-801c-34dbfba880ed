<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kushoof Dynamic Extension</title>
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }
        
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 20px;
        }
        
        .status {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .status-value {
            font-weight: bold;
            font-size: 14px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .actions {
            margin-top: 20px;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .btn-secondary:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
            text-align: center;
            font-size: 12px;
            opacity: 0.7;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Kushoof Dynamic</h1>
        <p>إضافة الكشوف الديناميكية</p>
    </div>
    
    <div class="content">
        <div class="status">
            <div class="status-item">
                <span class="status-label">حالة الاتصال:</span>
                <div style="display: flex; align-items: center;">
                    <span id="connection-status" class="status-value">فحص...</span>
                    <div id="connection-indicator" class="status-indicator"></div>
                </div>
            </div>
            
            <div class="status-item">
                <span class="status-label">إصدار الإضافة:</span>
                <span class="status-value">1.0.0</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">المكونات المحملة:</span>
                <span id="components-count" class="status-value">0</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">آخر تحديث:</span>
                <span id="last-update" class="status-value">--</span>
            </div>
        </div>
        
        <div class="actions">
            <button id="refresh-btn" class="btn btn-primary">
                🔄 تحديث المكونات
            </button>
            
            <button id="admin-panel-btn" class="btn btn-secondary">
                ⚙️ لوحة التحكم
            </button>
            
            <button id="test-connection-btn" class="btn btn-secondary">
                🔗 اختبار الاتصال
            </button>
            
            <button id="clear-cache-btn" class="btn btn-secondary">
                🗑️ مسح التخزين المؤقت
            </button>
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <div>جاري التحميل...</div>
        </div>
    </div>
    
    <div class="footer">
        Kushoof Dynamic Extension v1.0.0
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
