/**
 * 🚀 Kushoof Dynamic Extension Loader (Fixed Version)
 * المحمل الرئيسي للإضافة الديناميكية - نسخة محسنة
 */

class KushoofDynamicExtension {
    constructor() {
        this.apiBase = 'https://kushoofapp.com/js/api'; // API الموجود
        this.localApiBase = 'http://localhost/Dynamic/api'; // API المحلي
        this.version = '1.0.0';
        this.components = {};
        this.isLoaded = false;
        this.useLocalMode = false;
        
        console.log('🚀 [Dynamic] تهيئة الإضافة الديناميكية المحسنة');
        this.init();
    }
    
    /**
     * تهيئة الإضافة
     */
    async init() {
        try {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                await this.start();
            }
        } catch (error) {
            console.error('❌ [Dynamic] فشل في تهيئة الإضافة:', error);
            this.fallbackToLocalMode();
        }
    }
    
    /**
     * بدء تشغيل الإضافة
     */
    async start() {
        try {
            console.log('🔄 [Dynamic] بدء تحميل المكونات...');
            
            // تجربة تحميل المكونات من API
            const components = await this.fetchComponents();
            await this.loadComponents(components);
            
            // حقن الواجهة
            await this.injectUI();
            
            // ربط الأحداث
            this.bindEvents();
            
            this.isLoaded = true;
            console.log('✅ [Dynamic] تم تحميل الإضافة بنجاح');
            
        } catch (error) {
            console.error('❌ [Dynamic] فشل في تشغيل الإضافة:', error);
            this.fallbackToLocalMode();
        }
    }
    
    /**
     * جلب المكونات (مع دعم الوضع المحلي)
     */
    async fetchComponents() {
        try {
            // محاولة API المحلي أولاً
            let response = await fetch(`${this.localApiBase}/components.php`);
            
            if (!response.ok) {
                // إذا فشل، استخدم المكونات الافتراضية
                console.warn('⚠️ [Dynamic] API غير متاح، استخدام الوضع المحلي');
                this.useLocalMode = true;
                return this.getDefaultComponents();
            }
            
            const result = await response.json();
            if (result.success) {
                console.log('📦 [Dynamic] تم جلب المكونات من API');
                return result.data;
            }
            
            throw new Error('فشل في جلب المكونات');
            
        } catch (error) {
            console.warn('⚠️ [Dynamic] فشل في جلب المكونات:', error);
            this.useLocalMode = true;
            return this.getDefaultComponents();
        }
    }
    
    /**
     * تحميل المكونات
     */
    async loadComponents(components) {
        try {
            // تحميل CSS
            if (components.styles) {
                this.injectCSS(components.styles);
            }
            
            // حفظ القوالب
            if (components.templates) {
                this.templates = components.templates;
            }
            
            console.log('✅ [Dynamic] تم تحميل جميع المكونات');
            
        } catch (error) {
            console.error('❌ [Dynamic] فشل في تحميل المكونات:', error);
            throw error;
        }
    }
    
    /**
     * حقن CSS
     */
    injectCSS(styles) {
        const styleElement = document.createElement('style');
        styleElement.id = 'kushoof-dynamic-styles';
        styleElement.textContent = styles;
        document.head.appendChild(styleElement);
        console.log('🎨 [Dynamic] تم حقن CSS');
    }
    
    /**
     * حقن الواجهة
     */
    async injectUI() {
        try {
            const uiContainer = document.createElement('div');
            uiContainer.id = 'kushoof-dynamic-ui';
            uiContainer.innerHTML = this.templates?.mainUI || this.getDefaultUI();
            document.body.appendChild(uiContainer);
            console.log('🎨 [Dynamic] تم حقن الواجهة');
        } catch (error) {
            console.error('❌ [Dynamic] فشل في حقن الواجهة:', error);
        }
    }
    
    /**
     * ربط الأحداث
     */
    bindEvents() {
        try {
            // زر عرض البيانات
            const showDataBtn = document.getElementById('kushoof-show-data-btn');
            if (showDataBtn) {
                showDataBtn.addEventListener('click', () => this.createModalLocal());
            }
            
            // زر مصمم الكشوف
            const designerBtn = document.getElementById('kushoof-designer-btn');
            if (designerBtn) {
                designerBtn.addEventListener('click', () => this.createDesignerLocal());
            }
            
            // زر جمع البيانات
            const collectBtn = document.getElementById('kushoof-collect-btn');
            if (collectBtn) {
                collectBtn.addEventListener('click', () => this.collectDataLocal());
            }
            
            console.log('🔗 [Dynamic] تم ربط الأحداث');
            
        } catch (error) {
            console.error('❌ [Dynamic] فشل في ربط الأحداث:', error);
        }
    }
    
    /**
     * إنشاء موديل محلي
     */
    createModalLocal() {
        console.log('🎨 [Dynamic] إنشاء موديل محلي');
        
        const schoolData = this.getSchoolDataFromPage();
        
        const modal = document.createElement('div');
        modal.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 999999; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; width: 90%; height: 85%; border-radius: 15px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.3); display: flex; flex-direction: column;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center;">
                        <h2>📊 عرض البيانات الديناميكية</h2>
                        <button onclick="this.closest('div[style*=\"position: fixed\"]').remove()" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 10px 15px; border-radius: 50%; cursor: pointer; font-size: 18px;">✕</button>
                    </div>
                    <div style="flex: 1; padding: 20px; overflow-y: auto;">
                        <h3>🎉 مرحباً بك في الإضافة الديناميكية!</h3>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                            <h4>📊 بيانات المدرسة:</h4>
                            <p><strong>اسم المدرسة:</strong> ${schoolData.schoolName}</p>
                            <p><strong>اسم المدير:</strong> ${schoolData.managerName}</p>
                            <p><strong>عدد الطلاب:</strong> ${schoolData.studentsCount}</p>
                            <p><strong>معرف المدرسة:</strong> ${schoolData.schoolId}</p>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
                                <h4>✅ تحديث فوري</h4>
                                <p>تحديث المكونات دون إعادة تثبيت</p>
                            </div>
                            <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; border-left: 4px solid #764ba2;">
                                <h4>⚙️ إدارة مركزية</h4>
                                <p>لوحة تحكم شاملة</p>
                            </div>
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                                <h4>🎯 تخصيص متقدم</h4>
                                <p>تخصيص حسب المدرسة</p>
                            </div>
                            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                                <h4>🛡️ أمان محسن</h4>
                                <p>تشفير وحماية البيانات</p>
                            </div>
                        </div>
                        <button onclick="alert('تم النقر من الكود الديناميكي!')" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; font-weight: bold; margin-top: 20px;">
                            🚀 اختبار التفاعل
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }
    
    /**
     * إنشاء مصمم محلي
     */
    createDesignerLocal() {
        console.log('🎨 [Dynamic] إنشاء مصمم محلي');
        
        const schoolData = this.getSchoolDataFromPage();
        this.openAdvancedDesigner(schoolData);
    }
    
    /**
     * جمع البيانات محلياً
     */
    collectDataLocal() {
        console.log('📥 [Dynamic] جمع البيانات محلياً');
        this.collectSchoolData();
    }
    
    /**
     * التبديل للوضع المحلي
     */
    fallbackToLocalMode() {
        console.log('🔄 [Dynamic] التبديل للوضع المحلي...');
        this.useLocalMode = true;
        
        // إضافة واجهة بسيطة
        const fallbackUI = `
            <div style="position: fixed; top: 20px; right: 20px; z-index: 999999; background: #ff6b6b; color: white; padding: 10px; border-radius: 5px;">
                ⚠️ الوضع المحلي مفعل
                <br>
                <small>API غير متاح</small>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', fallbackUI);
        
        // إضافة الواجهة الأساسية
        setTimeout(() => {
            this.injectUI();
            this.bindEvents();
        }, 1000);
    }

    /**
     * جلب بيانات المدرسة من الصفحة
     */
    getSchoolDataFromPage() {
        const schoolId = getSchoolIdFromUrl();

        let schoolData = {
            schoolId: schoolId,
            schoolName: 'مدرسة غير محددة',
            managerName: 'مدير غير محدد',
            studentsCount: 0,
            students: []
        };

        try {
            // جلب اسم المدرسة من الصفحة
            const schoolNameElement = document.querySelector('h1, .school-name, [class*="school"], [class*="title"]');
            if (schoolNameElement) {
                schoolData.schoolName = schoolNameElement.textContent.trim();
            }

            // جلب اسم المدير
            const managerElement = document.querySelector('[class*="manager"], [class*="principal"], .manager-name');
            if (managerElement) {
                schoolData.managerName = managerElement.textContent.trim();
            }

            // جلب عدد الطلاب من الجداول
            const studentRows = document.querySelectorAll('table tbody tr, .student-row, [class*="student"]');
            schoolData.studentsCount = studentRows.length;

            // جلب البيانات من window.name إذا كانت موجودة
            if (window.name && window.name.includes('kushoof')) {
                const savedData = JSON.parse(window.name.split('kushoof')[1]);
                if (savedData.n && savedData.n.length > 0) {
                    schoolData.studentsCount = savedData.n.length;
                    schoolData.students = [];

                    for (let i = 0; i < savedData.n.length; i++) {
                        schoolData.students.push({
                            name: decodeURIComponent(savedData.n[i] || ''),
                            classLabel: decodeURIComponent(savedData.c[i] || ''),
                            parentPhone: savedData.p[i] || '',
                            studentPhone: savedData.sp ? savedData.sp[i] : '',
                            nationalId: savedData.nid ? savedData.nid[i] : '',
                            username: savedData.u ? savedData.u[i] : ''
                        });
                    }
                }
            }

        } catch (error) {
            console.warn('⚠️ [Dynamic] فشل في جلب بيانات المدرسة:', error);
        }

        return schoolData;
    }

    /**
     * جمع بيانات المدرسة
     */
    collectSchoolData() {
        console.log('📊 [Dynamic] بدء جمع بيانات المدرسة');

        const schoolId = getSchoolIdFromUrl();
        if (!schoolId) {
            alert('❌ لم يتم العثور على معرف المدرسة\n\nتأكد من أنك في صفحة مدرسة صحيحة في منصة مدرستي');
            return;
        }

        // إظهار رسالة التحميل
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'kushoof-loading';
        loadingDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 999999;
            text-align: center;
            font-family: Arial, sans-serif;
        `;
        loadingDiv.innerHTML = `
            <div style="font-size: 24px; margin-bottom: 15px;">📊</div>
            <h3 style="margin: 0 0 10px 0; color: #333;">جاري جمع البيانات...</h3>
            <p style="margin: 0; color: #666;">يرجى الانتظار</p>
            <div style="width: 200px; height: 4px; background: #f0f0f0; border-radius: 2px; margin: 15px auto; overflow: hidden;">
                <div style="width: 0%; height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); border-radius: 2px; animation: loading 2s ease-in-out infinite;"></div>
            </div>
            <style>
                @keyframes loading {
                    0% { width: 0%; }
                    50% { width: 70%; }
                    100% { width: 100%; }
                }
            </style>
        `;
        document.body.appendChild(loadingDiv);

        // محاكاة جمع البيانات
        setTimeout(() => {
            const schoolData = this.getSchoolDataFromPage();

            // حفظ البيانات في window.name
            const dataToSave = {
                schoolId: schoolData.schoolId,
                schoolName: schoolData.schoolName,
                managerName: schoolData.managerName,
                studentsCount: schoolData.studentsCount,
                timestamp: Date.now()
            };

            window.name = 'kushoof' + JSON.stringify(dataToSave);

            // إزالة شاشة التحميل
            loadingDiv.remove();

            // إظهار النتيجة
            alert(`✅ تم جمع البيانات بنجاح!\n\n📊 المدرسة: ${schoolData.schoolName}\n👨‍💼 المدير: ${schoolData.managerName}\n👥 عدد الطلاب: ${schoolData.studentsCount}\n🆔 معرف المدرسة: ${schoolData.schoolId}`);

            console.log('✅ [Dynamic] تم جمع البيانات:', schoolData);
        }, 2000);
    }

    /**
     * فتح مصمم الكشوف المتقدم
     */
    openAdvancedDesigner(schoolData) {
        console.log('🎨 [Dynamic] فتح مصمم الكشوف المتقدم');

        const modal = document.createElement('div');
        modal.id = 'kushoof-designer-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.95);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                width: 95%;
                height: 90%;
                border-radius: 15px;
                position: relative;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                display: flex;
                flex-direction: column;
            ">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px 30px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <div>
                        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">🎨 مصمم الكشوف المتقدم</h1>
                        <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 16px;">
                            ${schoolData.schoolName} • ${schoolData.studentsCount} طالب • ${schoolData.managerName}
                        </p>
                    </div>
                    <button onclick="document.getElementById('kushoof-designer-modal').remove()" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 12px 16px;
                        border-radius: 50%;
                        cursor: pointer;
                        font-size: 20px;
                        font-weight: bold;
                    ">✕</button>
                </div>

                <div style="flex: 1; padding: 20px; text-align: center;">
                    <h2>🎯 مصمم الكشوف الديناميكي</h2>
                    <p>هذا المصمم يتم تحديثه ديناميكياً من قاعدة البيانات</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #667eea;">
                            <h3>📋 أنواع الكشوف</h3>
                            <ul style="text-align: right; margin: 15px 0;">
                                <li>كشف حضور وغياب</li>
                                <li>كشف درجات الطلاب</li>
                                <li>كشف أرقام الهواتف</li>
                                <li>قائمة الطلاب الكاملة</li>
                            </ul>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #764ba2;">
                            <h3>⚙️ المميزات</h3>
                            <ul style="text-align: right; margin: 15px 0;">
                                <li>اختيار الحقول المطلوبة</li>
                                <li>تصفية الفصول والصفوف</li>
                                <li>تخصيص التصميم والألوان</li>
                                <li>تصدير وطباعة متقدمة</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin-top: 30px;">
                        <button onclick="alert('🚀 سيتم تطوير هذه الميزة قريباً من لوحة التحكم!')" style="
                            background: linear-gradient(135deg, #28a745, #20c997);
                            color: white;
                            border: none;
                            padding: 15px 30px;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            margin: 0 10px;
                        ">🎨 بدء التصميم</button>

                        <button onclick="alert('📊 سيتم إضافة المعاينة المباشرة قريباً!')" style="
                            background: linear-gradient(135deg, #007bff, #0056b3);
                            color: white;
                            border: none;
                            padding: 15px 30px;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            margin: 0 10px;
                        ">👁️ معاينة</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * الحصول على مكونات افتراضية
     */
    getDefaultComponents() {
        return {
            styles: this.getDefaultCSS(),
            templates: {
                mainUI: this.getDefaultUI()
            },
            metadata: {
                total_components: 1,
                last_update: new Date().toISOString(),
                version: this.version,
                source: 'local'
            }
        };
    }

    /**
     * CSS افتراضي
     */
    getDefaultCSS() {
        return `
            .kushoof-dynamic-ui {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 999999;
                font-family: Arial, sans-serif;
            }

            .kushoof-btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                cursor: pointer;
                margin: 5px;
                font-size: 14px;
                font-weight: bold;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .kushoof-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            }
        `;
    }

    /**
     * الواجهة الافتراضية
     */
    getDefaultUI() {
        return `
            <div class="kushoof-dynamic-ui">
                <button id="kushoof-show-data-btn" class="kushoof-btn">
                    📊 عرض البيانات
                </button>
                <button id="kushoof-designer-btn" class="kushoof-btn">
                    🎨 مصمم الكشوف
                </button>
                <button id="kushoof-collect-btn" class="kushoof-btn">
                    📥 جمع البيانات
                </button>
            </div>
        `;
    }
}

/**
 * 🔍 جلب معرف المدرسة من URL
 */
function getSchoolIdFromUrl() {
    const currentUrl = window.location.href;
    console.log('🔍 [Dynamic] البحث في الرابط:', currentUrl);

    const patterns = [
        /SchoolId=([A-F0-9]{32})/i,
        /Index\/([A-F0-9]{32})/i,
        /\/([A-F0-9]{32})/i,
        /schoolId[=\/]([A-F0-9]{32})/i,
        /Schools\/([A-F0-9]{32})/i,
        /ManageStudents\/([A-F0-9]{32})/i
    ];

    for (const pattern of patterns) {
        const match = currentUrl.match(pattern);
        if (match) {
            console.log('✅ [Dynamic] تم العثور على معرف المدرسة:', match[1]);
            return match[1];
        }
    }

    console.log('❌ [Dynamic] لم يتم العثور على معرف المدرسة');
    return null;
}

/**
 * 📡 إرسال البيانات للخادم
 */
async function sendDataToServer(data, endpoint = 'save-data.php') {
    try {
        const response = await fetch(`http://localhost/Dynamic/api/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            console.log('✅ [Dynamic] تم إرسال البيانات بنجاح:', result);
            return result;
        } else {
            throw new Error(result.message);
        }

    } catch (error) {
        console.error('❌ [Dynamic] فشل في إرسال البيانات:', error);
        throw error;
    }
}

// إتاحة الدوال للاستخدام العام
window.KushoofDynamic = {
    sendDataToServer,
    getSchoolIdFromUrl
};

// تشغيل الإضافة
if (typeof window !== 'undefined') {
    new KushoofDynamicExtension();
}
