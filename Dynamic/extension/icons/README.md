# 🎨 أيقونات الإضافة

## 📋 الأيقونات المطلوبة

يجب إضافة الأيقونات التالية لهذا المجلد:

### الأحجام المطلوبة:
- `icon16.png` - 16x16 بكسل
- `icon32.png` - 32x32 بكسل  
- `icon48.png` - 48x48 بكسل
- `icon128.png` - 128x128 بكسل

## 🎯 التصميم المقترح

### الألوان:
- **اللون الأساسي**: #667eea (أزرق بنفسجي)
- **اللون الثانوي**: #764ba2 (بنفسجي)
- **الخلفية**: شفافة أو بيضاء

### العناصر:
- رمز الكشوف (📊 أو 📋)
- حر<PERSON> <PERSON> من Kushoof
- تدرج لوني جميل

## 🛠️ إنشاء الأيقونات

يمكنك استخدام أي من الأدوات التالية:

### أدوات مجانية:
- **GIMP** - محرر صور مجاني
- **Canva** - تصميم أونلاين
- **Figma** - تصميم واجهات
- **Paint.NET** - محرر بسيط

### أدوات احترافية:
- **Adobe Photoshop**
- **Adobe Illustrator**
- **Sketch** (Mac)

## 📐 مواصفات التصميم

### icon16.png (16x16)
- استخدم رمز بسيط
- تجنب التفاصيل الدقيقة
- ألوان واضحة ومتباينة

### icon32.png (32x32)
- يمكن إضافة تفاصيل أكثر
- نص واضح إذا لزم الأمر

### icon48.png (48x48)
- التصميم الأساسي للإضافة
- يظهر في قائمة الإضافات

### icon128.png (128x128)
- أعلى جودة وتفاصيل
- يستخدم في متجر Chrome

## 🎨 مثال على التصميم

```
┌─────────────────┐
│  ┌───┐ ┌───┐    │
│  │ K │ │ 📊 │   │  
│  └───┘ └───┘    │
│                 │
│   KUSHOOF       │
│   DYNAMIC       │
└─────────────────┘
```

## 🔄 تحديث الأيقونات

بعد إضافة الأيقونات:

1. **إعادة تحميل الإضافة** في Chrome
2. **التحقق من الظهور** في شريط الأدوات
3. **اختبار الأحجام** المختلفة

## 📝 ملاحظات

- تأكد من أن الأيقونات بصيغة PNG
- استخدم خلفية شفافة للمظهر الأفضل
- احرص على وضوح الرمز في جميع الأحجام
- تجنب النصوص الطويلة في الأحجام الصغيرة

---

**💡 نصيحة**: يمكنك البدء بتصميم الأيقونة الكبيرة (128x128) ثم تصغيرها للأحجام الأخرى.
