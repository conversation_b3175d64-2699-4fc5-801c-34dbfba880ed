/**
 * 🚀 Kushoof Dynamic Extension Loader
 * المحمل الرئيسي للإضافة الديناميكية
 */

class KushoofDynamicExtension {
    constructor() {
        this.apiBase = 'http://localhost/Dynamic/api';
        this.version = '1.0.0';
        this.components = {};
        this.isLoaded = false;
        
        console.log('🚀 [Dynamic] تهيئة الإضافة الديناميكية');
        this.init();
    }
    
    /**
     * تهيئة الإضافة
     */
    async init() {
        try {
            // التحقق من أن الصفحة جاهزة
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                await this.start();
            }
        } catch (error) {
            console.error('❌ [Dynamic] فشل في تهيئة الإضافة:', error);
        }
    }
    
    /**
     * بدء تشغيل الإضافة
     */
    async start() {
        try {
            console.log('🔄 [Dynamic] بدء تحميل المكونات...');
            
            // جلب إعدادات الإضافة
            const config = await this.fetchConfig();
            
            // جلب المكونات من قاعدة البيانات
            const components = await this.fetchComponents();
            
            // تحميل المكونات
            await this.loadComponents(components);
            
            // حقن الواجهة في الصفحة
            await this.injectUI();
            
            // ربط الأحداث
            this.bindEvents();
            
            this.isLoaded = true;
            console.log('✅ [Dynamic] تم تحميل الإضافة بنجاح');
            
        } catch (error) {
            console.error('❌ [Dynamic] فشل في تشغيل الإضافة:', error);
            this.fallbackToStaticMode();
        }
    }
    
    /**
     * جلب إعدادات الإضافة
     */
    async fetchConfig() {
        try {
            const response = await fetch(`${this.apiBase}/config.php`);
            const config = await response.json();
            
            if (config.success) {
                console.log('⚙️ [Dynamic] تم جلب الإعدادات:', config.data);
                return config.data;
            } else {
                throw new Error(config.message);
            }
        } catch (error) {
            console.warn('⚠️ [Dynamic] فشل في جلب الإعدادات:', error);
            return this.getDefaultConfig();
        }
    }
    
    /**
     * جلب المكونات من قاعدة البيانات
     */
    async fetchComponents() {
        try {
            const response = await fetch(`${this.apiBase}/components.php`);
            const result = await response.json();
            
            if (result.success) {
                console.log('📦 [Dynamic] تم جلب المكونات:', Object.keys(result.data));
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.warn('⚠️ [Dynamic] فشل في جلب المكونات:', error);
            return this.getDefaultComponents();
        }
    }
    
    /**
     * تحميل المكونات
     */
    async loadComponents(components) {
        try {
            // تحميل CSS
            if (components.styles) {
                this.injectCSS(components.styles);
            }
            
            // حفظ القوالب
            if (components.templates) {
                this.templates = components.templates;
            }
            
            // تحميل JavaScript بشكل آمن
            if (components.scripts) {
                await this.loadJavaScript(components.scripts);
            }
            
            console.log('✅ [Dynamic] تم تحميل جميع المكونات');
            
        } catch (error) {
            console.error('❌ [Dynamic] فشل في تحميل المكونات:', error);
            throw error;
        }
    }
    
    /**
     * حقن CSS في الصفحة
     */
    injectCSS(styles) {
        const styleElement = document.createElement('style');
        styleElement.id = 'kushoof-dynamic-styles';
        styleElement.textContent = styles;
        document.head.appendChild(styleElement);
        
        console.log('🎨 [Dynamic] تم حقن CSS');
    }
    
    /**
     * تحميل JavaScript بشكل آمن
     */
    async loadJavaScript(scripts) {
        return new Promise((resolve, reject) => {
            try {
                // إنشاء iframe معزول لتنفيذ الكود
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.sandbox = 'allow-scripts allow-same-origin';
                iframe.id = 'kushoof-dynamic-sandbox';
                
                // إعداد محتوى الـ iframe
                const scriptContent = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                    </head>
                    <body>
                        <script>
                            try {
                                // تنفيذ الكود المجلب من قاعدة البيانات
                                ${scripts}
                                
                                // إرسال الدوال للصفحة الرئيسية
                                window.parent.postMessage({
                                    type: 'KUSHOOF_FUNCTIONS_LOADED',
                                    functions: {
                                        createModal: typeof createModal !== 'undefined' ? createModal.toString() : null,
                                        createDesigner: typeof createDesigner !== 'undefined' ? createDesigner.toString() : null,
                                        exportData: typeof exportData !== 'undefined' ? exportData.toString() : null,
                                        collectData: typeof collectData !== 'undefined' ? collectData.toString() : null
                                    },
                                    timestamp: Date.now()
                                }, '*');
                                
                            } catch (error) {
                                window.parent.postMessage({
                                    type: 'KUSHOOF_SCRIPT_ERROR',
                                    error: error.message,
                                    timestamp: Date.now()
                                }, '*');
                            }
                        </script>
                    </body>
                    </html>
                `;
                
                iframe.srcdoc = scriptContent;
                document.body.appendChild(iframe);
                
                // انتظار تحميل الدوال
                const messageHandler = (event) => {
                    if (event.data.type === 'KUSHOOF_FUNCTIONS_LOADED') {
                        this.remoteFunctions = event.data.functions;
                        window.removeEventListener('message', messageHandler);
                        console.log('📜 [Dynamic] تم تحميل الدوال:', Object.keys(this.remoteFunctions));
                        resolve();
                    } else if (event.data.type === 'KUSHOOF_SCRIPT_ERROR') {
                        window.removeEventListener('message', messageHandler);
                        console.error('❌ [Dynamic] خطأ في تنفيذ الكود:', event.data.error);
                        reject(new Error(event.data.error));
                    }
                };
                
                window.addEventListener('message', messageHandler);
                
                // timeout للأمان
                setTimeout(() => {
                    window.removeEventListener('message', messageHandler);
                    reject(new Error('انتهت مهلة تحميل الدوال'));
                }, 10000);
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * حقن الواجهة في الصفحة
     */
    async injectUI() {
        try {
            // إنشاء حاوي الواجهة
            const uiContainer = document.createElement('div');
            uiContainer.id = 'kushoof-dynamic-ui';
            uiContainer.innerHTML = this.templates?.mainUI || this.getDefaultUI();
            
            // إضافة الواجهة للصفحة
            document.body.appendChild(uiContainer);
            
            console.log('🎨 [Dynamic] تم حقن الواجهة');
            
        } catch (error) {
            console.error('❌ [Dynamic] فشل في حقن الواجهة:', error);
        }
    }
    
    /**
     * ربط الأحداث
     */
    bindEvents() {
        try {
            // زر عرض البيانات
            const showDataBtn = document.getElementById('kushoof-show-data-btn');
            if (showDataBtn) {
                showDataBtn.addEventListener('click', () => this.executeFunction('createModal'));
            }
            
            // زر مصمم الكشوف
            const designerBtn = document.getElementById('kushoof-designer-btn');
            if (designerBtn) {
                designerBtn.addEventListener('click', () => this.executeFunction('createDesigner'));
            }
            
            // زر جمع البيانات
            const collectBtn = document.getElementById('kushoof-collect-btn');
            if (collectBtn) {
                collectBtn.addEventListener('click', () => this.executeFunction('collectData'));
            }
            
            console.log('🔗 [Dynamic] تم ربط الأحداث');
            
        } catch (error) {
            console.error('❌ [Dynamic] فشل في ربط الأحداث:', error);
        }
    }
    
    /**
     * تنفيذ دالة مجلبة من قاعدة البيانات
     */
    executeFunction(functionName) {
        try {
            if (!this.remoteFunctions || !this.remoteFunctions[functionName]) {
                console.warn(`⚠️ [Dynamic] الدالة غير موجودة: ${functionName}`);
                return;
            }
            
            console.log(`🚀 [Dynamic] تنفيذ الدالة: ${functionName}`);
            
            // إنشاء الدالة وتنفيذها
            const functionCode = this.remoteFunctions[functionName];
            const func = new Function('return ' + functionCode)();
            
            // تنفيذ الدالة مع معالجة الأخطاء
            const result = func();
            
            console.log(`✅ [Dynamic] تم تنفيذ ${functionName} بنجاح`);
            return result;
            
        } catch (error) {
            console.error(`❌ [Dynamic] فشل في تنفيذ ${functionName}:`, error);
        }
    }
    
    /**
     * الوضع الاحتياطي (Static Mode)
     */
    fallbackToStaticMode() {
        console.log('🔄 [Dynamic] التبديل للوضع الاحتياطي...');
        
        // إضافة واجهة بسيطة
        const fallbackUI = `
            <div style="position: fixed; top: 20px; right: 20px; z-index: 999999; background: #ff6b6b; color: white; padding: 10px; border-radius: 5px;">
                ⚠️ فشل في تحميل الإضافة الديناميكية
                <br>
                <small>يتم استخدام الوضع الاحتياطي</small>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', fallbackUI);
    }
    
    /**
     * الحصول على إعدادات افتراضية
     */
    getDefaultConfig() {
        return {
            theme: 'blue',
            features: ['modal', 'designer', 'export'],
            version: this.version
        };
    }
    
    /**
     * الحصول على مكونات افتراضية
     */
    getDefaultComponents() {
        return {
            styles: `
                .kushoof-btn {
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin: 5px;
                }
                .kushoof-btn:hover {
                    background: #0056b3;
                }
            `,
            templates: {
                mainUI: this.getDefaultUI()
            },
            scripts: `
                function createModal() {
                    alert('مرحباً من الوضع الافتراضي!');
                }
                function createDesigner() {
                    alert('مصمم الكشوف - الوضع الافتراضي');
                }
                function collectData() {
                    alert('جمع البيانات - الوضع الافتراضي');
                }
            `
        };
    }
    
    /**
     * الحصول على واجهة افتراضية
     */
    getDefaultUI() {
        return `
            <div style="position: fixed; top: 20px; right: 20px; z-index: 999999;">
                <button id="kushoof-show-data-btn" class="kushoof-btn">📊 عرض البيانات</button>
                <button id="kushoof-designer-btn" class="kushoof-btn">🎨 مصمم الكشوف</button>
                <button id="kushoof-collect-btn" class="kushoof-btn">📥 جمع البيانات</button>
            </div>
        `;
    }
}

// تشغيل الإضافة
if (typeof window !== 'undefined') {
    new KushoofDynamicExtension();
}

/**
 * 📡 إرسال البيانات للخادم
 */
async function sendDataToServer(data, endpoint = 'save-data.php') {
    try {
        const response = await fetch(`http://localhost/Dynamic/api/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            console.log('✅ [Dynamic] تم إرسال البيانات بنجاح:', result);
            return result;
        } else {
            throw new Error(result.message);
        }

    } catch (error) {
        console.error('❌ [Dynamic] فشل في إرسال البيانات:', error);
        throw error;
    }
}

/**
 * 🔍 جلب معرف المدرسة من URL
 */
function getSchoolIdFromUrl() {
    const currentUrl = window.location.href;
    console.log('🔍 [Dynamic] البحث في الرابط:', currentUrl);

    const patterns = [
        /SchoolId=([A-F0-9]{32})/i,
        /Index\/([A-F0-9]{32})/i,
        /\/([A-F0-9]{32})/i,
        /schoolId[=\/]([A-F0-9]{32})/i,
        /Schools\/([A-F0-9]{32})/i,
        /ManageStudents\/([A-F0-9]{32})/i
    ];

    for (const pattern of patterns) {
        const match = currentUrl.match(pattern);
        if (match) {
            console.log('✅ [Dynamic] تم العثور على معرف المدرسة:', match[1]);
            return match[1];
        }
    }

    console.log('❌ [Dynamic] لم يتم العثور على معرف المدرسة');
    return null;
}

// إتاحة الدوال للاستخدام العام
window.KushoofDynamic = {
    sendDataToServer,
    getSchoolIdFromUrl
};
