/**
 * 🔧 Kushoof Dynamic Extension Background Script
 * خدمات الخلفية للإضافة الديناميكية
 */

console.log('🚀 [Background] تشغيل خدمات الخلفية للإضافة الديناميكية');

/**
 * معالج تثبيت الإضافة
 */
chrome.runtime.onInstalled.addListener((details) => {
    console.log('📦 [Background] تم تثبيت الإضافة:', details.reason);
    
    if (details.reason === 'install') {
        // إعدادات أولية عند التثبيت
        chrome.storage.local.set({
            'kushoof_dynamic_version': '1.0.0',
            'kushoof_dynamic_installed': Date.now(),
            'kushoof_api_base': 'http://localhost/Dynamic/api'
        });
        
        console.log('✅ [Background] تم حفظ الإعدادات الأولية');
    }
});

/**
 * معالج الرسائل من content scripts
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 [Background] استلام رسالة:', request.action);
    
    switch (request.action) {
        case 'saveToDatabase':
            handleSaveToDatabase(request.data)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // للاستجابة غير المتزامنة
            
        case 'fetchFromDatabase':
            handleFetchFromDatabase(request.query)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        case 'updateComponent':
            handleUpdateComponent(request.component)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        case 'getSettings':
            handleGetSettings()
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        default:
            console.warn('⚠️ [Background] إجراء غير معروف:', request.action);
            sendResponse({ success: false, error: 'إجراء غير معروف' });
    }
});

/**
 * حفظ البيانات في قاعدة البيانات
 */
async function handleSaveToDatabase(data) {
    try {
        console.log('💾 [Background] حفظ البيانات في قاعدة البيانات');
        
        const settings = await chrome.storage.local.get(['kushoof_api_base']);
        const apiBase = settings.kushoof_api_base || 'http://localhost/Dynamic/api';
        
        const response = await fetch(`${apiBase}/save-data.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: 'SCHOOL_DATA',
                data: data,
                timestamp: Date.now(),
                source: 'kushoof_dynamic_extension'
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('✅ [Background] تم حفظ البيانات:', result);
        
        return {
            success: true,
            data: result,
            message: 'تم حفظ البيانات بنجاح'
        };
        
    } catch (error) {
        console.error('❌ [Background] فشل في حفظ البيانات:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * جلب البيانات من قاعدة البيانات
 */
async function handleFetchFromDatabase(query) {
    try {
        console.log('📥 [Background] جلب البيانات من قاعدة البيانات');
        
        const settings = await chrome.storage.local.get(['kushoof_api_base']);
        const apiBase = settings.kushoof_api_base || 'http://localhost/Dynamic/api';
        
        const response = await fetch(`${apiBase}/fetch-data.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(query)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('✅ [Background] تم جلب البيانات:', result);
        
        return {
            success: true,
            data: result,
            message: 'تم جلب البيانات بنجاح'
        };
        
    } catch (error) {
        console.error('❌ [Background] فشل في جلب البيانات:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * تحديث مكون في قاعدة البيانات
 */
async function handleUpdateComponent(component) {
    try {
        console.log('🔄 [Background] تحديث المكون:', component.name);
        
        const settings = await chrome.storage.local.get(['kushoof_api_base']);
        const apiBase = settings.kushoof_api_base || 'http://localhost/Dynamic/api';
        
        const response = await fetch(`${apiBase}/update-component.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(component)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('✅ [Background] تم تحديث المكون:', result);
        
        return {
            success: true,
            data: result,
            message: 'تم تحديث المكون بنجاح'
        };
        
    } catch (error) {
        console.error('❌ [Background] فشل في تحديث المكون:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * جلب الإعدادات
 */
async function handleGetSettings() {
    try {
        const settings = await chrome.storage.local.get();
        console.log('⚙️ [Background] الإعدادات الحالية:', settings);
        
        return {
            success: true,
            data: settings,
            message: 'تم جلب الإعدادات بنجاح'
        };
        
    } catch (error) {
        console.error('❌ [Background] فشل في جلب الإعدادات:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * معالج أخطاء غير متوقعة
 */
self.addEventListener('error', (event) => {
    console.error('❌ [Background] خطأ غير متوقع:', event.error);
});

/**
 * معالج إغلاق الإضافة
 */
chrome.runtime.onSuspend.addListener(() => {
    console.log('🔄 [Background] إيقاف خدمات الخلفية');
});

console.log('✅ [Background] تم تحميل خدمات الخلفية بنجاح');
