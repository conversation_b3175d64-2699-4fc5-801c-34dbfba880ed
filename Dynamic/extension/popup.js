/**
 * 🎛️ Kushoof Dynamic Extension Popup Script
 * سكريبت النافذة المنبثقة للإضافة
 */

document.addEventListener('DOMContentLoaded', async () => {
    console.log('🎛️ [Popup] تحميل النافذة المنبثقة');
    
    // تهيئة الواجهة
    await initializePopup();
    
    // ربط الأحداث
    bindEvents();
    
    // فحص الحالة
    await checkStatus();
});

/**
 * تهيئة النافذة المنبثقة
 */
async function initializePopup() {
    try {
        // جلب الإعدادات المحفوظة
        const settings = await chrome.storage.local.get();
        
        // تحديث معلومات آخر تحديث
        if (settings.kushoof_last_update) {
            const lastUpdate = new Date(settings.kushoof_last_update);
            document.getElementById('last-update').textContent = lastUpdate.toLocaleString('ar-SA');
        }
        
        console.log('✅ [Popup] تم تهيئة النافذة المنبثقة');
        
    } catch (error) {
        console.error('❌ [Popup] فشل في تهيئة النافذة المنبثقة:', error);
    }
}

/**
 * ربط الأحداث
 */
function bindEvents() {
    // زر تحديث المكونات
    document.getElementById('refresh-btn').addEventListener('click', refreshComponents);
    
    // زر لوحة التحكم
    document.getElementById('admin-panel-btn').addEventListener('click', openAdminPanel);
    
    // زر اختبار الاتصال
    document.getElementById('test-connection-btn').addEventListener('click', testConnection);
    
    // زر مسح التخزين المؤقت
    document.getElementById('clear-cache-btn').addEventListener('click', clearCache);
    
    console.log('🔗 [Popup] تم ربط الأحداث');
}

/**
 * فحص حالة الإضافة
 */
async function checkStatus() {
    try {
        showLoading(true);
        
        // فحص الاتصال بـ API
        const connectionStatus = await testAPIConnection();
        updateConnectionStatus(connectionStatus);
        
        // جلب عدد المكونات
        const componentsCount = await getComponentsCount();
        document.getElementById('components-count').textContent = componentsCount;
        
        showLoading(false);
        
    } catch (error) {
        console.error('❌ [Popup] فشل في فحص الحالة:', error);
        updateConnectionStatus(false);
        showLoading(false);
    }
}

/**
 * تحديث المكونات
 */
async function refreshComponents() {
    try {
        showLoading(true);
        
        // إرسال رسالة لـ content script لتحديث المكونات
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        if (tab && tab.url.includes('schools.madrasati.sa')) {
            // إعادة تحميل content script
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: () => {
                    // إعادة تحميل الإضافة الديناميكية
                    if (window.KushoofDynamicExtension) {
                        location.reload();
                    }
                }
            });
            
            // تحديث الوقت
            await chrome.storage.local.set({
                'kushoof_last_update': Date.now()
            });
            
            showMessage('✅ تم تحديث المكونات بنجاح', 'success');
            
        } else {
            showMessage('⚠️ يرجى فتح موقع مدرستي أولاً', 'warning');
        }
        
        showLoading(false);
        
    } catch (error) {
        console.error('❌ [Popup] فشل في تحديث المكونات:', error);
        showMessage('❌ فشل في تحديث المكونات', 'error');
        showLoading(false);
    }
}

/**
 * فتح لوحة التحكم
 */
function openAdminPanel() {
    chrome.tabs.create({
        url: 'http://localhost/Dynamic/admin/index.html'
    });
}

/**
 * اختبار الاتصال
 */
async function testConnection() {
    try {
        showLoading(true);
        
        const isConnected = await testAPIConnection();
        
        if (isConnected) {
            showMessage('✅ الاتصال يعمل بشكل طبيعي', 'success');
        } else {
            showMessage('❌ فشل في الاتصال بالخادم', 'error');
        }
        
        updateConnectionStatus(isConnected);
        showLoading(false);
        
    } catch (error) {
        console.error('❌ [Popup] فشل في اختبار الاتصال:', error);
        showMessage('❌ خطأ في اختبار الاتصال', 'error');
        updateConnectionStatus(false);
        showLoading(false);
    }
}

/**
 * مسح التخزين المؤقت
 */
async function clearCache() {
    try {
        showLoading(true);
        
        // مسح التخزين المحلي
        await chrome.storage.local.clear();
        
        // إعادة تعيين الإعدادات الأساسية
        await chrome.storage.local.set({
            'kushoof_dynamic_version': '1.0.0',
            'kushoof_api_base': 'http://localhost/Dynamic/api'
        });
        
        showMessage('✅ تم مسح التخزين المؤقت', 'success');
        
        // إعادة تحميل الحالة
        await checkStatus();
        
        showLoading(false);
        
    } catch (error) {
        console.error('❌ [Popup] فشل في مسح التخزين المؤقت:', error);
        showMessage('❌ فشل في مسح التخزين المؤقت', 'error');
        showLoading(false);
    }
}

/**
 * اختبار الاتصال بـ API
 */
async function testAPIConnection() {
    try {
        const response = await fetch('http://localhost/Dynamic/api/config.php', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        return response.ok;
        
    } catch (error) {
        console.error('❌ [Popup] فشل في اختبار الاتصال:', error);
        return false;
    }
}

/**
 * جلب عدد المكونات
 */
async function getComponentsCount() {
    try {
        const response = await fetch('http://localhost/Dynamic/api/components.php');
        const result = await response.json();
        
        if (result.success && result.data) {
            return Object.keys(result.data).length;
        }
        
        return 0;
        
    } catch (error) {
        console.error('❌ [Popup] فشل في جلب عدد المكونات:', error);
        return 0;
    }
}

/**
 * تحديث حالة الاتصال
 */
function updateConnectionStatus(isConnected) {
    const statusElement = document.getElementById('connection-status');
    const indicatorElement = document.getElementById('connection-indicator');
    
    if (isConnected) {
        statusElement.textContent = 'متصل';
        indicatorElement.className = 'status-indicator status-online';
    } else {
        statusElement.textContent = 'غير متصل';
        indicatorElement.className = 'status-indicator status-offline';
    }
}

/**
 * إظهار/إخفاء شاشة التحميل
 */
function showLoading(show) {
    const loadingElement = document.getElementById('loading');
    const contentElement = document.querySelector('.content .actions');
    
    if (show) {
        loadingElement.style.display = 'block';
        contentElement.style.opacity = '0.5';
    } else {
        loadingElement.style.display = 'none';
        contentElement.style.opacity = '1';
    }
}

/**
 * إظهار رسالة
 */
function showMessage(message, type = 'info') {
    // إنشاء عنصر الرسالة
    const messageElement = document.createElement('div');
    messageElement.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        text-align: center;
        z-index: 1000;
        animation: slideDown 0.3s ease;
    `;
    
    // تحديد لون الرسالة حسب النوع
    switch (type) {
        case 'success':
            messageElement.style.background = 'rgba(40, 167, 69, 0.9)';
            break;
        case 'error':
            messageElement.style.background = 'rgba(220, 53, 69, 0.9)';
            break;
        case 'warning':
            messageElement.style.background = 'rgba(255, 193, 7, 0.9)';
            messageElement.style.color = '#000';
            break;
        default:
            messageElement.style.background = 'rgba(23, 162, 184, 0.9)';
    }
    
    messageElement.textContent = message;
    document.body.appendChild(messageElement);
    
    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (messageElement.parentNode) {
            messageElement.parentNode.removeChild(messageElement);
        }
    }, 3000);
}

// إضافة CSS للرسائل
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
