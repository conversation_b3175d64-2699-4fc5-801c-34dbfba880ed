/**
 * 🚀 Kushoof Simple Dynamic Extension
 * إضافة بسيطة تجلب كل شيء من قاعدة البيانات
 */

class SimpleDynamicExtension {
    constructor() {
        this.apiUrl = 'http://localhost/Dynamic/api-simple/get-extension.php';
        this.backupApiUrl = 'https://kushoofapp.com/js/api/receive-data.php';

        console.log('🚀 [Simple] بدء الإضافة البسيطة - كل شيء من قاعدة البيانات');
        this.init();
    }
    
    async init() {
        try {
            // جلب كل شيء من قاعدة البيانات
            const components = await this.fetchFromDatabase();
            
            // تطبيق ما تم جلبه
            this.applyComponents(components);
            
            console.log('✅ [Simple] تم تحميل الإضافة من قاعدة البيانات');
            
        } catch (error) {
            console.error('❌ [Simple] فشل في تحميل من قاعدة البيانات:', error);
            this.showError();
        }
    }
    
    async fetchFromDatabase() {
        try {
            console.log('📡 [Simple] جلب كل شيء من قاعدة البيانات...');

            // محاولة جلب من API الرئيسي
            let response = await fetch(this.apiUrl);

            if (!response.ok) {
                console.warn('⚠️ [Simple] API الرئيسي غير متاح، جرب الاحتياطي');
                response = await fetch(this.backupApiUrl);
            }

            if (response.ok) {
                const data = await response.json();

                if (data.success) {
                    console.log('✅ [Simple] تم جلب كل شيء من قاعدة البيانات:', data.metadata);
                    return data;
                } else {
                    throw new Error(data.message || 'فشل في جلب البيانات');
                }
            }

            throw new Error('فشل في الاتصال بجميع APIs');

        } catch (error) {
            console.error('❌ [Simple] خطأ في جلب البيانات:', error);
            throw error;
        }
    }
    
    applyComponents(components) {
        try {
            // تطبيق CSS من قاعدة البيانات
            if (components.styles) {
                this.injectCSS(components.styles);
            }
            
            // تطبيق HTML من قاعدة البيانات
            if (components.templates && components.templates.mainUI) {
                this.injectHTML(components.templates.mainUI);
            }
            
            // تطبيق JavaScript من قاعدة البيانات
            if (components.scripts) {
                this.executeScripts(components.scripts);
            }
            
            console.log('✅ [Simple] تم تطبيق جميع المكونات من قاعدة البيانات');
            
        } catch (error) {
            console.error('❌ [Simple] فشل في تطبيق المكونات:', error);
        }
    }
    
    injectCSS(cssCode) {
        const style = document.createElement('style');
        style.textContent = cssCode;
        document.head.appendChild(style);
        console.log('🎨 [Simple] تم حقن CSS من قاعدة البيانات');
    }
    
    injectHTML(htmlCode) {
        const container = document.createElement('div');
        container.innerHTML = htmlCode;
        document.body.appendChild(container);
        console.log('🏗️ [Simple] تم حقن HTML من قاعدة البيانات');
    }
    
    executeScripts(jsCode) {
        try {
            // إنشاء script element لتنفيذ الكود من قاعدة البيانات
            const script = document.createElement('script');
            script.textContent = jsCode;
            document.head.appendChild(script);
            console.log('⚡ [Simple] تم تنفيذ JavaScript من قاعدة البيانات');
            
        } catch (error) {
            console.error('❌ [Simple] فشل في تنفيذ JavaScript:', error);
        }
    }
    
    showError() {
        // عرض رسالة خطأ بسيطة
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4757;
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 999999;
            font-family: Arial, sans-serif;
        `;
        errorDiv.innerHTML = `
            ❌ فشل في الاتصال بقاعدة البيانات<br>
            <small>تحقق من الاتصال بالإنترنت</small>
        `;
        document.body.appendChild(errorDiv);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => errorDiv.remove(), 5000);
    }
}

// تشغيل الإضافة البسيطة
new SimpleDynamicExtension();
