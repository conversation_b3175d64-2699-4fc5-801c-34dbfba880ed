<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار APIs الخاصة بالإضافة</h1>
        
        <div>
            <button onclick="testDataAPI()">اختبار API البيانات</button>
            <button onclick="testUIAPI()">اختبار API الأكواد</button>
            <button onclick="testBoth()">اختبار كلاهما</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const dataApiUrls = [
            'https://kushoofapp.com/js/api/receive-data-fixed.php',
            'https://kushoofapp.com/js/api/receive-data.php'
        ];
        const uiApiUrl = 'https://kushoofapp.com/js/api/get-extension-ui.php';

        function addResult(title, content, type = 'result') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div>${content}</div>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        async function testDataAPI() {
            addResult('🔄 اختبار API البيانات...', 'جاري الاختبار...');
            
            for (let i = 0; i < dataApiUrls.length; i++) {
                const apiUrl = dataApiUrls[i];
                try {
                    const response = await fetch(apiUrl);
                    
                    if (!response.ok) {
                        addResult(`❌ API ${i + 1} فشل`, `HTTP ${response.status} - ${apiUrl}`, 'error');
                        continue;
                    }
                    
                    const data = await response.json();
                    let dataCount = 0;
                    
                    if (Array.isArray(data)) {
                        dataCount = data.length;
                    } else if (data.data && Array.isArray(data.data)) {
                        dataCount = data.data.length;
                    } else if (data.students && Array.isArray(data.students)) {
                        dataCount = data.students.length;
                    }
                    
                    addResult(
                        `✅ API ${i + 1} نجح`, 
                        `
                            <p><strong>الرابط:</strong> ${apiUrl}</p>
                            <p><strong>عدد العناصر:</strong> ${dataCount}</p>
                            <p><strong>نوع البيانات:</strong> ${typeof data}</p>
                            <details>
                                <summary>عرض البيانات الخام</summary>
                                <pre>${JSON.stringify(data, null, 2).substring(0, 1000)}...</pre>
                            </details>
                        `, 
                        'success'
                    );
                    break;
                    
                } catch (error) {
                    addResult(`❌ API ${i + 1} خطأ`, `${error.message} - ${apiUrl}`, 'error');
                }
            }
        }

        async function testUIAPI() {
            addResult('🔄 اختبار API الأكواد...', 'جاري الاختبار...');
            
            try {
                const response = await fetch(uiApiUrl);
                
                if (!response.ok) {
                    addResult('❌ API الأكواد فشل', `HTTP ${response.status} - ${uiApiUrl}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.success && data.ui) {
                    const hasCSS = data.ui.css ? '✅' : '❌';
                    const hasHTML = data.ui.html ? '✅' : '❌';
                    const hasJS = data.ui.javascript ? '✅' : '❌';
                    
                    addResult(
                        '✅ API الأكواد نجح', 
                        `
                            <p><strong>الرابط:</strong> ${uiApiUrl}</p>
                            <p><strong>CSS:</strong> ${hasCSS} ${data.ui.css ? `(${data.ui.css.length} حرف)` : ''}</p>
                            <p><strong>HTML:</strong> ${hasHTML} ${data.ui.html ? `(${data.ui.html.length} حرف)` : ''}</p>
                            <p><strong>JavaScript:</strong> ${hasJS} ${data.ui.javascript ? `(${data.ui.javascript.length} حرف)` : ''}</p>
                            <details>
                                <summary>عرض JavaScript</summary>
                                <pre>${data.ui.javascript || 'لا يوجد'}</pre>
                            </details>
                        `, 
                        'success'
                    );
                    
                    // اختبار تنفيذ JavaScript
                    if (data.ui.javascript) {
                        try {
                            eval(data.ui.javascript);
                            
                            setTimeout(() => {
                                const showFunc = typeof window.showRealStudentsData;
                                const exportFunc = typeof window.exportRealData;
                                
                                addResult(
                                    '🔍 اختبار تنفيذ JavaScript',
                                    `
                                        <p><strong>showRealStudentsData:</strong> ${showFunc}</p>
                                        <p><strong>exportRealData:</strong> ${exportFunc}</p>
                                        ${showFunc === 'function' ? '✅ دالة العرض معرّفة' : '❌ دالة العرض غير معرّفة'}
                                        <br>
                                        ${exportFunc === 'function' ? '✅ دالة التصدير معرّفة' : '❌ دالة التصدير غير معرّفة'}
                                    `,
                                    showFunc === 'function' && exportFunc === 'function' ? 'success' : 'error'
                                );
                            }, 100);
                            
                        } catch (error) {
                            addResult('❌ فشل في تنفيذ JavaScript', error.message, 'error');
                        }
                    }
                    
                } else {
                    addResult('❌ بيانات غير صحيحة', 'الاستجابة لا تحتوي على أكواد صحيحة', 'error');
                }
                
            } catch (error) {
                addResult('❌ خطأ في API الأكواد', error.message, 'error');
            }
        }

        async function testBoth() {
            clearResults();
            await testDataAPI();
            await testUIAPI();
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 مرحباً', 'استخدم الأزرار أعلاه لاختبار APIs الخاصة بالإضافة');
        };
    </script>
</body>
</html>
