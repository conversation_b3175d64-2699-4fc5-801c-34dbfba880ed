# 📚 شرح مفصل لملف kushoof-loader.js

## 🎯 نظرة عامة

ملف `kushoof-loader.js` هو الملف الرئيسي للإضافة الديناميكية. يحتوي على 600+ سطر من الكود المنظم في فئة واحدة تدير جميع عمليات الإضافة.

## 🏗️ هيكل الملف

### **1. تعريف الفئة الرئيسية**

```javascript
class KushoofRealDataExtension {
  constructor() {
    // إعدادات الإضافة
  }
}
```

## 📋 شرح مفصل للأكواد

### **🔧 1. Constructor - منشئ الفئة (السطور 7-23)**

```javascript
constructor() {
    // APIs للبيانات
    this.dataApiUrls = [
        'https://kushoofapp.com/js/api/receive-data-fixed.php',  // API المُصحح
        'https://kushoofapp.com/js/api/receive-data.php'         // API الأصلي
    ];

    // API لأكواد الإضافة
    this.uiApiUrl = 'https://kushoofapp.com/js/api/get-extension-ui.php';

    // متغيرات البيانات
    this.realData = null;           // البيانات المجلبة
    this.workingApiUrl = '';        // API الناجح
    this.uiComponents = null;       // أكواد الواجهة

    console.log('🚀 [Kushoof] جلب البيانات والأكواد من الموقع');
    this.init();                    // بدء التشغيل
}
```

**الشرح:**

- `dataApiUrls`: مصفوفة تحتوي على APIs البيانات (يجرب الأول، إذا فشل يجرب الثاني)
- `uiApiUrl`: رابط API لجلب أكواد الواجهة من الموقع
- `realData`: متغير لحفظ البيانات المجلبة من قاعدة البيانات
- `workingApiUrl`: يحفظ رابط API الذي نجح في جلب البيانات
- `uiComponents`: يحفظ أكواد HTML/CSS/JS المجلبة من الموقع

### **🚀 2. init() - دالة التشغيل الرئيسية (السطور 25-42)**

```javascript
async init() {
    try {
        // جلب البيانات الحقيقية من الموقع
        await this.fetchRealData();

        // جلب أكواد الإضافة من الموقع
        await this.fetchUIComponents();

        // تطبيق الأكواد المجلبة
        await this.applyUIComponents();

        console.log('✅ [Kushoof] تم تحميل الإضافة والأكواد من الموقع بنجاح');

    } catch (error) {
        console.error('❌ [Kushoof] فشل في تحميل البيانات أو الأكواد:', error);
        this.showError();
    }
}
```

**الشرح:**

- دالة `async` تعمل بشكل متتالي
- تجلب البيانات أولاً، ثم أكواد الواجهة، ثم تطبقها
- إذا فشلت أي خطوة، تظهر رسالة خطأ

### **📡 3. fetchRealData() - جلب البيانات (السطور 44-100)**

```javascript
async fetchRealData() {
    console.log('📡 [Kushoof] جلب البيانات من قاعدة البيانات الحقيقية...');

    // جرب كل API حتى تجد واحد يعمل
    for (let i = 0; i < this.dataApiUrls.length; i++) {
        const apiUrl = this.dataApiUrls[i];

        try {
            console.log(`🔄 [Kushoof] جرب API ${i + 1}: ${apiUrl}`);

            const response = await fetch(apiUrl);

            if (!response.ok) {
                console.warn(`⚠️ [Kushoof] API ${i + 1} فشل: HTTP ${response.status}`);
                continue;
            }

            const data = await response.json();
            console.log(`📦 [Kushoof] البيانات المجلبة من API ${i + 1}:`, data);

            // التحقق من وجود بيانات حقيقية
            let realData = null;

            // إذا كانت البيانات مصفوفة مباشرة
            if (Array.isArray(data) && data.length > 0) {
                realData = data;
            }
            // إذا كانت البيانات في خاصية معينة
            else if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                realData = data.data;
            }
            // إذا كانت البيانات في خاصية أخرى
            else if (data.students && Array.isArray(data.students) && data.students.length > 0) {
                realData = data.students;
            }
            // إذا كانت البيانات في خاصية results
            else if (data.results && Array.isArray(data.results) && data.results.length > 0) {
                realData = data.results;
            }

            if (realData && realData.length > 0) {
                this.realData = realData;
                this.workingApiUrl = apiUrl;
                console.log(`✅ [Kushoof] تم جلب ${realData.length} عنصر من قاعدة البيانات الحقيقية`);
                console.log(`🎯 [Kushoof] API الناجح: ${apiUrl}`);
                return realData;
            }

            console.warn(`⚠️ [Kushoof] API ${i + 1} لا يحتوي على بيانات`);

        } catch (error) {
            console.warn(`⚠️ [Kushoof] خطأ في API ${i + 1}:`, error);
            continue;
        }
    }

    throw new Error('فشل في جلب البيانات الحقيقية من جميع APIs');
}
```

**الشرح:**

- يجرب APIs متعددة للبيانات (نظام احتياطي)
- يتحقق من أشكال مختلفة لهيكل البيانات
- يحفظ البيانات الناجحة ورابط API الناجح
- إذا فشلت جميع APIs، يرمي خطأ

### **🎨 4. fetchUIComponents() - جلب أكواد الواجهة (السطور 102-125)**

```javascript
async fetchUIComponents() {
    try {
        console.log('🎨 [Kushoof] جلب أكواد الإضافة من الموقع...');
        console.log(`🔗 [Kushoof] الاتصال بـ: ${this.uiApiUrl}`);

        const response = await fetch(this.uiApiUrl);

        if (!response.ok) {
            throw new Error(`فشل في جلب أكواد الإضافة: HTTP ${response.status}`);
        }

        const data = await response.json();
        console.log('📦 [Kushoof] أكواد الإضافة المجلبة:', data);

        if (data.success && data.ui) {
            this.uiComponents = data.ui;
            console.log('✅ [Kushoof] تم جلب أكواد الإضافة من الموقع بنجاح');
            return data.ui;
        } else {
            throw new Error('فشل في جلب أكواد الإضافة من الموقع');
        }

    } catch (error) {
        console.error('❌ [Kushoof] فشل في جلب أكواد الإضافة:', error);
        throw error;
    }
}
```

**الشرح:**

- يجلب أكواد HTML/CSS/JavaScript من الموقع
- يتحقق من نجاح العملية
- يحفظ الأكواد في `this.uiComponents`

### **🔧 5. applyUIComponents() - تطبيق الأكواد (السطور 127-166)**

```javascript
async applyUIComponents() {
    if (!this.uiComponents) {
        throw new Error('لا توجد أكواد إضافة لتطبيقها');
    }

    try {
        console.log('🔧 [Kushoof] تطبيق أكواد الإضافة المجلبة من الموقع...');

        // تطبيق CSS
        if (this.uiComponents.css) {
            this.injectCSS(this.uiComponents.css);
            console.log('🎨 [Kushoof] تم تطبيق CSS من الموقع');
        }

        // تطبيق HTML
        if (this.uiComponents.html) {
            this.injectHTML(this.uiComponents.html);
            console.log('🏗️ [Kushoof] تم تطبيق HTML من الموقع');
        }

        // تطبيق JavaScript
        if (this.uiComponents.javascript) {
            this.executeJavaScript(this.uiComponents.javascript);
            console.log('⚡ [Kushoof] تم تطبيق JavaScript من الموقع');
        }

        // تمرير البيانات للدوال المجلبة
        this.passDataToUI();

        // إضافة إشعار النجاح
        this.showSuccessNotice();

        console.log('✅ [Kushoof] تم تطبيق جميع أكواد الإضافة من الموقع');

    } catch (error) {
        console.error('❌ [Kushoof] فشل في تطبيق أكواد الإضافة:', error);
        throw error;
    }
}
```

**الشرح:**

- يطبق CSS أولاً (التصميم)
- ثم HTML (العناصر)
- ثم JavaScript (الوظائف)
- يمرر البيانات للواجهة
- يظهر إشعار نجاح

### **🎨 6. injectCSS() - حقن CSS (السطور 168-172)**

```javascript
injectCSS(cssCode) {
    const style = document.createElement('style');
    style.textContent = cssCode;
    document.head.appendChild(style);
}
```

**الشرح:**

- ينشئ عنصر `<style>` جديد
- يضع كود CSS المجلب من الموقع داخله
- يضيفه إلى `<head>` الصفحة

### **🏗️ 7. injectHTML() - حقن HTML (السطور 174-207)**

```javascript
injectHTML(htmlCode) {
    const container = document.createElement('div');
    container.innerHTML = htmlCode;

    // إزالة onclick attributes وإضافة event listeners بدلاً منها
    const buttons = container.querySelectorAll('button[onclick]');
    buttons.forEach(button => {
        const onclickValue = button.getAttribute('onclick');
        button.removeAttribute('onclick');

        if (onclickValue.includes('showRealStudentsData')) {
            button.addEventListener('click', () => {
                if (typeof window.showRealStudentsData === 'function') {
                    window.showRealStudentsData();
                } else {
                    console.log('📊 [Kushoof] استخدام الدالة الاحتياطية لعرض البيانات');
                    this.showRealDataBackup();
                }
            });
        } else if (onclickValue.includes('exportRealData')) {
            button.addEventListener('click', () => {
                if (typeof window.exportRealData === 'function') {
                    window.exportRealData();
                } else {
                    console.log('📤 [Kushoof] استخدام الدالة الاحتياطية لتصدير البيانات');
                    this.exportRealDataBackup();
                }
            });
        }
    });

    document.body.appendChild(container);
    console.log('🔗 [Kushoof] تم ربط الأحداث بالأزرار');
}
```

**الشرح:**

- ينشئ container لـ HTML المجلب
- يزيل `onclick` attributes (لحل مشكلة Content Security Policy)
- يضيف `addEventListener` بدلاً منها
- يتحقق من وجود الدوال المجلبة من الموقع
- إذا لم توجد، يستخدم الدوال الاحتياطية

### **⚡ 8. executeJavaScript() - تنفيذ JavaScript (السطور 209-243)**

```javascript
executeJavaScript(jsCode) {
    try {
        console.log('🔧 [Kushoof] تنفيذ JavaScript المجلب من الموقع...');

        // إنشاء script element وإضافته للصفحة
        const script = document.createElement('script');
        script.textContent = jsCode;
        script.type = 'text/javascript';

        // إضافة معرف فريد للتتبع
        script.id = 'kushoof-dynamic-script-' + Date.now();

        // إضافة Script للصفحة
        document.head.appendChild(script);

        console.log('✅ [Kushoof] تم إضافة JavaScript المجلب للصفحة');

        // التحقق من تعريف الدوال بعد فترة قصيرة
        setTimeout(() => {
            if (typeof window.showRealStudentsData === 'function') {
                console.log('✅ [Kushoof] دالة showRealStudentsData معرّفة بنجاح');
            } else {
                console.warn('⚠️ [Kushoof] دالة showRealStudentsData غير معرّفة، استخدام الدوال الاحتياطية');
                this.defineBackupFunctions();
            }

            if (typeof window.exportRealData === 'function') {
                console.log('✅ [Kushoof] دالة exportRealData معرّفة بنجاح');
            } else {
                console.warn('⚠️ [Kushoof] دالة exportRealData غير معرّفة');
            }
        }, 200);

    } catch (error) {
        console.error('❌ [Kushoof] فشل في تنفيذ JavaScript:', error);
        this.defineBackupFunctions();
    }
}
```

**الشرح:**

- ينشئ عنصر `<script>` جديد
- يضع كود JavaScript المجلب داخله
- يضيفه إلى `<head>` الصفحة
- يتحقق من تعريف الدوال بعد 200ms
- إذا فشل، يستخدم الدوال الاحتياطية
