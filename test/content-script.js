// Content Script - محاكاة الكود الذي يتم حقنه في صفحات الويب
// هذا الملف يوضح كيف تعمل الإضافة الأصلية

console.log('🚀 Kshuf Content Script تم تحميله');

// محاكاة دالة nullthrows من a.js
const nullthrows = (v) => {
    if (v == null) throw new Error("it's a null");
    return v;
}

// محاكاة دالة injectCode من a.js
function injectCode(src) {
    const script = document.createElement('script');
    script.src = src;
    script.onload = function() { this.remove(); };
    nullthrows(document.head || document.documentElement).appendChild(script);
}

// محاكاة فحص URL والتفاعل مع المواقع المختلفة
function checkCurrentSite() {
    const currentURL = window.location.href;
    console.log('🔍 فحص الموقع الحالي:', currentURL);
    
    // فحص موقع مدرستي (النسخة الجديدة)
    if (currentURL.substring(0, 63) === 'https://schools.madrasati.sa/SchoolManagment/Teachers?SchoolId=') {
        console.log('✅ تم اكتشاف موقع مدرستي (النسخة الجديدة)');
        handleMadrasatiSite();
    }
    
    // فحص موقع مدرستي (النسخة التجريبية)
    else if (currentURL.substring(0, 44) === 'https://beta.madrasati.sa/Teachers?SchoolId=') {
        console.log('✅ تم اكتشاف موقع مدرستي (النسخة التجريبية)');
        handleBetaMadrasatiSite();
    }
    
    // فحص موقع نور
    else if (currentURL === "https://noor.moe.gov.sa/Noor/EduWavek12Portal/HomePage.aspx") {
        console.log('✅ تم اكتشاف موقع نور');
        handleNoorSite();
    }
    
    // فحص WhatsApp Web
    else if (currentURL.substring(0, 24) === "https://web.whatsapp.com") {
        console.log('✅ تم اكتشاف WhatsApp Web');
        handleWhatsAppSite();
    }
    
    else {
        console.log('ℹ️ موقع غير مدعوم:', currentURL);
    }
}

// التعامل مع موقع مدرستي (النسخة الجديدة)
function handleMadrasatiSite() {
    // استخراج معرف المدرسة من URL
    const urlParts = window.location.href.split("=");
    const schoolId = urlParts[1];
    
    console.log('🏫 معرف المدرسة:', schoolId);
    
    // تحميل السكريبت من الخادم
    const scriptUrl = `https://kushoofapp.com/java/js.php?version=258&id=${schoolId}&k=K`;
    console.log('📥 تحميل السكريبت:', scriptUrl);
    
    // محاكاة loadScript من b.js
    loadScript(scriptUrl, function() {
        console.log('✅ تم تحميل سكريبت Kushoof بنجاح');
        initializeKshufInterface();
    });
}

// التعامل مع موقع مدرستي (النسخة التجريبية)
function handleBetaMadrasatiSite() {
    const urlParts = window.location.href.split("=");
    const schoolId = urlParts[1];
    
    console.log('🏫 معرف المدرسة (بيتا):', schoolId);
    
    const scriptUrl = `https://kushoofapp.com/java/js.php?version=280&id=${schoolId}&k=K`;
    console.log('📥 تحميل السكريبت (بيتا):', scriptUrl);

    loadScript(scriptUrl, function() {
        console.log('✅ تم تحميل سكريبت Kushoof (بيتا) بنجاح');
        initializeKshufInterface();
    });
}

// التعامل مع موقع نور
function handleNoorSite() {
    console.log('🎓 تحميل سكريبت نور');
    loadScript(`https://kushoofapp.com/java/noor.php?k=vir3`, function() {
        console.log('✅ تم تحميل سكريبت نور بنجاح');
    });
}

// التعامل مع WhatsApp Web
function handleWhatsAppSite() {
    console.log('💬 معالجة WhatsApp Web');
    
    // فحص إذا كان هناك بيانات في URL
    const urlSegment = window.location.href.substring(26, 100);
    
    if (urlSegment.length > 30 && urlSegment.indexOf("=") < 0) {
        console.log('🔄 جلب البيانات من kushoofapp.com');
        
        // محاكاة fetch من a.js
        // استخدام الخادم الحقيقي
        fetch('https://kushoofapp.com/java/g.php?s=' + urlSegment)
            .then(response => response.text())
            .then(data => {
                console.log('📊 تم استلام البيانات:', data);
                window.name = data;
                window.location.href = 'https://web.whatsapp.com';
            })
            .catch(error => {
                console.error('❌ خطأ في جلب البيانات:', error);
            });
    }
    
    // تحميل السكريبت الرئيسي
    injectCode(chrome.runtime.getURL('/injected-script.js'));
    
    // تهيئة واجهة WhatsApp
    initializeWhatsAppInterface();
}

// محاكاة دالة loadScript من b.js
function loadScript(url, callback) {
    console.log('📜 تحميل سكريبت:', url);
    
    const head = document.getElementsByTagName('head')[0];
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onreadystatechange = callback;
    script.onload = callback;
    script.onerror = function() {
        console.error('❌ فشل في تحميل السكريبت:', url);
    };
    head.appendChild(script);
}

// تهيئة واجهة Kshuf في موقع مدرستي
function initializeKshufInterface() {
    console.log('🎨 تهيئة واجهة Kshuf');
    
    // البحث عن header (محاكاة findpp من b.js)
    function findHeader() {
        const headers = document.getElementsByTagName("header");
        
        if (headers.length > 0) {
            console.log('✅ تم العثور على header');
            injectKshufInterface();
        } else {
            console.log('⏳ البحث عن header...');
            setTimeout(findHeader, 2000);
        }
    }
    
    findHeader();
}

// حقن واجهة Kshuf
function injectKshufInterface() {
    console.log('💉 حقن واجهة Kshuf');
    
    // حقن CSS
    injectCSS();
    
    // محاكاة استخراج البيانات من window.name
    let studentsData;
    try {
        if (window.name.includes('kshuf')) {
            studentsData = JSON.parse(window.name.split('kshuf')[1]);
            console.log('📊 تم استخراج بيانات الطلاب:', studentsData);
        }
    } catch (error) {
        console.error('❌ خطأ في استخراج البيانات:', error);
        return;
    }
    
    // بناء HTML الواجهة
    const interfaceHTML = buildKshufInterface(studentsData);
    
    // استبدال header (محاكاة outerHTML)
    const header = document.getElementsByTagName("header")[0];
    if (header) {
        header.outerHTML = interfaceHTML;
        console.log('✅ تم استبدال header بواجهة Kshuf');
        
        // إضافة مستمعي الأحداث
        addEventListeners();
    }
}

// حقن CSS
function injectCSS() {
    const style = document.createElement('style');
    style.textContent = `
        .kshuf-btn {
            font-size: 18px;
            width: 80%;
            border-radius: 5px;
            height: 30px;
            background: #4682b4;
            color: white;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        
        .kshuf-option {
            font-weight: bold;
            font-size: 16px;
            color: #006400;
        }
        
        .kshuf-interface {
            background: linear-gradient(135deg, #128c7e 0%, #075e54 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
    `;
    document.head.appendChild(style);
    console.log('🎨 تم حقن CSS');
}

// بناء HTML واجهة Kshuf
function buildKshufInterface(data) {
    if (!data) return '';
    
    let optionsHTML = '';
    for (let i = 0; i < data.n.length; i++) {
        const style = data.p[i].length < 12 ? 'style="color:red"' : '';
        optionsHTML += `
            <option class="kshuf-option" ${style} value="${data.p[i]}">
                ${i + 1} - ${decodeURIComponent(data.n[i])}
            </option>
        `;
    }
    
    return `
        <div class="kshuf-interface">
            <a id="kshuf-link" href="" style="visibility:hidden"></a>
            <h2>🚀 Kshuf WhatsApp</h2>
            <hr style="width:70%;">
            
            <div style="margin: 20px 0;">
                <button id="start-btn" class="kshuf-btn">بدء الإرسال</button>
                <button id="stop-btn" class="kshuf-btn" style="background:#dc143c;">إيقاف الإرسال</button>
            </div>
            
            <div>
                <h3>قائمة بيانات الإرسال</h3>
                <select id="students-select" size="4" style="width:90%; padding:10px;">
                    ${optionsHTML}
                </select>
            </div>
            
            <div style="margin-top: 20px;">
                <textarea id="message-text" rows="4" style="width:90%; padding:10px;" 
                          placeholder="اكتب نص الرسالة هنا"></textarea>
                <br>
                <input id="signature-text" style="width:90%; padding:10px; margin-top:10px;" 
                       placeholder="تذييل الرسالة" value="مع تحيات إدارة المدرسة">
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    console.log('🎧 إضافة مستمعي الأحداث');
    
    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    const studentsSelect = document.getElementById('students-select');
    
    if (startBtn) {
        startBtn.addEventListener('click', startSending);
    }
    
    if (stopBtn) {
        stopBtn.addEventListener('click', stopSending);
    }
    
    if (studentsSelect) {
        studentsSelect.addEventListener('change', onStudentSelect);
    }
    
    // مستمع Enter للإرسال السريع
    document.addEventListener('keypress', function(event) {
        if (event.key === 'Enter' && document.activeElement.id === 'students-select') {
            sendQuickMessage();
        }
    });
}

// تهيئة واجهة WhatsApp
function initializeWhatsAppInterface() {
    console.log('💬 تهيئة واجهة WhatsApp');
    
    // فحص وجود البيانات في window.name
    if (window.name.includes('kshuf')) {
        console.log('📊 تم العثور على بيانات في window.name');
        setupWhatsAppInterface();
    }
}

// إعداد واجهة WhatsApp
function setupWhatsAppInterface() {
    // البحث عن header في WhatsApp
    function findWhatsAppHeader() {
        const headers = document.getElementsByTagName("header");
        
        if (headers.length > 0) {
            console.log('✅ تم العثور على header في WhatsApp');
            injectWhatsAppControls();
        } else {
            console.log('⏳ البحث عن header في WhatsApp...');
            setTimeout(findWhatsAppHeader, 2000);
        }
    }
    
    findWhatsAppHeader();
}

// حقن أدوات التحكم في WhatsApp
function injectWhatsAppControls() {
    console.log('💉 حقن أدوات التحكم في WhatsApp');
    
    // هنا يتم حقن الواجهة المخصصة في WhatsApp
    // مشابه لما يحدث في b.js
}

// وظائف التحكم
function startSending() {
    console.log('▶️ بدء الإرسال');
    // تنفيذ منطق الإرسال
}

function stopSending() {
    console.log('⏹️ إيقاف الإرسال');
    // إيقاف عملية الإرسال
}

function onStudentSelect() {
    console.log('👤 تم اختيار طالب');
    // تحديث الرسالة حسب الطالب المختار
}

function sendQuickMessage() {
    console.log('⚡ إرسال سريع');
    // إرسال رسالة للطالب المختار
}

// بدء فحص الموقع عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', checkCurrentSite);
} else {
    checkCurrentSite();
}

// مراقبة تغييرات URL (للتطبيقات أحادية الصفحة)
let currentURL = window.location.href;
setInterval(() => {
    if (window.location.href !== currentURL) {
        currentURL = window.location.href;
        console.log('🔄 تغيير URL:', currentURL);
        checkCurrentSite();
    }
}, 1000);
