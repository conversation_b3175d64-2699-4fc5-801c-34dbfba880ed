// مولد أيقونات Kushoof
// يقوم بإنشاء أيقونات بأحجام مختلفة

const iconSizes = [16, 24, 32, 48, 64, 72, 96, 128];

function createIconSVG(size) {
    return `
<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#128c7e;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#075e54;stop-opacity:1" />
        </linearGradient>
    </defs>
    
    <!-- خلفية مستديرة -->
    <rect width="${size}" height="${size}" rx="${size * 0.15}" ry="${size * 0.15}" fill="url(#bg-gradient)"/>
    
    <!-- حرف K -->
    <text x="${size / 2}" y="${size / 2 + size * 0.1}" 
          font-family="Arial, sans-serif" 
          font-size="${size * 0.6}" 
          font-weight="bold" 
          text-anchor="middle" 
          fill="white">K</text>
    
    <!-- نقطة ذهبية -->
    <circle cx="${size * 0.8}" cy="${size * 0.2}" r="${size * 0.08}" fill="#ffd700"/>
</svg>`;
}

function svgToCanvas(svgString, size) {
    return new Promise((resolve) => {
        const img = new Image();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = size;
        canvas.height = size;
        
        img.onload = function() {
            ctx.drawImage(img, 0, 0, size, size);
            resolve(canvas);
        };
        
        const blob = new Blob([svgString], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        img.src = url;
    });
}

function downloadCanvas(canvas, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL('image/png');
    link.click();
}

async function generateAllIcons() {
    console.log('🎨 بدء إنشاء الأيقونات...');
    
    for (const size of iconSizes) {
        try {
            const svgString = createIconSVG(size);
            const canvas = await svgToCanvas(svgString, size);
            downloadCanvas(canvas, `icon${size}.png`);
            console.log(`✅ تم إنشاء icon${size}.png`);
            
            // تأخير قصير بين التحميلات
            await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
            console.error(`❌ خطأ في إنشاء icon${size}.png:`, error);
        }
    }
    
    console.log('🎉 تم الانتهاء من إنشاء جميع الأيقونات!');
}

// تصدير الدوال للاستخدام في HTML
if (typeof window !== 'undefined') {
    window.generateAllIcons = generateAllIcons;
    window.createIconSVG = createIconSVG;
    window.iconSizes = iconSizes;
}

// إذا تم تشغيل الملف مباشرة في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateAllIcons,
        createIconSVG,
        iconSizes
    };
}
