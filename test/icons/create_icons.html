<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات Kushoof</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
            direction: rtl;
        }
        .icon-generator {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            margin: 10px;
        }
        .icon {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 5px;
        }
        button {
            background: #128c7e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0f7a6b;
        }
    </style>
</head>
<body>
    <div class="icon-generator">
        <h1>🎨 مولد أيقونات Kushoof</h1>
        <p>هذه الأداة تنشئ أيقونات للإضافة بأحجام مختلفة</p>
        
        <div class="icon-preview" id="iconPreview">
            <!-- سيتم إنشاء الأيقونات هنا -->
        </div>
        
        <div style="text-align: center;">
            <button onclick="generateIcons()">🚀 إنشاء جميع الأيقونات</button>
            <button onclick="downloadAll()">📥 تحميل الكل</button>
        </div>
        
        <div id="status" style="margin-top: 20px; text-align: center;"></div>
    </div>

    <script>
        const iconSizes = [16, 24, 32, 48, 64, 72, 96, 128];
        const generatedIcons = {};

        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#128c7e');
            gradient.addColorStop(1, '#075e54');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // إضافة حدود مستديرة
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';

            // رسم الرمز (K)
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('K', size / 2, size / 2);

            // إضافة نقطة صغيرة للتمييز
            ctx.fillStyle = '#ffd700';
            ctx.beginPath();
            ctx.arc(size * 0.8, size * 0.2, size * 0.08, 0, 2 * Math.PI);
            ctx.fill();

            return canvas;
        }

        function generateIcons() {
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';

            iconSizes.forEach(size => {
                const canvas = createIcon(size);
                generatedIcons[size] = canvas;

                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <canvas class="icon" width="${size}" height="${size}"></canvas>
                    <div>${size}x${size}</div>
                    <button onclick="downloadIcon(${size})">تحميل</button>
                `;

                const displayCanvas = iconItem.querySelector('canvas');
                const displayCtx = displayCanvas.getContext('2d');
                displayCtx.drawImage(canvas, 0, 0);

                preview.appendChild(iconItem);
            });

            document.getElementById('status').innerHTML = 
                '<span style="color: green;">✅ تم إنشاء جميع الأيقونات بنجاح!</span>';
        }

        function downloadIcon(size) {
            if (!generatedIcons[size]) return;

            const canvas = generatedIcons[size];
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAll() {
            if (Object.keys(generatedIcons).length === 0) {
                alert('يرجى إنشاء الأيقونات أولاً');
                return;
            }

            iconSizes.forEach(size => {
                if (generatedIcons[size]) {
                    setTimeout(() => downloadIcon(size), size * 10);
                }
            });

            document.getElementById('status').innerHTML = 
                '<span style="color: blue;">📥 جاري تحميل جميع الأيقونات...</span>';
        }

        // إنشاء الأيقونات تلقائياً عند تحميل الصفحة
        window.onload = function() {
            generateIcons();
        };
    </script>
</body>
</html>
