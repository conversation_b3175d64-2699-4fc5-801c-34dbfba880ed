<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Kushoof Icon Generator</title>
</head>
<body>
    <h1>Generating Kushoof Icons...</h1>
    <div id="status"></div>
    
    <script>
        const iconSizes = [16, 24, 32, 48, 64, 72, 96, 128];
        
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#128c7e');
            gradient.addColorStop(1, '#075e54');
            
            // رسم الخلفية مع حواف مستديرة
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();

            // رسم حرف K
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('K', size / 2, size / 2);

            // نقطة ذهبية
            ctx.fillStyle = '#ffd700';
            ctx.beginPath();
            ctx.arc(size * 0.8, size * 0.2, size * 0.08, 0, 2 * Math.PI);
            ctx.fill();

            return canvas;
        }

        function downloadIcon(canvas, size) {
            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `icon${size}.png`;
                    a.click();
                    URL.revokeObjectURL(url);
                    resolve();
                }, 'image/png');
            });
        }

        async function generateAllIcons() {
            const status = document.getElementById('status');
            
            for (let i = 0; i < iconSizes.length; i++) {
                const size = iconSizes[i];
                status.innerHTML = `Creating icon${size}.png... (${i + 1}/${iconSizes.length})`;
                
                const canvas = createIcon(size);
                await downloadIcon(canvas, size);
                
                // تأخير قصير
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            status.innerHTML = '✅ All icons generated successfully!<br>Check your Downloads folder.';
        }

        // بدء التوليد تلقائياً
        window.onload = generateAllIcons;
    </script>
</body>
</html>
