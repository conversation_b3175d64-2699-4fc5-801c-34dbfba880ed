# إعادة توجيه المسارات القديمة إلى مجلد java
# لضمان التوافق مع الإضافات القديمة

RewriteEngine On

# إعادة توجيه js.php إلى java/js.php
RewriteRule ^js\.php$ java/js.php [L,QSA]

# إعادة توجيه g.php إلى java/g.php  
RewriteRule ^b/g\.php$ java/g.php [L,QSA]

# إعادة توجيه noor.php إلى java/noor.php
RewriteRule ^java/noor/noor\.php$ java/noor.php [L,QSA]

# السماح بالوصول المباشر لمجلد java
RewriteRule ^java/ - [L]

# إعادة توجيه المسارات القديمة للتوافق
RewriteRule ^noor\.php$ java/noor.php [L,QSA]

# Headers للتطوير المحلي
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# تحسين الأداء
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/javascript "access plus 1 hour"
    ExpiresByType application/json "access plus 5 minutes"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>
