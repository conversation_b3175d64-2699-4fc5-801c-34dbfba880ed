# محاكاة خادم Kushoof - PHP Simulation

هذا المجلد يحتوي على ملفات PHP تجريبية تحاكي خادم kushoofapp.com مع بيانات تجريبية كاملة.

## 🗂️ **التنظيم الجديد - جميع APIs في مجلد `/java/`**

تم إعادة تنظيم جميع ملفات الموقع لتكون داخل مجلد `java` لسهولة الإدارة:

```
kushoofapp.com/
└── java/
    ├── js.php          # API بيانات المدارس
    ├── g.php           # API بيانات WhatsApp
    └── noor.php        # API نظام نور
```

## 📁 الملفات المتضمنة:

### 🌐 `index.php`

- **الوصف**: الصفحة الرئيسية للخادم التجريبي
- **الميزات**:
  - واجهة تفاعلية لاختبار جميع APIs
  - إحصائيات مباشرة للطلبات
  - سجل مباشر للعمليات
  - أزرار اختبار لكل API

### 🏫 `java/js.php`

- **الوصف**: محاكاة API بيانات المدارس
- **المسار الجديد**: `/java/js.php`
- **الوظيفة**: يرسل كود JavaScript مع بيانات الطلاب
- **المعاملات**:
  - `version`: إصدار السكريپت (258, 280)
  - `id`: معرف المدرسة
  - `k`: مفتاح التحقق (K)
- **البيانات التجريبية**:
  - 3 مدارس مختلفة مع بيانات كاملة
  - طلاب بأرقام هواتف صحيحة وغير صحيحة
  - معلومات المدرسة والمدينة

### 💬 `java/g.php`

- **الوصف**: محاكاة API بيانات WhatsApp
- **المسار الجديد**: `/java/g.php`
- **الوظيفة**: يرسل بيانات JSON للاستخدام في WhatsApp
- **المعاملات**:
  - `s`: معرف الجلسة
- **البيانات التجريبية**:
  - جلسات مختلفة مع بيانات متنوعة
  - قوالب رسائل للغياب والدرجات
  - معلومات المعلمين والمواد

### 🎓 `java/noor.php`

- **الوصف**: محاكاة API نظام نور
- **المسار الجديد**: `/java/noor.php`
- **الوظيفة**: يرسل كود JavaScript للتكامل مع نظام نور
- **المعاملات**:
  - `k`: مفتاح التحقق (vir3)
- **البيانات التجريبية**:
  - بيانات مدارس نظام نور
  - طلاب ومعلمين
  - واجهة مخصصة لنظام نور

## 🚀 كيفية التشغيل:

### المتطلبات:

- خادم ويب مع دعم PHP (Apache, Nginx, أو XAMPP)
- PHP 7.0 أو أحدث

### خطوات التشغيل:

1. **نسخ الملفات**:

   ```bash
   cp -r test/php-simulation/ /path/to/webserver/kushoof-sim/
   ```

2. **تشغيل الخادم المحلي**:

   ```bash
   # باستخدام PHP المدمج
   cd test/php-simulation/
   php -S localhost:8080

   # أو باستخدام XAMPP
   # ضع الملفات في htdocs/kushoof-sim/
   ```

3. **فتح المتصفح**:
   ```
   http://localhost:8080/
   ```

## 🧪 اختبار APIs:

### 🏫 اختبار API المدارس:

```bash
curl "http://localhost:8080/js.php?version=258&id=123456&k=K"
```

### 💬 اختبار API WhatsApp:

```bash
curl "http://localhost:8080/g.php?s=abc123def456"
```

### 🎓 اختبار API نور:

```bash
curl "http://localhost:8080/noor.php?k=vir3"
```

## 📊 البيانات التجريبية:

### المدارس المتاحة:

- **123456**: مدرسة الأمل الابتدائية (الرياض)
- **789012**: مدرسة النور المتوسطة (جدة)
- **345678**: مدرسة المستقبل الثانوية (الدمام)

### الجلسات المتاحة:

- **abc123def456**: جلسة غياب وحضور
- **xyz789uvw012**: جلسة درجات ونتائج

### أرقام الهواتف:

- أرقام صحيحة: تبدأ بـ 966 وتحتوي على 12 رقم
- أرقام غير صحيحة: "invalid" أو أرقام ناقصة

## 🔧 التخصيص:

### إضافة مدرسة جديدة:

```php
// في js.php
$schools_data['999999'] = [
    'school_name' => 'مدرسة جديدة',
    'city' => 'مدينة جديدة',
    'students' => [
        ['name' => 'طالب جديد', 'phone' => '966500000000', 'class' => 'صف جديد']
    ]
];
```

### إضافة جلسة جديدة:

```php
// في g.php
$sessions_data['new_session_id'] = [
    'school_id' => '999999',
    'teacher_name' => 'معلم جديد',
    'students' => [...]
];
```

## 📝 ملفات السجل:

- `kushoof_requests.log`: سجل طلبات API المدارس
- `whatsapp_requests.log`: سجل طلبات API WhatsApp
- `noor_requests.log`: سجل طلبات API نور

## 🔒 الأمان:

- التحقق من المعاملات المطلوبة
- تسجيل جميع الطلبات
- حماية من الطلبات الخاطئة
- Headers CORS للتطوير

## 🌟 الميزات المتقدمة:

### 📱 تكامل WhatsApp:

- تمرير البيانات عبر `window.name`
- قوالب رسائل متنوعة
- دعم الغياب والدرجات

### 🎨 واجهات مخصصة:

- CSS متجاوب لكل نظام
- رسوم متحركة وتأثيرات
- إحصائيات مباشرة

### 📊 تحليلات:

- عدد الطلبات
- الجلسات النشطة
- سجل العمليات

## 🚨 ملاحظات مهمة:

1. **للتطوير فقط**: هذا خادم تجريبي وليس للإنتاج
2. **البيانات وهمية**: جميع البيانات تجريبية وليست حقيقية
3. **الأمان**: لا يحتوي على حماية كاملة للإنتاج
4. **الأداء**: مُحسن للاختبار وليس للأحمال العالية

## 🔗 الربط مع الإضافة:

لاستخدام هذا الخادم مع إضافة Kushoof:

1. **تحديث URLs في الإضافة**:

   ```javascript
   // بدلاً من kushoofapp.com
   const baseURL = "http://localhost:8080";
   ```

2. **اختبار التكامل**:

   - افتح محاكاة موقع مدرستي
   - تأكد من تشغيل الخادم المحلي
   - اختبر جلب البيانات

3. **مراقبة السجلات**:
   - راقب ملفات `.log`
   - استخدم واجهة الخادم لمتابعة الطلبات

---

**🎯 الهدف**: توفير بيئة تطوير كاملة لاختبار إضافة Kushoof دون الحاجة للخادم الحقيقي.
