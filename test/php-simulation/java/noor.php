<?php
/**
 * API نظام نور - kushoofapp.com/java/noor.php
 * يحاكي التكامل مع نظام نور التعليمي
 * المسار الجديد المنظم: /java/noor.php
 */

// إعداد headers للاستجابة
header('Content-Type: application/javascript; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// استقبال المعاملات
$key = isset($_GET['k']) ? $_GET['k'] : '';

// التحقق من صحة المفتاح
if ($key !== 'vir3') {
    http_response_code(403);
    echo "// خطأ: مفتاح غير صحيح - API: /java/noor.php";
    exit;
}

// بيانات تجريبية لنظام نور
$noor_data = [
    'schools' => [
        [
            'id' => 'noor_001',
            'name' => 'مدرسة الملك عبدالعزيز الابتدائية',
            'region' => 'الرياض',
            'education_office' => 'تعليم الرياض',
            'principal' => 'أ. عبدالله محمد السعد'
        ],
        [
            'id' => 'noor_002', 
            'name' => 'مدرسة الأميرة نورا المتوسطة',
            'region' => 'مكة المكرمة',
            'education_office' => 'تعليم جدة',
            'principal' => 'أ. فاطمة أحمد الحربي'
        ]
    ],
    'students' => [
        ['name' => 'سعد محمد الدوسري', 'id' => '1234567890', 'grade' => 'الصف الثالث', 'phone' => '966501234567'],
        ['name' => 'لجين عبدالله القحطاني', 'id' => '1234567891', 'grade' => 'الصف الثالث', 'phone' => '966507654321'],
        ['name' => 'فيصل أحمد العتيبي', 'id' => '1234567892', 'grade' => 'الصف الرابع', 'phone' => '966509876543'],
        ['name' => 'رغد سالم الشهري', 'id' => '1234567893', 'grade' => 'الصف الرابع', 'phone' => '966512345678'],
        ['name' => 'تركي عبدالرحمن المطيري', 'id' => '1234567894', 'grade' => 'الصف الخامس', 'phone' => '966587654321']
    ],
    'teachers' => [
        ['name' => 'أ. محمد سعد الغامدي', 'subject' => 'الرياضيات', 'phone' => '966550123456'],
        ['name' => 'أ. نورا علي الزهراني', 'subject' => 'اللغة العربية', 'phone' => '966551234567'],
        ['name' => 'أ. خالد أحمد البقمي', 'subject' => 'العلوم', 'phone' => '966552345678']
    ]
];

// تاريخ اليوم
$arabic_days = [
    'Sunday' => 'الأحد',
    'Monday' => 'الاثنين', 
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة',
    'Saturday' => 'السبت'
];
$day_name = $arabic_days[date('l')] ?? 'اليوم';
$formatted_date = $day_name . ' ' . date('d/m/Y');

// إنشاء كود JavaScript لنظام نور
$js_code = "
// كود نظام نور - Kushoof Integration من /java/noor.php
// تم التحميل في: " . date('Y-m-d H:i:s') . "

console.log('🎓 تم تحميل تكامل نظام نور مع Kushoof من /java/noor.php');

// بيانات نظام نور
var noor_kushoof_data = {
    'system': 'noor',
    'schools': " . json_encode($noor_data['schools'], JSON_UNESCAPED_UNICODE) . ",
    'students': " . json_encode($noor_data['students'], JSON_UNESCAPED_UNICODE) . ",
    'teachers': " . json_encode($noor_data['teachers'], JSON_UNESCAPED_UNICODE) . ",
    'date': '{$formatted_date}',
    'timestamp': '" . date('Y-m-d H:i:s') . "',
    'api_path': '/java/noor.php',
    'api_version': '2.0'
};

// دالة البحث عن عناصر نظام نور
function findNoorElements() {
    // البحث عن عناصر مميزة في نظام نور
    var noorElements = [
        document.querySelector('.header-noor'),
        document.querySelector('#noor-main'),
        document.querySelector('.noor-content'),
        document.getElementById('MainContent')
    ];
    
    var foundElement = null;
    for (var i = 0; i < noorElements.length; i++) {
        if (noorElements[i]) {
            foundElement = noorElements[i];
            break;
        }
    }
    
    if (foundElement) {
        console.log('✅ تم العثور على عناصر نظام نور');
        injectNoorKushoofInterface(foundElement);
    } else {
        console.log('⏳ البحث عن عناصر نظام نور...');
        setTimeout(findNoorElements, 3000);
    }
}

// دالة حقن واجهة Kushoof في نظام نور
function injectNoorKushoofInterface(targetElement) {
    // حقن CSS خاص بنظام نور
    var noorStyle = document.createElement('style');
    noorStyle.textContent = \`
        .noor-kushoof-panel {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .noor-kushoof-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .noor-kushoof-title {
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .noor-api-info {
            font-size: 12px;
            opacity: 0.8;
            background: rgba(255,255,255,0.1);
            padding: 5px 10px;
            border-radius: 3px;
        }
        .noor-kushoof-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .noor-stat-card {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .noor-stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        .noor-stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        .noor-kushoof-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .noor-action-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .noor-action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }
        .noor-data-table {
            background: rgba(255,255,255,0.05);
            border-radius: 5px;
            padding: 10px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
        }
        .noor-data-row {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 12px;
        }
        .noor-data-row:last-child {
            border-bottom: none;
        }
    \`;
    document.head.appendChild(noorStyle);
    
    // إنشاء واجهة نظام نور
    var noorInterface = document.createElement('div');
    noorInterface.className = 'noor-kushoof-panel';
    noorInterface.innerHTML = \`
        <div class='noor-kushoof-header'>
            <div class='noor-kushoof-title'>
                🎓 نظام نور - تكامل Kushoof
            </div>
            <div class='noor-api-info'>
                API: /java/noor.php v2.0
            </div>
        </div>
        
        <div style='text-align: center; margin-bottom: 15px; font-size: 14px; opacity: 0.9;'>
            📅 {$formatted_date}
        </div>
        
        <div class='noor-kushoof-stats'>
            <div class='noor-stat-card'>
                <span class='noor-stat-number'>\` + noor_kushoof_data.schools.length + \`</span>
                <span class='noor-stat-label'>المدارس</span>
            </div>
            <div class='noor-stat-card'>
                <span class='noor-stat-number'>\` + noor_kushoof_data.students.length + \`</span>
                <span class='noor-stat-label'>الطلاب</span>
            </div>
            <div class='noor-stat-card'>
                <span class='noor-stat-number'>\` + noor_kushoof_data.teachers.length + \`</span>
                <span class='noor-stat-label'>المعلمين</span>
            </div>
        </div>
        
        <div class='noor-kushoof-actions'>
            <button class='noor-action-btn' onclick='exportNoorData()'>
                📊 تصدير البيانات
            </button>
            <button class='noor-action-btn' onclick='openNoorWhatsApp()'>
                📱 فتح WhatsApp
            </button>
            <button class='noor-action-btn' onclick='generateNoorReports()'>
                📋 إنشاء التقارير
            </button>
            <button class='noor-action-btn' onclick='hideNoorPanel()'>
                ❌ إخفاء
            </button>
        </div>
        
        <div class='noor-data-table' id='noor-data-display'>
            <div style='text-align: center; padding: 20px; opacity: 0.7;'>
                اختر إجراء لعرض البيانات
            </div>
        </div>
    \`;
    
    // إدراج الواجهة
    targetElement.insertBefore(noorInterface, targetElement.firstChild);
    
    console.log('✅ تم حقن واجهة Kushoof في نظام نور من /java/noor.php');
}

// دوال التفاعل مع نظام نور
function exportNoorData() {
    console.log('📊 تصدير بيانات نظام نور');
    
    var dataStr = JSON.stringify(noor_kushoof_data, null, 2);
    var dataBlob = new Blob([dataStr], {type: 'application/json'});
    var url = URL.createObjectURL(dataBlob);
    
    var a = document.createElement('a');
    a.href = url;
    a.download = 'noor_kushoof_data_' + new Date().getTime() + '.json';
    a.click();
    
    URL.revokeObjectURL(url);
    
    // عرض البيانات في الجدول
    var display = document.getElementById('noor-data-display');
    if (display) {
        display.innerHTML = \`
            <div style='text-align: center; color: #4CAF50; padding: 10px;'>
                ✅ تم تصدير البيانات بنجاح من /java/noor.php
            </div>
        \`;
    }
}

function openNoorWhatsApp() {
    console.log('📱 فتح WhatsApp مع بيانات نظام نور');
    
    // تحضير البيانات لـ WhatsApp
    var whatsappData = {
        'n': noor_kushoof_data.students.map(function(s) { return s.name; }),
        'p': noor_kushoof_data.students.map(function(s) { return s.phone; }),
        'day': noor_kushoof_data.date,
        'source': 'noor_system',
        'schools': noor_kushoof_data.schools,
        'api_path': '/java/noor.php'
    };
    
    // فتح WhatsApp مع البيانات
    var whatsappWindow = window.open('https://web.whatsapp.com', '_blank');
    
    setTimeout(function() {
        if (whatsappWindow && !whatsappWindow.closed) {
            whatsappWindow.name = 'kushoof' + JSON.stringify(whatsappData);
        }
    }, 1000);
    
    // عرض حالة في الجدول
    var display = document.getElementById('noor-data-display');
    if (display) {
        display.innerHTML = \`
            <div style='text-align: center; color: #2196F3; padding: 10px;'>
                📱 تم فتح WhatsApp مع بيانات \` + noor_kushoof_data.students.length + \` طالب<br>
                <small>من API: /java/noor.php</small>
            </div>
        \`;
    }
}

function generateNoorReports() {
    console.log('📋 إنشاء تقارير نظام نور');
    
    var display = document.getElementById('noor-data-display');
    if (display) {
        var reportsHTML = '<div style=\"font-size: 13px;\">';
        reportsHTML += '<div style=\"font-weight: bold; margin-bottom: 10px;\">📋 تقرير سريع من /java/noor.php:</div>';
        
        noor_kushoof_data.students.forEach(function(student, index) {
            reportsHTML += \`
                <div class='noor-data-row'>
                    <span>\` + (index + 1) + \`. \` + student.name + \`</span>
                    <span>\` + student.grade + \`</span>
                    <span>\` + student.phone + \`</span>
                </div>
            \`;
        });
        
        reportsHTML += '</div>';
        display.innerHTML = reportsHTML;
    }
}

function hideNoorPanel() {
    var panel = document.querySelector('.noor-kushoof-panel');
    if (panel) {
        panel.style.display = 'none';
        console.log('❌ تم إخفاء لوحة نظام نور');
    }
}

// بدء عملية البحث والحقن
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', findNoorElements);
} else {
    findNoorElements();
}

console.log('🎯 تم تحميل تكامل نظام نور مع Kushoof من /java/noor.php');
";

// إرسال كود JavaScript
echo $js_code;

// تسجيل الطلب
$log_entry = date('Y-m-d H:i:s') . " - [/java/noor.php] Noor integration loaded, Key: {$key}\n";
file_put_contents(__DIR__ . '/../noor_requests.log', $log_entry, FILE_APPEND | LOCK_EX);

?>
