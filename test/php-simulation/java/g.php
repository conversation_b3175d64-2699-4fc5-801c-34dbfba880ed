<?php
/**
 * API بيانات WhatsApp - kushoofapp.com/java/g.php
 * يحاكي جلب البيانات لـ WhatsApp Web
 * المسار الجديد المنظم: /java/g.php
 */

// إعداد headers للاستجابة
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// استقبال المعاملات
$session_id = isset($_GET['s']) ? $_GET['s'] : '';

// التحقق من صحة المعاملات
if (empty($session_id)) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف الجلسة مفقود', 'api_path' => '/java/g.php']);
    exit;
}

// محاكاة قاعدة بيانات الجلسات
$sessions_data = [
    'abc123def456' => [
        'school_id' => '123456',
        'teacher_name' => 'أ. محمد أحمد السالم',
        'subject' => 'الرياضيات',
        'class' => '3أ',
        'students' => [
            ['name' => 'أحمد محمد العلي', 'phone' => '966501234567'],
            ['name' => 'فاطمة سالم الأحمد', 'phone' => '966507654321'],
            ['name' => 'محمد عبدالله الحسن', 'phone' => '966509876543'],
            ['name' => 'نورا علي السالم', 'phone' => '966512345678']
        ],
        'message_template' => 'عزيزي ولي أمر الطالب ***، نود إعلامكم بأن الطالب ### غائب اليوم. نرجو التواصل معنا.',
        'absence_data' => [
            ['student_index' => 0, 'status' => '3', 'reason' => 'غياب بدون عذر'],
            ['student_index' => 2, 'status' => '1', 'reason' => 'تأخير']
        ]
    ],
    'xyz789uvw012' => [
        'school_id' => '789012',
        'teacher_name' => 'أ. فاطمة علي الزهراني',
        'subject' => 'اللغة العربية',
        'class' => '2م',
        'students' => [
            ['name' => 'يوسف أحمد الغامدي', 'phone' => '966501111111'],
            ['name' => 'زينب محمد الحربي', 'phone' => '966502222222'],
            ['name' => 'عمر سالم البقمي', 'phone' => '966503333333']
        ],
        'message_template' => 'تحية طيبة، نود إبلاغكم بأن الطالب *** حصل على درجة ### في مادة $$$.',
        'grades_data' => [
            ['student_index' => 0, 'grade' => '95', 'subject' => 'اللغة العربية'],
            ['student_index' => 1, 'grade' => '88', 'subject' => 'اللغة العربية'],
            ['student_index' => 2, 'grade' => '92', 'subject' => 'اللغة العربية']
        ]
    ]
];

// البحث عن الجلسة أو إنشاء بيانات افتراضية
$session_data = null;
foreach ($sessions_data as $key => $data) {
    if (strpos($session_id, substr($key, 0, 6)) !== false) {
        $session_data = $data;
        break;
    }
}

// إذا لم توجد الجلسة، إنشاء بيانات افتراضية
if (!$session_data) {
    $session_data = [
        'school_id' => 'default',
        'teacher_name' => 'معلم تجريبي',
        'subject' => 'مادة تجريبية',
        'class' => 'صف تجريبي',
        'students' => [
            ['name' => 'طالب تجريبي 1', 'phone' => '966500000001'],
            ['name' => 'طالب تجريبي 2', 'phone' => '966500000002'],
            ['name' => 'طالب تجريبي 3', 'phone' => 'invalid_number']
        ],
        'message_template' => 'رسالة تجريبية للطالب ***',
        'test_data' => true
    ];
}

// إعداد البيانات للإرسال
$students_names = [];
$students_phones = [];
$absence_info = [];
$grades_info = [];

foreach ($session_data['students'] as $index => $student) {
    $students_names[] = $student['name'];
    $students_phones[] = $student['phone'];
    
    // إضافة بيانات الغياب إذا وجدت
    if (isset($session_data['absence_data'])) {
        $absence_found = false;
        foreach ($session_data['absence_data'] as $absence) {
            if ($absence['student_index'] == $index) {
                $absence_info[] = $absence['status'];
                $absence_found = true;
                break;
            }
        }
        if (!$absence_found) {
            $absence_info[] = '0'; // حاضر
        }
    }
    
    // إضافة بيانات الدرجات إذا وجدت
    if (isset($session_data['grades_data'])) {
        $grade_found = false;
        foreach ($session_data['grades_data'] as $grade) {
            if ($grade['student_index'] == $index) {
                $grades_info[] = $grade['grade'];
                $grade_found = true;
                break;
            }
        }
        if (!$grade_found) {
            $grades_info[] = '-';
        }
    }
}

// تاريخ اليوم
$today = date('l d/m/Y');
$arabic_days = [
    'Sunday' => 'الأحد',
    'Monday' => 'الاثنين',
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة',
    'Saturday' => 'السبت'
];
$day_name = $arabic_days[date('l')] ?? 'اليوم';
$formatted_date = $day_name . ' ' . date('d/m/Y');

// إعداد الاستجابة
$response_data = [
    'n' => $students_names,
    'p' => $students_phones,
    'day' => $formatted_date,
    'school_id' => $session_data['school_id'],
    'teacher' => $session_data['teacher_name'],
    'subject' => $session_data['subject'],
    'class' => $session_data['class'],
    'sm' => $session_data['message_template'],
    'session_id' => $session_id,
    'timestamp' => date('Y-m-d H:i:s'),
    'api_path' => '/java/g.php'
];

// إضافة بيانات الغياب إذا وجدت
if (!empty($absence_info)) {
    $response_data['t'] = $absence_info; // بيانات الحضور والغياب
}

// إضافة بيانات الدرجات إذا وجدت
if (!empty($grades_info)) {
    $response_data['c'] = $grades_info; // بيانات الدرجات
}

// إضافة معلومات إضافية
$response_data['meta'] = [
    'total_students' => count($students_names),
    'valid_phones' => count(array_filter($students_phones, function($phone) {
        return strlen($phone) >= 12;
    })),
    'generated_at' => date('c'),
    'source' => 'kushoof_simulation',
    'api_version' => '2.0',
    'organized_path' => true
];

// تسجيل الطلب
$log_entry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'api_path' => '/java/g.php',
    'session_id' => $session_id,
    'school_id' => $session_data['school_id'],
    'students_count' => count($students_names),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
];

file_put_contents(__DIR__ . '/../whatsapp_requests.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

// إرسال الاستجابة
echo 'kushoof' . json_encode($response_data, JSON_UNESCAPED_UNICODE);

?>
