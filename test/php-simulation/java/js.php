<?php
/**
 * API بيانات المدارس - kushoofapp.com/java/js.php
 * يحاكي الاستجابة التي ترسل بيانات الطلاب بصيغة JavaScript
 * المسار الجديد المنظم: /java/js.php
 */

// إعداد headers للاستجابة
header('Content-Type: application/javascript; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// استقبال المعاملات
$version = isset($_GET['version']) ? $_GET['version'] : '258';
$school_id = isset($_GET['id']) ? $_GET['id'] : '';
$key = isset($_GET['k']) ? $_GET['k'] : '';

// التحقق من صحة المعاملات
if (empty($school_id) || empty($key)) {
    http_response_code(400);
    echo "// خطأ: معاملات مفقودة";
    exit;
}

// بيانات تجريبية للمدارس المختلفة
$schools_data = [
    '123456' => [
        'school_name' => 'مدرسة الأمل الابتدائية',
        'city' => 'الرياض',
        'students' => [
            ['name' => 'أحمد محمد العلي', 'phone' => '966501234567', 'class' => '3أ'],
            ['name' => 'فاطمة سالم الأحمد', 'phone' => '966507654321', 'class' => '3أ'],
            ['name' => 'محمد عبدالله الحسن', 'phone' => '966509876543', 'class' => '3ب'],
            ['name' => 'نورا علي السالم', 'phone' => '966512345678', 'class' => '3ب'],
            ['name' => 'خالد أحمد المحمد', 'phone' => '966587654321', 'class' => '3ج'],
            ['name' => 'سارة محمد العتيبي', 'phone' => 'invalid', 'class' => '3ج'],
            ['name' => 'عبدالرحمن سعد القحطاني', 'phone' => '966555123456', 'class' => '4أ'],
            ['name' => 'مريم عبدالله الزهراني', 'phone' => '966566789012', 'class' => '4أ']
        ]
    ],
    '789012' => [
        'school_name' => 'مدرسة النور المتوسطة',
        'city' => 'جدة',
        'students' => [
            ['name' => 'يوسف أحمد الغامدي', 'phone' => '966501111111', 'class' => '1م'],
            ['name' => 'زينب محمد الحربي', 'phone' => '966502222222', 'class' => '1م'],
            ['name' => 'عمر سالم البقمي', 'phone' => '966503333333', 'class' => '2م'],
            ['name' => 'هند علي الشهري', 'phone' => '966504444444', 'class' => '2م'],
            ['name' => 'ماجد عبدالعزيز السبيعي', 'phone' => '966505555555', 'class' => '3م']
        ]
    ],
    '345678' => [
        'school_name' => 'مدرسة المستقبل الثانوية',
        'city' => 'الدمام',
        'students' => [
            ['name' => 'ريان محمد الدوسري', 'phone' => '966506666666', 'class' => '1ث'],
            ['name' => 'لينا أحمد العنزي', 'phone' => '966507777777', 'class' => '1ث'],
            ['name' => 'فهد سعد المطيري', 'phone' => '966508888888', 'class' => '2ث'],
            ['name' => 'رهف علي الرشيد', 'phone' => '966509999999', 'class' => '2ث']
        ]
    ]
];

// الحصول على بيانات المدرسة
$school_data = isset($schools_data[$school_id]) ? $schools_data[$school_id] : null;

if (!$school_data) {
    // إنشاء بيانات افتراضية للمدارس غير المعرفة
    $school_data = [
        'school_name' => 'مدرسة تجريبية - ' . $school_id,
        'city' => 'مدينة تجريبية',
        'students' => [
            ['name' => 'طالب تجريبي 1', 'phone' => '966500000001', 'class' => 'صف تجريبي'],
            ['name' => 'طالب تجريبي 2', 'phone' => '966500000002', 'class' => 'صف تجريبي'],
            ['name' => 'طالب تجريبي 3', 'phone' => 'invalid', 'class' => 'صف تجريبي']
        ]
    ];
}

// إعداد البيانات للإرسال
$students_names = [];
$students_phones = [];
$students_classes = [];

foreach ($school_data['students'] as $student) {
    $students_names[] = urlencode($student['name']);
    $students_phones[] = $student['phone'];
    $students_classes[] = urlencode($student['class']);
}

// تاريخ اليوم
$today = date('l d/m/Y');
$arabic_days = [
    'Sunday' => 'الأحد',
    'Monday' => 'الاثنين',
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة',
    'Saturday' => 'السبت'
];
$day_name = $arabic_days[date('l')] ?? 'اليوم';
$formatted_date = $day_name . ' ' . date('d/m/Y');

// إنشاء كود JavaScript للحقن
$js_code = "
// كود محقون من kushoofapp.com/java/js.php - الإصدار {$version}
// معرف المدرسة: {$school_id}

console.log('🚀 تم تحميل بيانات المدرسة: {$school_data['school_name']}');
console.log('📍 المسار الجديد: kushoofapp.com/java/js.php');

// بيانات الطلاب
var kushoof_data = {
    'n': ['" . implode("', '", $students_names) . "'],
    'p': ['" . implode("', '", $students_phones) . "'],
    'c': ['" . implode("', '", $students_classes) . "'],
    'day': '{$formatted_date}',
    'school_id': '{$school_id}',
    'school_name': '" . urlencode($school_data['school_name']) . "',
    'city': '" . urlencode($school_data['city']) . "',
    'version': '{$version}',
    'timestamp': '" . date('Y-m-d H:i:s') . "',
    'api_path': '/java/js.php'
};

// تخزين البيانات في window.name للاستخدام في WhatsApp
if (typeof window !== 'undefined') {
    window.name = 'kushoof' + JSON.stringify(kushoof_data);
    console.log('📊 تم تخزين بيانات ' + kushoof_data.n.length + ' طالب');
}

// دالة البحث عن header وحقن الواجهة
function findHeaderAndInject() {
    var headers = document.getElementsByTagName('header');
    
    if (headers.length > 0) {
        console.log('✅ تم العثور على header - جاري حقن واجهة Kushoof');
        injectKushoofInterface();
    } else {
        console.log('⏳ البحث عن header...');
        setTimeout(findHeaderAndInject, 2000);
    }
}

// دالة حقن واجهة Kushoof
function injectKushoofInterface() {
    // حقن CSS
    var style = document.createElement('style');
    style.textContent = \`
        .kushoof-interface {
            background: linear-gradient(135deg, #128c7e 0%, #075e54 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .kushoof-header {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .kushoof-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
        }
        .kushoof-btn {
            background: #4682b4;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .kushoof-btn:hover {
            background: #5a9bd4;
        }
        .kushoof-student-list {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .kushoof-student {
            padding: 5px;
            margin: 2px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
            font-size: 12px;
        }
        .kushoof-invalid {
            color: #ffcccb;
            text-decoration: line-through;
        }
    \`;
    document.head.appendChild(style);
    
    // إنشاء HTML الواجهة
    var interfaceHTML = \`
        <div class='kushoof-interface'>
            <div class='kushoof-header'>
                🚀 Kushoof - {$school_data['school_name']}
            </div>
            
            <div class='kushoof-info'>
                📍 {$school_data['city']} | 📅 {$formatted_date}<br>
                👥 عدد الطلاب: \` + kushoof_data.n.length + \`<br>
                🔗 API: /java/js.php (v{$version})
            </div>
            
            <div class='kushoof-student-list'>
    \`;
    
    for (var i = 0; i < kushoof_data.n.length; i++) {
        var studentClass = kushoof_data.p[i].length < 12 ? 'kushoof-student kushoof-invalid' : 'kushoof-student';
        var statusIcon = kushoof_data.p[i].length < 12 ? '❌' : '✅';
        
        interfaceHTML += \`
            <div class='\` + studentClass + \`'>
                \` + statusIcon + \` \` + (i + 1) + \`. \` + decodeURIComponent(kushoof_data.n[i]) + \`
                <br><small>📱 \` + kushoof_data.p[i] + \` | 🏫 \` + decodeURIComponent(kushoof_data.c[i]) + \`</small>
            </div>
        \`;
    }
    
    interfaceHTML += \`
            </div>
            
            <div>
                <button class='kushoof-btn' onclick='openWhatsAppWithData()'>
                    📱 فتح WhatsApp
                </button>
                <button class='kushoof-btn' onclick='downloadStudentsData()'>
                    📥 تحميل البيانات
                </button>
                <button class='kushoof-btn' onclick='hideKushoofInterface()'>
                    ❌ إخفاء
                </button>
            </div>
        </div>
    \`;
    
    // إضافة الواجهة إلى الصفحة
    var interfaceDiv = document.createElement('div');
    interfaceDiv.id = 'kushoof-interface';
    interfaceDiv.innerHTML = interfaceHTML;
    
    // إدراج الواجهة في أعلى الصفحة
    document.body.insertBefore(interfaceDiv, document.body.firstChild);
    
    console.log('✅ تم حقن واجهة Kushoof بنجاح من /java/js.php');
}

// دالة فتح WhatsApp مع البيانات
function openWhatsAppWithData() {
    console.log('📱 فتح WhatsApp مع البيانات...');
    var whatsappWindow = window.open('https://web.whatsapp.com', '_blank');
    
    // تمرير البيانات للنافذة الجديدة
    setTimeout(function() {
        if (whatsappWindow && !whatsappWindow.closed) {
            whatsappWindow.name = window.name;
        }
    }, 1000);
}

// دالة تحميل البيانات
function downloadStudentsData() {
    var dataStr = JSON.stringify(kushoof_data, null, 2);
    var dataBlob = new Blob([dataStr], {type: 'application/json'});
    var url = URL.createObjectURL(dataBlob);
    
    var a = document.createElement('a');
    a.href = url;
    a.download = 'kushoof_students_' + kushoof_data.school_id + '_' + new Date().getTime() + '.json';
    a.click();
    
    URL.revokeObjectURL(url);
    console.log('📥 تم تحميل بيانات الطلاب');
}

// دالة إخفاء الواجهة
function hideKushoofInterface() {
    var interface = document.getElementById('kushoof-interface');
    if (interface) {
        interface.style.display = 'none';
        console.log('❌ تم إخفاء واجهة Kushoof');
    }
}

// بدء عملية البحث والحقن
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', findHeaderAndInject);
} else {
    findHeaderAndInject();
}

console.log('🎯 تم تحميل سكريبت Kushoof من /java/js.php - الإصدار {$version}');
";

// إرسال كود JavaScript
echo $js_code;

// تسجيل الطلب في ملف log
$log_entry = date('Y-m-d H:i:s') . " - [/java/js.php] School ID: {$school_id}, Version: {$version}, Students: " . count($school_data['students']) . "\n";
file_put_contents(__DIR__ . '/../kushoof_requests.log', $log_entry, FILE_APPEND | LOCK_EX);

?>
