<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكاة خادم Kushoof - PHP Simulation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #128c7e 0%, #075e54 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .api-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #128c7e;
        }
        
        .api-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .api-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .endpoint {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .method {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .params {
            background: #ecf0f1;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .param {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        
        .param:last-child {
            border-bottom: none;
        }
        
        .param-name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .param-desc {
            color: #666;
            font-size: 0.9em;
        }
        
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2980b9;
        }
        
        .response-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            white-space: pre-wrap;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .logs {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .footer {
            background: #34495e;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 محاكاة خادم Kushoof</h1>
            <p>خادم PHP تجريبي لمحاكاة APIs موقع kushoofapp.com</p>
        </div>
        
        <div class="content">
            <!-- إحصائيات سريعة -->
            <div class="stats">
                <div class="stat-card">
                    <span class="stat-number">3</span>
                    <span class="stat-label">APIs متاحة</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="total-requests">0</span>
                    <span class="stat-label">إجمالي الطلبات</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="active-sessions">0</span>
                    <span class="stat-label">الجلسات النشطة</span>
                </div>
            </div>
            
            <!-- API للمدارس -->
            <div class="api-section">
                <div class="api-title">
                    🏫 API بيانات المدارس
                </div>
                <div class="api-description">
                    يحاكي الـ API الذي يجلب بيانات الطلاب من موقع مدرستي ويرسل كود JavaScript للحقن في الصفحة.
                </div>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    /java/js.php?version={version}&id={school_id}&k={key}
                </div>
                
                <div class="params">
                    <div class="param">
                        <span class="param-name">version</span>
                        <span class="param-desc">إصدار السكريپت (258, 280)</span>
                    </div>
                    <div class="param">
                        <span class="param-name">id</span>
                        <span class="param-desc">معرف المدرسة</span>
                    </div>
                    <div class="param">
                        <span class="param-name">k</span>
                        <span class="param-desc">مفتاح التحقق (K)</span>
                    </div>
                </div>
                
                <button class="test-button" onclick="testSchoolAPI()">🧪 اختبار API</button>
                <button class="test-button" onclick="testSchoolAPI('789012')">🧪 اختبار مدرسة أخرى</button>
            </div>
            
            <!-- API لـ WhatsApp -->
            <div class="api-section">
                <div class="api-title">
                    💬 API بيانات WhatsApp
                </div>
                <div class="api-description">
                    يحاكي الـ API الذي يجلب البيانات المخزنة للاستخدام في WhatsApp Web.
                </div>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    /java/g.php?s={session_id}
                </div>
                
                <div class="params">
                    <div class="param">
                        <span class="param-name">s</span>
                        <span class="param-desc">معرف الجلسة</span>
                    </div>
                </div>
                
                <button class="test-button" onclick="testWhatsAppAPI()">🧪 اختبار API</button>
                <button class="test-button" onclick="testWhatsAppAPI('xyz789uvw012')">🧪 اختبار جلسة أخرى</button>
            </div>
            
            <!-- API لنظام نور -->
            <div class="api-section">
                <div class="api-title">
                    🎓 API نظام نور
                </div>
                <div class="api-description">
                    يحاكي التكامل مع نظام نور التعليمي ويرسل كود JavaScript للتفاعل مع النظام.
                </div>
                
                <div class="endpoint">
                    <span class="method">GET</span>
                    /java/noor.php?k={key}
                </div>
                
                <div class="params">
                    <div class="param">
                        <span class="param-name">k</span>
                        <span class="param-desc">مفتاح التحقق (vir3)</span>
                    </div>
                </div>
                
                <button class="test-button" onclick="testNoorAPI()">🧪 اختبار API</button>
            </div>
            
            <!-- منطقة عرض الاستجابات -->
            <div class="api-section">
                <div class="api-title">
                    📋 استجابة API
                </div>
                <div class="response-area" id="response-area">
                    اضغط على أي زر "اختبار API" لعرض الاستجابة هنا...
                </div>
            </div>
            
            <!-- سجل الطلبات -->
            <div class="api-section">
                <div class="api-title">
                    📊 سجل الطلبات المباشر
                </div>
                <div class="logs" id="logs">
                    انتظار الطلبات...
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>محاكاة خادم Kushoof - تم إنشاؤه لأغراض التطوير والاختبار</p>
            <p>📧 للدعم التقني: تواصل مع فريق التطوير</p>
        </div>
    </div>

    <script>
        // متغيرات الإحصائيات
        let totalRequests = 0;
        let activeSessions = 0;
        
        // دالة اختبار API المدارس
        function testSchoolAPI(schoolId = '123456') {
            totalRequests++;
            updateStats();

            const url = `java/js.php?version=258&id=${schoolId}&k=K`;
            logRequest('GET', url);
            
            fetch(url)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('response-area').textContent = data;
                    logResponse('✅ نجح', data.length + ' حرف');
                })
                .catch(error => {
                    document.getElementById('response-area').textContent = 'خطأ: ' + error.message;
                    logResponse('❌ فشل', error.message);
                });
        }
        
        // دالة اختبار API WhatsApp
        function testWhatsAppAPI(sessionId = 'abc123def456') {
            totalRequests++;
            updateStats();

            const url = `java/g.php?s=${sessionId}`;
            logRequest('GET', url);
            
            fetch(url)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('response-area').textContent = data;
                    logResponse('✅ نجح', data.length + ' حرف');
                })
                .catch(error => {
                    document.getElementById('response-area').textContent = 'خطأ: ' + error.message;
                    logResponse('❌ فشل', error.message);
                });
        }
        
        // دالة اختبار API نور
        function testNoorAPI() {
            totalRequests++;
            updateStats();

            const url = 'java/noor.php?k=vir3';
            logRequest('GET', url);
            
            fetch(url)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('response-area').textContent = data;
                    logResponse('✅ نجح', data.length + ' حرف');
                })
                .catch(error => {
                    document.getElementById('response-area').textContent = 'خطأ: ' + error.message;
                    logResponse('❌ فشل', error.message);
                });
        }
        
        // دالة تحديث الإحصائيات
        function updateStats() {
            document.getElementById('total-requests').textContent = totalRequests;
            document.getElementById('active-sessions').textContent = Math.floor(Math.random() * 5) + 1;
        }
        
        // دالة تسجيل الطلبات
        function logRequest(method, url) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${method} ${url}`;
            addToLogs(logEntry);
        }
        
        // دالة تسجيل الاستجابات
        function logResponse(status, details) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${status} - ${details}`;
            addToLogs(logEntry);
        }
        
        // دالة إضافة إلى السجل
        function addToLogs(entry) {
            const logsElement = document.getElementById('logs');
            logsElement.textContent += entry + '\n';
            logsElement.scrollTop = logsElement.scrollHeight;
        }
        
        // تحديث الإحصائيات كل 5 ثواني
        setInterval(() => {
            activeSessions = Math.floor(Math.random() * 8) + 1;
            document.getElementById('active-sessions').textContent = activeSessions;
        }, 5000);
        
        // رسالة ترحيب في السجل
        addToLogs('🚀 خادم Kushoof جاهز للاستقبال...');
        addToLogs('📡 جميع APIs متاحة ومجهزة للاختبار');
    </script>
</body>
</html>
