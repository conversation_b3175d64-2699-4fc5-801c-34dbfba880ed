// content.js - مصمم الكشوف النهائي

console.log('🚀 [Kushoof] تم تحميل إضافة مصمم الكشوف');

// التحقق من أننا في موقع مدرستي
if (window.location.href.includes('schools.madrasati.sa')) {
    console.log('✅ [Kushoof] نحن في موقع مدرستي');
    
    // انتظار تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initKushoof);
    } else {
        initKushoof();
    }
}

function initKushoof() {
    console.log('🔧 [Kushoof] بدء تهيئة الإضافة');
    createKushoofButton();
}

function createKushoofButton() {
    // التأكد من عدم وجود زر سابق
    const existingButton = document.getElementById('kushoof-btn');
    if (existingButton) {
        existingButton.remove();
    }
    
    // إنشاء زر عائم
    const floatingButton = document.createElement('div');
    floatingButton.id = 'kushoof-btn';
    floatingButton.innerHTML = '🎯 مصمم الكشوف';
    floatingButton.style.cssText = `
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        background: linear-gradient(45deg, #007bff, #0056b3) !important;
        color: white !important;
        padding: 15px 20px !important;
        border-radius: 25px !important;
        cursor: pointer !important;
        z-index: 999999 !important;
        font-weight: bold !important;
        font-size: 16px !important;
        box-shadow: 0 4px 15px rgba(0,123,255,0.5) !important;
        transition: all 0.3s ease !important;
        font-family: Arial, sans-serif !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        min-width: 150px !important;
        text-align: center !important;
        border: 2px solid white !important;
    `;
    
    floatingButton.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1)';
        this.style.boxShadow = '0 6px 20px rgba(0,123,255,0.7)';
    });
    
    floatingButton.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.boxShadow = '0 4px 15px rgba(0,123,255,0.5)';
    });
    
    floatingButton.addEventListener('click', function() {
        console.log('🖱️ [Kushoof] تم الضغط على الزر');
        showKushoofMenu();
    });
    
    document.body.appendChild(floatingButton);
    console.log('✅ [Kushoof] تم إنشاء الزر العائم');
}

function showKushoofMenu() {
    // إزالة القائمة السابقة إن وجدت
    const existingMenu = document.getElementById('kushoof-menu');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }
    
    const menu = document.createElement('div');
    menu.id = 'kushoof-menu';
    menu.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: white;
        border: 3px solid #007bff;
        border-radius: 15px;
        padding: 25px;
        z-index: 999998;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        min-width: 300px;
        font-family: Arial, sans-serif;
    `;
    
    menu.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px;">
            <h3 style="color: #007bff; margin: 0 0 10px 0;">🎯 مصمم الكشوف</h3>
            <p style="margin: 0; color: #666; font-size: 14px;">استخراج بيانات الطلاب من مدرستي</p>
        </div>
        
        <button id="extract-btn" style="
            width: 100%;
            margin: 8px 0;
            padding: 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        ">
            📊 استخراج وحفظ البيانات
        </button>
        
        <button id="designer-btn" style="
            width: 100%;
            margin: 8px 0;
            padding: 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        ">
            🎨 مصمم الكشوف (نافذة صغيرة)
        </button>
        
        <button id="test-btn" style="
            width: 100%;
            margin: 8px 0;
            padding: 15px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        ">
            🔗 اختبار الاتصال
        </button>

        <button id="test-school-btn" style="
            width: 100%;
            margin: 8px 0;
            padding: 15px;
            background: #6f42c1;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        ">
            🏫 اختبار بيانات المدرسة
        </button>

        <button id="test-db-btn" style="
            width: 100%;
            margin: 8px 0;
            padding: 15px;
            background: #fd7e14;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        ">
            🗄️ اختبار قاعدة البيانات
        </button>
        
        <button onclick="document.getElementById('kushoof-menu').remove()" style="
            width: 100%;
            margin: 15px 0 0 0;
            padding: 10px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        ">
            ✖️ إغلاق
        </button>
    `;
    
    document.body.appendChild(menu);
    
    // ربط الأزرار بالدوال
    const extractBtn = menu.querySelector('#extract-btn');
    const designerBtn = menu.querySelector('#designer-btn');
    const testBtn = menu.querySelector('#test-btn');
    const testSchoolBtn = menu.querySelector('#test-school-btn');
    const testDbBtn = menu.querySelector('#test-db-btn');

    if (extractBtn) {
        extractBtn.addEventListener('click', function() {
            console.log('🖱️ [Menu] تم الضغط على زر الاستخراج');
            menu.remove();
            extractData();
        });
    }

    if (designerBtn) {
        designerBtn.addEventListener('click', function() {
            console.log('🖱️ [Menu] تم الضغط على زر المصمم');
            menu.remove();
            openDesignerSmallWindow();
        });
    }

    if (testBtn) {
        testBtn.addEventListener('click', function() {
            console.log('🖱️ [Menu] تم الضغط على زر الاختبار');
            menu.remove();
            testConnection();
        });
    }

    if (testSchoolBtn) {
        testSchoolBtn.addEventListener('click', function() {
            console.log('🖱️ [Menu] تم الضغط على زر اختبار بيانات المدرسة');
            menu.remove();
            testSchoolData();
        });
    }

    if (testDbBtn) {
        testDbBtn.addEventListener('click', function() {
            console.log('🖱️ [Menu] تم الضغط على زر اختبار قاعدة البيانات');
            menu.remove();
            testDatabase();
        });
    }
    
    console.log('✅ [Kushoof] تم عرض القائمة وربط الأزرار');
}

// ==== الدوال الأساسية ====

/*** دالة لاستخراج معرف المدرسة من الرابط أو المحتوى ***/
function getSchoolId() {
    const currentUrl = window.location.href;

    console.log('🔍 [School ID] البحث في الرابط:', currentUrl);

    // أنماط مختلفة للبحث عن معرف المدرسة
    const patterns = [
        /Index\/([A-F0-9]{32})/i,                    // /Index/20E4F8A116ED799CCFC7A85320DEB4B1
        /\/([A-F0-9]{32})/i,                         // /20E4F8A116ED799CCFC7A85320DEB4B1
        /schoolId[=\/]([A-F0-9]{32})/i,              // schoolId=20E4F8A116ED799CCFC7A85320DEB4B1
        /Schools\/([A-F0-9]{32})/i,                  // Schools/20E4F8A116ED799CCFC7A85320DEB4B1
        /ManageStudents\/([A-F0-9]{32})/i            // ManageStudents/20E4F8A116ED799CCFC7A85320DEB4B1
    ];

    for (const pattern of patterns) {
        const match = currentUrl.match(pattern);
        if (match) {
            console.log('✅ [School ID] تم العثور على معرف المدرسة:', match[1]);
            return match[1];
        }
    }

    // البحث في محتوى الصفحة
    console.log('🔍 [School ID] البحث في محتوى الصفحة...');
    const pageContent = document.body.innerHTML;
    const contentMatch = pageContent.match(/([A-F0-9]{32})/i);
    if (contentMatch) {
        console.log('✅ [School ID] تم العثور على معرف المدرسة في المحتوى:', contentMatch[1]);
        return contentMatch[1];
    }

    console.log('❌ [School ID] لم يتم العثور على معرف المدرسة');
    return null;
}

/*** دالة لتحويل رقم الجوال إلى تنسيق دولي ***/
function normalizePhone(input) {
    const t = input.replace(/[\s\-–—]/g, '').trim();
    return t.startsWith('0') ? '966' + t.slice(1) : t;
}

/*** دالة لجلب تفاصيل طالب واحد من رابط ملف الطالب ***/
async function fetchStudentDetail(url) {
    try {
        const res = await fetch(url, { credentials: 'include' });
        const html = await res.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');

        // البيانات الأساسية
        const name = doc.querySelector("h2#FullNameId")?.innerText.trim() || "";
        const parentMobile = doc.querySelector("#ParentMobile")?.value.trim() || "";
        const parentPhone = normalizePhone(parentMobile);

        // رقم جوال الطالب من input#Mobile
        const studentMobile = doc.querySelector("#Mobile")?.value.trim() || "";
        const studentPhone = normalizePhone(studentMobile);

        // اسم المستخدم من label for="MicrosoftUserName"
        let username = "";
        const usernameLabel = doc.querySelector('label[for="MicrosoftUserName"]');
        if (usernameLabel) {
            username = usernameLabel.textContent.trim();
        }
        // محاولة بديلة للبحث عن اسم المستخدم
        if (!username) {
            const usernameInput = doc.querySelector("#UserName, #MicrosoftUserName");
            if (usernameInput) {
                username = usernameInput.value?.trim() || usernameInput.textContent?.trim() || "";
            }
        }

        // السجل المدني
        const nationalId = doc.querySelector("#NationalId")?.value.trim() || "";

        // معلومات الفصل والصف
        const h6 = doc.querySelector("h6.fw-medium");
        let clsRaw = "";
        if (h6) {
            const c = h6.cloneNode(true);
            c.querySelector("small")?.remove();
            clsRaw = c.innerText.trim();
        }
        clsRaw = clsRaw.replace(/[٠-٩]/g, d => String.fromCharCode(d.charCodeAt(0) - 0x0660 + 0x30));
        const parts = clsRaw.match(/^(.+?)[\s-]?(\d+)$/u);
        const classLabel = parts ? `${parts[1].trim()}-${parts[2]}` : clsRaw;

        // استخراج الصف والفصل منفصلين
        const grade = parts ? parts[1].trim() : clsRaw;
        const section = parts ? parts[2] : "";

        console.log(`👤 [Student Detail] ${name}: phone=${studentPhone}, username=${username}, nationalId=${nationalId}`);

        if (name) {
            return {
                name,                    // اسم الطالب
                parentPhone,            // رقم جوال ولي الأمر
                studentPhone,           // رقم جوال الطالب
                username,               // اسم المستخدم
                nationalId,             // السجل المدني
                classLabel,             // الفصل الكامل (مثل: الأول-1)
                grade,                  // الصف (مثل: الأول)
                section                 // الفصل (مثل: 1)
            };
        }
    } catch (e) {
        console.error("fetchStudentDetail failed:", e);
    }
    return null;
}

/*** دالة لجمع جميع الطلاب (الطريقة الأصلية) ***/
async function collectStudents(sid) {
    console.log('📊 [Collect] بدء جمع الطلاب...');
    
    if (!sid) return [];
    
    const base = `https://schools.madrasati.sa/SchoolManagment/Students/ManageStudents/${sid}`;
    let page = 1;
    let links = [];

    console.log('🔍 [Collect] البحث عن روابط الطلاب...');

    while (true) {
        try {
            console.log(`📄 [Collect] فحص الصفحة ${page}...`);
            
            const html = await fetch(`${base}?PageNumber=${page}&years=-1&semesters=-1&classrooms=-1`, { 
                credentials: 'include' 
            }).then(r => r.text());
            
            const doc = new DOMParser().parseFromString(html, 'text/html');
            const hrefs = Array.from(doc.querySelectorAll("a[href*='StudentInfo']")).map(a => a.href);
            
            if (!hrefs.length) {
                console.log(`📄 [Collect] لا توجد روابط في الصفحة ${page}, انتهاء البحث`);
                break;
            }
            
            console.log(`📄 [Collect] تم العثور على ${hrefs.length} رابط في الصفحة ${page}`);
            links.push(...hrefs);
            page++;
            
            if (page > 50) {
                console.log('⚠️ [Collect] تم الوصول للحد الأقصى من الصفحات (50)');
                break;
            }
            
        } catch (error) {
            console.error(`❌ [Collect] خطأ في الصفحة ${page}:`, error);
            break;
        }
    }

    links = Array.from(new Set(links));
    console.log(`🔗 [Collect] إجمالي الروابط الفريدة: ${links.length}`);

    if (links.length === 0) {
        throw new Error('لم يتم العثور على أي روابط طلاب');
    }

    const all = [];
    console.log('👥 [Collect] بدء جمع تفاصيل الطلاب...');

    for (let i = 0; i < links.length; i++) {
        try {
            console.log(`👤 [Collect] جمع بيانات الطالب ${i + 1}/${links.length}...`);
            
            const stu = await fetchStudentDetail(links[i]);
            if (stu) {
                all.push(stu);
                console.log(`✅ [Collect] تم جمع بيانات: ${stu.name} - ${stu.classLabel}`);
            }
            
            if (i % 10 === 0 && i > 0) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
        } catch (error) {
            console.error(`❌ [Collect] خطأ في جمع بيانات الطالب ${i + 1}:`, error);
        }
    }

    console.log(`✅ [Collect] تم جمع ${all.length} طالب من أصل ${links.length} رابط`);
    return all;
}

// ==== دالة فتح النافذة الصغيرة للمصمم ====

async function openDesignerSmallWindow() {
    console.log('🎨 [Designer] فتح مصمم الكشوف الآمن...');

    const sid = getSchoolId();
    if (!sid) {
        alert('❌ لم يتم العثور على معرف المدرسة\n\nتأكد من أنك في صفحة مدرسة صحيحة في منصة مدرستي');
        return;
    }

    // فتح النافذة بدون شريط العنوان
    const features = 'width=620,height=680,resizable=no,scrollbars=no,toolbar=no,menubar=no,location=no,status=no,titlebar=no,directories=no';
    const url = `https://kushoofapp.com/js/designer.php?sid=${sid}&embedded=false&load_from_db=true`;

    const newWindow = window.open(url, 'KushoofDesigner', features);

    if (newWindow) {
        // محاولة إضافية لتحديد الحجم والموضع
        setTimeout(() => {
            newWindow.resizeTo(620, 680);
            newWindow.moveTo(
                (screen.width - 620) / 2,
                (screen.height - 680) / 2
            );

            // محاولة إخفاء شريط العنوان
            try {
                newWindow.document.title = '';
            } catch (e) {
                console.log('لا يمكن تعديل العنوان');
            }
        }, 500);

        console.log('✅ [Designer] تم فتح نافذة المصمم');

        // إرسال البيانات للمصمم بعد التحميل
        setTimeout(() => {
            sendDataToDesignerWindow(newWindow, sid);
        }, 3000);

    } else {
        alert('❌ فشل في فتح النافذة\n\nتأكد من السماح للنوافذ المنبثقة في المتصفح');
    }
}

// دالة إنشاء التوقيع الآمن
async function generateSecureSignature(schoolId, timestamp) {
    const data = schoolId + timestamp + getClientFingerprint();

    // استخدام crypto API إذا كان متاح
    if (window.crypto && window.crypto.subtle) {
        try {
            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(data);
            const hashBuffer = await window.crypto.subtle.digest('SHA-256', dataBuffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
        } catch (e) {
            console.warn('Crypto API not available, using fallback');
        }
    }

    // fallback hash function
    return simpleHash(data).substring(0, 16);
}

// دالة بصمة المتصفح
function getClientFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Security fingerprint', 2, 2);

    return [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL()
    ].join('|').substring(0, 32);
}

// دالة hash بسيطة
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
}

// ==== دالة فتح مودال المصمم (الشكل الأصلي الجميل) ====

async function openDesignerModal() {
    console.log('🎨 [Designer] فتح مصمم الكشوف...');

    const sid = getSchoolId();
    if (!sid) {
        alert('❌ لم يتم العثور على معرف المدرسة\n\nتأكد من أنك في صفحة مدرسة صحيحة في منصة مدرستي');
        return;
    }

    // إنشاء المودال الجميل الأصلي
    const modal = document.createElement('div');
    modal.id = 'designer-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 999999;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    modal.innerHTML = `
        <div style="
            background: white;
            width: 90%;
            height: 85%;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        ">
            <div style="
                position: absolute;
                top: 15px;
                right: 20px;
                z-index: 1000000;
            ">
                <button onclick="document.getElementById('designer-modal').remove()" style="
                    background: #ff4757;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 18px;
                    font-weight: bold;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                ">✕</button>
            </div>
            <iframe
                id="designer-iframe"
                src="https://kushoofapp.com/js/designer.php?sid=${sid}&ref=madrasati&load_from_db=true"
                style="width: 100%; height: 100%; border: none; border-radius: 15px;"
                frameborder="0">
            </iframe>
        </div>
    `;

    document.body.appendChild(modal);

    console.log('✅ [Designer] تم إنشاء المودال الجميل');

    // إرسال البيانات للمصمم بعد التحميل
    setTimeout(() => {
        sendDataToDesigner(sid);
    }, 2000);

    // إرسال إضافي بعد 4 ثوان مع محاولة جلب بيانات أكثر دقة
    setTimeout(() => {
        const iframe = document.getElementById('designer-iframe');
        if (iframe && iframe.contentWindow) {
            console.log('📤 [Content] إرسال إضافي مع بيانات محدثة...');

            // محاولة جلب البيانات من kushoof_school_data المحفوظة
            let finalData = {};

            try {
                const storedData = localStorage.getItem('kushoof_school_data');
                if (storedData) {
                    const parsedData = JSON.parse(storedData);
                    finalData = {
                        schoolId: sid,
                        schoolName: parsedData.schoolName || 'مدرسة غير محددة',
                        managerName: parsedData.managerName || 'مدير غير محدد',
                        ministryNumber: parsedData.ministryNumber || 'غير محدد',
                        studentsCount: parsedData.studentsCount || parsedData.students?.length || 0,
                        origin: 'madrasati_final'
                    };
                    console.log('📊 [Content] بيانات نهائية من التخزين:', finalData);
                }
            } catch (error) {
                console.warn('⚠️ [Content] استخدام البيانات الافتراضية');
            }

            iframe.contentWindow.postMessage(finalData, 'https://kushoofapp.com');
        }
    }, 4000);
}

// دالة إرسال البيانات للمصمم في النافذة
function sendDataToDesignerWindow(designerWindow, sid) {
    if (designerWindow && !designerWindow.closed) {
        console.log('📤 [Content] إرسال بيانات المدرسة للمصمم في النافذة');

        // جلب البيانات الحقيقية من الذاكرة المحلية
        let realSchoolData = {
            schoolId: sid,
            schoolName: 'مدرسة تجريبية',
            managerName: 'مدير المدرسة',
            ministryNumber: '12345',
            studentsCount: 150,
            origin: 'madrasati'
        };

        // محاولة جلب البيانات من الذاكرة المحلية
        try {
            const storedData = localStorage.getItem('kushoof_school_data');
            if (storedData) {
                const parsedData = JSON.parse(storedData);
                console.log('📊 [Content] بيانات محفوظة:', parsedData);

                realSchoolData = {
                    schoolId: sid,
                    schoolName: parsedData.schoolName || realSchoolData.schoolName,
                    managerName: parsedData.managerName || realSchoolData.managerName,
                    ministryNumber: parsedData.ministryNumber || realSchoolData.ministryNumber,
                    studentsCount: parsedData.studentsCount || parsedData.students?.length || realSchoolData.studentsCount,
                    origin: 'madrasati_stored'
                };
            }
        } catch (error) {
            console.warn('⚠️ [Content] فشل في قراءة البيانات المحفوظة:', error);
        }

        console.log('📤 [Content] إرسال البيانات الحقيقية للنافذة:', realSchoolData);

        designerWindow.postMessage(realSchoolData, 'https://kushoofapp.com');
    }
}

// دالة إرسال البيانات للمصمم
function sendDataToDesigner(sid) {
    const iframe = document.getElementById('designer-iframe');
    if (iframe && iframe.contentWindow) {
        console.log('📤 [Content] إرسال بيانات المدرسة للمصمم');

        // جلب البيانات الحقيقية من الذاكرة المحلية
        let realSchoolData = {
            schoolId: sid,
            schoolName: 'مدرسة تجريبية',
            managerName: 'مدير المدرسة',
            ministryNumber: '12345',
            studentsCount: 150,
            origin: 'madrasati'
        };

        // محاولة جلب البيانات من الذاكرة المحلية
        try {
            const storedData = localStorage.getItem('kushoof_school_data');
            if (storedData) {
                const parsedData = JSON.parse(storedData);
                console.log('📊 [Content] بيانات محفوظة:', parsedData);

                realSchoolData = {
                    schoolId: sid,
                    schoolName: parsedData.schoolName || realSchoolData.schoolName,
                    managerName: parsedData.managerName || realSchoolData.managerName,
                    ministryNumber: parsedData.ministryNumber || realSchoolData.ministryNumber,
                    studentsCount: parsedData.studentsCount || parsedData.students?.length || realSchoolData.studentsCount,
                    origin: 'madrasati_stored'
                };
            }
        } catch (error) {
            console.warn('⚠️ [Content] فشل في قراءة البيانات المحفوظة:', error);
        }

        console.log('📤 [Content] إرسال البيانات الحقيقية:', realSchoolData);

        iframe.contentWindow.postMessage(realSchoolData, 'https://kushoofapp.com');
    }
}





// ==== الدوال الرئيسية ====

async function extractData() {
    console.log('📊 [Extract] بدء الاستخراج...');

    try {
        // البحث عن معرف المدرسة
        const sid = getSchoolId();

        if (!sid) {
            alert('❌ لم يتم العثور على معرف المدرسة\n\nتأكد من أنك في صفحة مدرسة صحيحة في منصة مدرستي');
            return;
        }

        console.log('🏫 [Extract] معرف المدرسة:', sid);

        const confirmed = confirm(`🎯 استخراج بيانات الطلاب\n\nسيتم جمع جميع بيانات الطلاب من مدرستي.\nهذا قد يستغرق 2-5 دقائق.\n\nهل تريد المتابعة؟`);

        if (!confirmed) {
            return;
        }

        showLoadingMessage('🎯 جاري استخراج البيانات...<br>📊 جمع بيانات الطلاب من مدرستي<br>⏱️ قد يستغرق 2-5 دقائق');

        const students = await collectStudents(sid);

        if (!students || students.length === 0) {
            hideLoadingMessage();
            alert('❌ لم يتم العثور على بيانات طلاب');
            return;
        }

        console.log(`🎯 [Extract] تم جمع ${students.length} طالب`);

        // جلب بيانات المدرسة باستخدام الطريقة الأصلية الناجحة من content.txt
        showLoadingMessage('📋 جاري جلب معلومات المدرسة...');

        // استخدام الطريقة الأصلية البسيطة والناجحة
        let schoolName = 'اسم المدرسة غير محدد';
        let ministryNumber = null;
        let managerName = 'اسم المدير غير محدد';
        let academicYear = null;
        let semester = null;
        let studentsCountFromTable = null;
        let teachersCount = null;

        // 1. جلب اسم المدير من الصفحة الحالية (الطريقة الأصلية البسيطة)
        document.querySelectorAll("div.mx-2").forEach(div => {
            const txt = div.innerText.trim();
            if (txt.includes("مرحبا بك")) {
                const span = div.querySelector("span")?.innerText.trim();
                if (span) {
                    managerName = span;
                    console.log('👨‍💼 [Extract] تم العثور على اسم المدير:', managerName);
                }
            }
        });

        // 2. جلب اسم المدرسة من الصفحة الحالية (الطريقة الأصلية البسيطة)
        document.querySelectorAll("div.mx-2").forEach(div => {
            const txt = div.innerText.trim();
            if (/ابتدائية|متوسطة|ثانوية|روض/.test(txt)) {
                const name = txt.replace(/قائمة\s+المدارس?/, "").trim();
                if (name) {
                    schoolName = name;
                    console.log('🏫 [Extract] تم العثور على اسم المدرسة:', schoolName);
                }
            }
        });

        // 3. جلب العام الدراسي والفصل من صفحة الفصول (الطريقة الأصلية)
        try {
            console.log('📅 [Extract] جلب العام الدراسي والفصل...');
            const classroomsUrl = `https://schools.madrasati.sa/SchoolStructure/ClassRooms?SchoolId=${sid}`;
            const response = await fetch(classroomsUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            const info = doc.querySelector("div.row.d-flex.mx-2 small")?.innerText;
            const match = info?.match(/العام الدراسي\s*(\d+).*الفصل الدراسي\s*([^\s]+)/);
            if (match) {
                academicYear = match[1];
                semester = match[2];
                console.log('✅ [Extract] العام الدراسي:', academicYear, 'الفصل:', semester);
            }
        } catch (error) {
            console.log('⚠️ [Extract] خطأ في جلب العام الدراسي:', error.message);
        }

        // 4. جلب الرقم الوزاري من تشغيل البحث (الطريقة الصحيحة)
        try {
            console.log('📊 [Extract] تشغيل البحث للحصول على الرقم الوزاري...');

            // تشغيل البحث للحصول على النتائج مثل الضغط على زر البحث
            const searchUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/GetSchoolReport?SchoolId=${sid}`;
            const searchResponse = await fetch(searchUrl, {
                credentials: 'include',
                method: 'GET'
            });

            if (searchResponse.ok) {
                const searchHtml = await searchResponse.text();
                console.log('📄 [Extract] تم الحصول على نتائج البحث');

                // البحث عن الرقم الوزاري في نتائج البحث
                const doc = new DOMParser().parseFromString(searchHtml, 'text/html');

                // البحث في جدول النتائج
                const table = doc.querySelector('#DataTables_Table_0, table');
                if (table) {
                    const rows = table.querySelectorAll('tbody tr');
                    if (rows.length > 0) {
                        const firstRow = rows[0];
                        const cells = firstRow.querySelectorAll('td');

                        // البحث في الخلايا عن الرقم الوزاري
                        for (let i = 0; i < cells.length; i++) {
                            const cellText = cells[i].textContent.trim();
                            // إذا كان رقم من 4-6 خانات وليس سنة
                            if (/^\d{4,6}$/.test(cellText) &&
                                cellText !== '1446' && cellText !== '2025' && cellText !== '1445') {
                                ministryNumber = cellText;
                                console.log(`✅ [Extract] تم العثور على الرقم الوزاري في الخلية ${i + 1}:`, ministryNumber);
                                break;
                            }
                        }
                    }
                }

                // إذا لم نجد في الجدول، نبحث في النص
                if (!ministryNumber) {
                    const textMatch = searchHtml.match(/47377|\b\d{4,6}\b/g);
                    if (textMatch) {
                        for (const match of textMatch) {
                            if (match !== '1446' && match !== '2025' && match !== '1445') {
                                ministryNumber = match;
                                console.log('✅ [Extract] تم العثور على الرقم الوزاري في النص:', ministryNumber);
                                break;
                            }
                        }
                    }
                }
            } else {
                console.log('⚠️ [Extract] فشل في تشغيل البحث:', searchResponse.status);
            }

        } catch (error) {
            console.error('❌ [Extract] خطأ في جلب بيانات الجدول:', error);
        }

        console.log('🏫 [Extract] البيانات النهائية المجمعة:', {
            schoolName,
            ministryNumber,
            managerName,
            academicYear,
            semester,
            studentsCountFromTable,
            teachersCount
        });

        console.log('🏫 [Extract] البيانات النهائية للحفظ:', {
            schoolName,
            ministryNumber,
            studentsCountFromTable,
            teachersCount,
            managerName,
            academicYear,
            semester,
            extractedStudents: students.length
        });

        // التحقق من اكتمال البيانات
        const dataCompleteness = {
            schoolName: schoolName !== 'اسم المدرسة غير محدد' ? '✅' : '❌',
            ministryNumber: ministryNumber ? '✅' : '❌',
            managerName: managerName !== 'اسم المدير غير محدد' ? '✅' : '❌',
            teachersCount: teachersCount ? '✅' : '❌',
            studentsCount: studentsCountFromTable ? '✅' : '❌',
            academicYear: academicYear ? '✅' : '❌',
            semester: semester ? '✅' : '❌'
        };

        console.log('📊 [Extract] اكتمال البيانات:', dataCompleteness);

        const schoolData = {
            schoolId: sid,
            schoolName: schoolName,
            managerName: managerName,
            ministryNumber: ministryNumber,
            studentsCountFromTable: studentsCountFromTable,
            teachersCount: teachersCount,
            academicYear: academicYear,
            semester: semester,
            extractedAt: new Date().toISOString(),
            students: students,
            url: window.location.href,
            totalFound: students.length,
            extractionMethod: 'madrasati_multi_source_enhanced'
        };

        console.log('🏫 [Extract] معلومات المدرسة:', {
            name: schoolName,
            manager: managerName,
            ministry: ministryNumber,
            studentsFromTable: studentsCountFromTable,
            studentsExtracted: students.length,
            teachers: teachersCount
        });

        showLoadingMessage('💾 جاري حفظ البيانات في قاعدة البيانات...');
        const success = await saveDataToServer(schoolData);
        hideLoadingMessage();

        if (success) {
            const tableCount = schoolData.studentsCountFromTable;
            const extractedCount = students.length;
            const countMatch = tableCount === extractedCount;

            // عينة من البيانات المستخرجة
            const sampleStudent = students[0];
            const sampleData = sampleStudent ? `

📋 عينة من البيانات المستخرجة:
   • الاسم: ${sampleStudent.name}
   • الصف: ${sampleStudent.grade || 'غير متوفر'}
   • الفصل: ${sampleStudent.section || 'غير متوفر'}
   • هاتف ولي الأمر: ${sampleStudent.parentPhone || 'غير متوفر'}
   • هاتف الطالب: ${sampleStudent.studentPhone || 'غير متوفر'}
   • اسم المستخدم: ${sampleStudent.username || 'غير متوفر'}
   • السجل المدني: ${sampleStudent.nationalId || 'غير متوفر'}` : '';

            // تأكيد البيانات المحفوظة
            const savedDataConfirmation = `

💾 البيانات المحفوظة في قاعدة البيانات:
   ✅ معلومات المدرسة: محفوظة
   ✅ بيانات ${extractedCount} طالب: محفوظة
   ✅ العام الدراسي والفصل: محفوظة
   ✅ تاريخ الاستخراج: ${new Date().toLocaleString('ar-SA')}`;

            const successMessage = `✅ تم الاستخراج والحفظ بنجاح!

🏫 معلومات المدرسة:
   • الاسم: ${schoolData.schoolName}
   • الرقم الوزاري: ${schoolData.ministryNumber || 'غير متوفر'}
   • المدير: ${schoolData.managerName}
   • عدد المعلمين: ${schoolData.teachersCount || 'غير متوفر'}

📅 معلومات العام الدراسي:
   • العام الدراسي: ${schoolData.academicYear || 'غير متوفر'}
   • الفصل الدراسي: ${schoolData.semester || 'غير متوفر'}

📊 إحصائيات الطلاب:
   • العدد من الجدول: ${tableCount || 'غير متوفر'}
   • العدد المستخرج: ${extractedCount}
   • الحالة: ${countMatch ? '✅ متطابق' : '⚠️ غير متطابق'}${sampleData}${savedDataConfirmation}

🆔 معرف المدرسة: ${schoolData.schoolId}

🎯 يمكنك الآن الذهاب إلى مصمم الكشوف لعرض البيانات!`;

            alert(successMessage);

            console.log('🎉 [Extract] تم إكمال العملية بنجاح! جميع البيانات محفوظة في قاعدة البيانات.');

            // عرض خيار فتح مصمم الكشوف
            const shouldOpenDesigner = confirm('🎯 هل تريد فتح مصمم الكشوف الآن لعرض البيانات؟');
            if (shouldOpenDesigner) {
                openDesignerSmallWindow();
            }
        } else {
            alert('❌ تم الاستخراج بنجاح لكن فشل في حفظ البيانات\n\nتحقق من اتصال الإنترنت أو اتصل بالدعم الفني.');
            console.error('❌ [Extract] فشل في حفظ البيانات في قاعدة البيانات');
        }

    } catch (error) {
        hideLoadingMessage();
        console.error('❌ [Extract] خطأ:', error);
        alert('❌ خطأ في الاستخراج: ' + error.message);
    }
}



async function testConnection() {
    try {
        const response = await fetch('https://kushoofapp.com/js/api/receive-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'test', schoolId: 'test' })
        });

        const result = await response.json();

        if (result.success) {
            alert('✅ الاتصال يعمل بشكل صحيح!\n\n' + result.message + '\n\nEndpoint: ' + (result.endpoint || 'https://kushoofapp.com/js/api/receive-data.php') + '\n\n🎨 مصمم الكشوف: https://kushoofapp.com/js/designer.php');
        } else {
            alert('⚠️ مشكلة في الخادم:\n' + result.message);
        }

    } catch (error) {
        console.error('❌ [Test] خطأ:', error);
        alert('❌ فشل في الاتصال:\n' + error.message);
    }
}

async function testDatabase() {
    try {
        showLoadingMessage('🗄️ جاري اختبار قاعدة البيانات...');

        const response = await fetch('https://kushoofapp.com/js/api/test-database.php');
        const result = await response.json();

        hideLoadingMessage();

        if (result.success) {
            let message = '✅ اختبار قاعدة البيانات مكتمل!\n\n';

            if (result.results.connection) {
                message += '🔗 ' + result.results.connection + '\n\n';
            }

            if (result.results.tables) {
                message += '📋 الجداول الموجودة:\n';
                result.results.tables.forEach(table => {
                    message += '  • ' + table + '\n';
                });
                message += '\n';
            }

            if (result.results.schools_count !== undefined) {
                message += '🏫 عدد المدارس: ' + result.results.schools_count + '\n';
            }

            if (result.results.students_count !== undefined) {
                message += '👥 عدد الطلاب: ' + result.results.students_count + '\n';
            }

            if (result.results.schools_created) {
                message += '\n✨ ' + result.results.schools_created;
            }

            if (result.results.students_created) {
                message += '\n✨ ' + result.results.students_created;
            }

            if (result.results.logs_created) {
                message += '\n✨ ' + result.results.logs_created;
            }

            alert(message);
        } else {
            alert('❌ خطأ في اختبار قاعدة البيانات:\n' + result.message);
        }

    } catch (error) {
        hideLoadingMessage();
        console.error('❌ [Test DB] خطأ:', error);
        alert('❌ فشل في اختبار قاعدة البيانات:\n' + error.message);
    }
}

async function testSchoolData() {
    try {
        const sid = getSchoolId();

        if (!sid) {
            alert('❌ لم يتم العثور على معرف المدرسة في الرابط أو المحتوى');
            return;
        }

        showLoadingMessage('🔍 جاري اختبار جلب بيانات المدرسة...');

        console.log('🏫 [Test School] معرف المدرسة:', sid);

        // اختبار جلب البيانات باستخدام نفس الطريقة المستخدمة في الاستخراج
        console.log('🔍 [Test School] بدء اختبار الطريقة الأصلية البسيطة...');

        showLoadingMessage('📋 جاري اختبار جلب البيانات...');

        // نفس الكود المستخدم في الاستخراج
        let schoolName = 'اسم المدرسة غير محدد';
        let ministryNumber = null;
        let managerName = 'اسم المدير غير محدد';
        let academicYear = null;
        let semester = null;
        let studentsCountFromTable = null;
        let teachersCount = null;

        // 1. جلب اسم المدير من الصفحة الحالية
        document.querySelectorAll("div.mx-2").forEach(div => {
            const txt = div.innerText.trim();
            if (txt.includes("مرحبا بك")) {
                const span = div.querySelector("span")?.innerText.trim();
                if (span) {
                    managerName = span;
                    console.log('👨‍💼 [Test] تم العثور على اسم المدير:', managerName);
                }
            }
        });

        // 2. جلب اسم المدرسة من الصفحة الحالية
        document.querySelectorAll("div.mx-2").forEach(div => {
            const txt = div.innerText.trim();
            if (/ابتدائية|متوسطة|ثانوية|روض/.test(txt)) {
                const name = txt.replace(/قائمة\s+المدارس?/, "").trim();
                if (name) {
                    schoolName = name;
                    console.log('🏫 [Test] تم العثور على اسم المدرسة:', schoolName);
                }
            }
        });

        // 3. جلب العام الدراسي والفصل
        try {
            const classroomsUrl = `https://schools.madrasati.sa/SchoolStructure/ClassRooms?SchoolId=${sid}`;
            const response = await fetch(classroomsUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            const info = doc.querySelector("div.row.d-flex.mx-2 small")?.innerText;
            const match = info?.match(/العام الدراسي\s*(\d+).*الفصل الدراسي\s*([^\s]+)/);
            if (match) {
                academicYear = match[1];
                semester = match[2];
                console.log('✅ [Test] العام الدراسي:', academicYear, 'الفصل:', semester);
            }
        } catch (error) {
            console.log('⚠️ [Test] خطأ في جلب العام الدراسي:', error.message);
        }

        // 4. جلب بيانات الجدول
        try {
            const reportsUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/Index/${sid}`;
            const response = await fetch(reportsUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            const table = doc.querySelector('#DataTables_Table_0');
            if (table) {
                const firstRow = table.querySelector('tbody tr');
                if (firstRow) {
                    const cells = firstRow.querySelectorAll('td');
                    const visibleCells = Array.from(cells).filter(cell =>
                        cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                    );

                    if (visibleCells.length >= 5) {
                        if (schoolName === 'اسم المدرسة غير محدد') {
                            schoolName = visibleCells[0]?.textContent.trim() || 'اسم المدرسة غير محدد';
                        }
                        ministryNumber = visibleCells[1]?.textContent.trim() || null;
                        teachersCount = parseInt(visibleCells[3]?.textContent.trim()) || null;
                        studentsCountFromTable = parseInt(visibleCells[4]?.textContent.trim()) || null;

                        console.log('✅ [Test] تم جلب بيانات الجدول بنجاح');
                    }
                }
            }

            // إذا لم نحصل على الرقم الوزاري من الجدول، نبحث بطرق دقيقة
            if (!ministryNumber) {
                console.log('🔍 [Test] البحث عن الرقم الوزاري بطرق دقيقة...');

                // البحث الدقيق في النص
                const patterns = [
                    /الرقم\s*الوزاري[:\s]*(\d{4,6})/gi,
                    /رقم\s*وزاري[:\s]*(\d{4,6})/gi,
                    /<td[^>]*class="[^"]*text-nowrap[^"]*"[^>]*>\s*(\d{4,6})\s*<\/td>/gi,
                    /47377/g
                ];

                for (const pattern of patterns) {
                    const matches = html.match(pattern);
                    if (matches) {
                        for (const match of matches) {
                            const numberMatch = match.match(/(\d{4,6})/);
                            if (numberMatch) {
                                const number = numberMatch[1];
                                if (number !== '1446' && number !== '2025' && number !== '1445' &&
                                    number !== '999999' && number !== '000000' && number !== '123456') {
                                    ministryNumber = number;
                                    console.log('✅ [Test] تم العثور على الرقم الوزاري:', ministryNumber);
                                    break;
                                }
                            }
                        }
                        if (ministryNumber) break;
                    }
                }

                // البحث في جميع الأرقام كحل أخير
                if (!ministryNumber) {
                    const allNumbers = html.match(/\d{4,6}/g);
                    if (allNumbers) {
                        const validNumbers = allNumbers.filter(num =>
                            num !== '1446' && num !== '2025' && num !== '1445' &&
                            num !== '999999' && num !== '000000' && num !== '123456' &&
                            parseInt(num) > 1000 && parseInt(num) < 999999
                        );

                        if (validNumbers.length > 0) {
                            ministryNumber = validNumbers[0];
                            console.log('✅ [Test] تم العثور على رقم وزاري محتمل:', ministryNumber);
                        }
                    }
                }

                if (!ministryNumber) {
                    console.log('❌ [Test] لم يتم العثور على الرقم الوزاري بأي طريقة');
                } else {
                    console.log('🎉 [Test] تم العثور على الرقم الوزاري بنجاح:', ministryNumber);
                }
            }

        } catch (error) {
            console.log('⚠️ [Test] خطأ في جلب بيانات الجدول:', error.message);
        }

        hideLoadingMessage();

        const dataStatus = (schoolName !== 'اسم المدرسة غير محدد' || ministryNumber) ? '✅ تم العثور على البيانات' : '❌ لم يتم العثور على البيانات';

        const message = `🏫 نتائج اختبار بيانات المدرسة (الطريقة الأصلية البسيطة):

📋 معرف المدرسة: ${sid}
📊 حالة البيانات: ${dataStatus}

🏫 معلومات المدرسة:
   • الاسم: ${schoolName}
   • الرقم الوزاري: ${ministryNumber || 'غير متوفر'}
   • المدير: ${managerName}
   • عدد المعلمين: ${teachersCount || 'غير متوفر'}
   • عدد الطلاب: ${studentsCountFromTable || 'غير متوفر'}

📅 معلومات العام الدراسي:
   • العام الدراسي: ${academicYear || 'غير متوفر'}
   • الفصل الدراسي: ${semester || 'غير متوفر'}

✅ تم الاختبار بنجاح!`;

        alert(message);

    } catch (error) {
        hideLoadingMessage();
        console.error('❌ [Test School] خطأ:', error);
        alert('❌ خطأ في اختبار بيانات المدرسة:\n' + error.message);
    }
}

// دوال مساعدة
async function getSchoolDataFromMultipleSources(schoolId) {
    try {
        console.log('🔍 [Multi] بدء البحث عن بيانات المدرسة من مصادر متعددة...');

        const schoolData = {
            schoolName: null,
            ministryNumber: null,
            teachersCount: null,
            studentsCount: null,
            managerName: null,
            academicYear: null,
            semester: null
        };

        // 1. محاولة جلب البيانات من صفحة التقارير
        try {
            console.log('🔍 [Multi] جلب بيانات من صفحة التقارير...');
            const reportsUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/Index/${schoolId}`;
            const response = await fetch(reportsUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            // البحث عن الجدول
            const table = doc.querySelector('#DataTables_Table_0') || doc.querySelector('table');
            if (table) {
                const firstRow = table.querySelector('tbody tr');
                if (firstRow) {
                    const cells = firstRow.querySelectorAll('td');
                    console.log('📋 [Multi] عدد أعمدة الجدول:', cells.length);

                    // طباعة محتوى كل عمود للتشخيص
                    cells.forEach((cell, index) => {
                        const cellText = cell.textContent.trim();
                        const isVisible = cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden');
                        console.log(`📋 [Multi] العمود ${index + 1}: "${cellText}" (مرئي: ${isVisible})`);
                    });

                    // البحث عن البيانات في الأعمدة المرئية فقط
                    const visibleCells = Array.from(cells).filter(cell =>
                        cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                    );

                    console.log('📋 [Multi] عدد الأعمدة المرئية:', visibleCells.length);

                    if (visibleCells.length >= 5) {
                        schoolData.schoolName = visibleCells[0]?.textContent.trim() || null;
                        schoolData.ministryNumber = visibleCells[1]?.textContent.trim() || null;
                        // تخطي العمود الثالث (شارة المدرسة)
                        schoolData.teachersCount = visibleCells[3]?.textContent.trim() || null;
                        schoolData.studentsCount = visibleCells[4]?.textContent.trim() || null;

                        console.log('✅ [Multi] تم جلب بيانات من جدول التقارير:', {
                            schoolName: schoolData.schoolName,
                            ministryNumber: schoolData.ministryNumber,
                            teachersCount: schoolData.teachersCount,
                            studentsCount: schoolData.studentsCount
                        });
                    } else {
                        console.log('⚠️ [Multi] عدد الأعمدة المرئية غير كافي');
                    }
                } else {
                    console.log('⚠️ [Multi] لم يتم العثور على صفوف في الجدول');
                }
            } else {
                console.log('⚠️ [Multi] لم يتم العثور على الجدول');
            }

            // البحث عن اسم المدير في صفحة التقارير
            const bodyText = doc.body.textContent;
            const managerMatch = bodyText.match(/مرحبا\s*بك\s*([^،\n\r<]+)/);
            if (managerMatch) {
                schoolData.managerName = managerMatch[1].trim();
                console.log('✅ [Multi] تم العثور على اسم المدير من التقارير:', schoolData.managerName);
            }

        } catch (error) {
            console.log('⚠️ [Multi] خطأ في جلب بيانات التقارير:', error.message);
        }

        // 2. محاولة جلب العام الدراسي والفصل من صفحة الفصول
        try {
            console.log('🔍 [Multi] جلب بيانات العام الدراسي من صفحة الفصول...');
            const classroomsUrl = `https://schools.madrasati.sa/SchoolStructure/ClassRooms?schoolId=${schoolId}`;
            const response = await fetch(classroomsUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            // البحث عن العام الدراسي والفصل
            const academicInfo = doc.querySelector('small');
            if (academicInfo) {
                const academicText = academicInfo.textContent.trim();
                console.log('📅 [Multi] نص العام الدراسي:', academicText);

                // استخراج العام الدراسي والفصل
                const yearMatch = academicText.match(/العام\s*الدراسي\s*(\d+)/);
                const semesterMatch = academicText.match(/الفصل\s*الدراسي\s*([^\s]+)/);

                if (yearMatch) {
                    schoolData.academicYear = yearMatch[1];
                    console.log('✅ [Multi] العام الدراسي:', schoolData.academicYear);
                }

                if (semesterMatch) {
                    schoolData.semester = semesterMatch[1];
                    console.log('✅ [Multi] الفصل الدراسي:', schoolData.semester);
                }
            }

            // البحث عن اسم المدرسة في صفحة الفصول إذا لم نجده
            if (!schoolData.schoolName) {
                const titleElements = doc.querySelectorAll('h1, h2, h3, .title, .page-title');
                for (const element of titleElements) {
                    if (element.textContent.includes('مدرسة')) {
                        schoolData.schoolName = element.textContent.trim();
                        console.log('✅ [Multi] تم العثور على اسم المدرسة من الفصول:', schoolData.schoolName);
                        break;
                    }
                }
            }

        } catch (error) {
            console.log('⚠️ [Multi] خطأ في جلب بيانات الفصول:', error.message);
        }

        // 3. محاولة جلب البيانات من صفحة الإدارة
        try {
            console.log('🔍 [Multi] جلب البيانات من صفحة الإدارة...');
            const managementUrl = `https://schools.madrasati.sa/SchoolManagment/Actions/Index/${schoolId}?selectedRole=8EC0F7552D1F9681C66195908072AB6A`;
            const response = await fetch(managementUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            // جلب اسم المدرسة من صفحة الإدارة إذا لم نجده
            if (!schoolData.schoolName) {
                // البحث عن اسم المدرسة في span بجانب أيقونة المدرسة
                const schoolSpans = doc.querySelectorAll('span');
                for (const span of schoolSpans) {
                    const spanText = span.textContent.trim();
                    if (spanText.includes('متوسطة') || spanText.includes('ابتدائية') || spanText.includes('ثانوية')) {
                        // التأكد من أن هذا span بجانب أيقونة المدرسة
                        const parentDiv = span.closest('div');
                        if (parentDiv && parentDiv.innerHTML.includes('fa-school')) {
                            schoolData.schoolName = spanText;
                            console.log('✅ [Multi] تم العثور على اسم المدرسة من صفحة الإدارة:', schoolData.schoolName);
                            break;
                        }
                    }
                }
            }

            // جلب البيانات من الجدول في صفحة الإدارة
            const managementTable = doc.querySelector('#DataTables_Table_0');
            if (managementTable && (!schoolData.ministryNumber || !schoolData.teachersCount || !schoolData.studentsCount)) {
                const firstRow = managementTable.querySelector('tbody tr');
                if (firstRow) {
                    const cells = firstRow.querySelectorAll('td');
                    const visibleCells = Array.from(cells).filter(cell =>
                        cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                    );

                    console.log('📋 [Multi] جدول الإدارة - عدد الأعمدة المرئية:', visibleCells.length);

                    if (visibleCells.length >= 5) {
                        if (!schoolData.schoolName) {
                            schoolData.schoolName = visibleCells[0]?.textContent.trim() || null;
                        }
                        if (!schoolData.ministryNumber) {
                            schoolData.ministryNumber = visibleCells[1]?.textContent.trim() || null;
                        }
                        if (!schoolData.teachersCount) {
                            schoolData.teachersCount = visibleCells[3]?.textContent.trim() || null;
                        }
                        if (!schoolData.studentsCount) {
                            schoolData.studentsCount = visibleCells[4]?.textContent.trim() || null;
                        }

                        console.log('✅ [Multi] تم جلب بيانات إضافية من جدول الإدارة');
                    }
                }
            }

            // جلب اسم المدير
            if (!schoolData.managerName) {
                // البحث عن النمط: <strong>مرحبا بك </strong> <span> اسم المدير </span>
                const htmlMatch = html.match(/<strong[^>]*>مرحبا\s*بك\s*<\/strong>\s*<span[^>]*>\s*([^<]+)\s*<\/span>/);
                if (htmlMatch) {
                    schoolData.managerName = htmlMatch[1].trim();
                    console.log('✅ [Multi] تم العثور على اسم المدير من HTML:', schoolData.managerName);
                }

                // محاولة بديلة: البحث في النص العادي
                if (!schoolData.managerName) {
                    const bodyText = doc.body.textContent;
                    const textMatch = bodyText.match(/مرحبا\s*بك\s*([^،\n\r@]+)/);
                    if (textMatch) {
                        let managerName = textMatch[1].trim();
                        // إزالة البريد الإلكتروني إذا كان موجوداً
                        managerName = managerName.replace(/\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*$/, '');
                        if (managerName) {
                            schoolData.managerName = managerName;
                            console.log('✅ [Multi] تم العثور على اسم المدير من النص:', schoolData.managerName);
                        }
                    }
                }
            }

        } catch (error) {
            console.log('⚠️ [Multi] خطأ في جلب البيانات من صفحة الإدارة:', error.message);
        }

        // 4. محاولة جلب البيانات من الصفحة الحالية
        if (!schoolData.managerName || !schoolData.schoolName) {
            console.log('🔍 [Multi] جلب البيانات من الصفحة الحالية...');

            // جلب اسم المدير من الصفحة الحالية
            if (!schoolData.managerName) {
                const currentPageText = document.body.textContent;
                const managerMatch = currentPageText.match(/مرحبا\s*بك\s*([^،\n\r<@]+)/);
                if (managerMatch) {
                    let managerName = managerMatch[1].trim();
                    managerName = managerName.replace(/\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*$/, '');
                    if (managerName) {
                        schoolData.managerName = managerName;
                        console.log('✅ [Multi] تم العثور على اسم المدير من الصفحة الحالية:', schoolData.managerName);
                    }
                }
            }

            // جلب اسم المدرسة من الصفحة الحالية
            if (!schoolData.schoolName) {
                const currentSpans = document.querySelectorAll('span');
                for (const span of currentSpans) {
                    const spanText = span.textContent.trim();
                    if (spanText.includes('متوسطة') || spanText.includes('ابتدائية') || spanText.includes('ثانوية')) {
                        const parentDiv = span.closest('div');
                        if (parentDiv && parentDiv.innerHTML.includes('fa-school')) {
                            schoolData.schoolName = spanText;
                            console.log('✅ [Multi] تم العثور على اسم المدرسة من الصفحة الحالية:', schoolData.schoolName);
                            break;
                        }
                    }
                }
            }
        }

        // التحقق من صحة الأرقام
        if (schoolData.ministryNumber && !/^\d+$/.test(schoolData.ministryNumber)) {
            schoolData.ministryNumber = null;
        }
        if (schoolData.teachersCount && !/^\d+$/.test(schoolData.teachersCount)) {
            schoolData.teachersCount = null;
        }
        if (schoolData.studentsCount && !/^\d+$/.test(schoolData.studentsCount)) {
            schoolData.studentsCount = null;
        }

        // ملخص البيانات المجمعة
        const summary = {
            schoolName: schoolData.schoolName ? '✅' : '❌',
            ministryNumber: schoolData.ministryNumber ? '✅' : '❌',
            managerName: schoolData.managerName ? '✅' : '❌',
            teachersCount: schoolData.teachersCount ? '✅' : '❌',
            studentsCount: schoolData.studentsCount ? '✅' : '❌',
            academicYear: schoolData.academicYear ? '✅' : '❌',
            semester: schoolData.semester ? '✅' : '❌'
        };

        console.log('📊 [Multi] ملخص البيانات المجمعة:', summary);
        console.log('📊 [Multi] البيانات النهائية المجمعة:', schoolData);
        return schoolData;

    } catch (error) {
        console.error('❌ [Multi] خطأ عام في جلب البيانات:', error);
        return {
            schoolName: null,
            ministryNumber: null,
            teachersCount: null,
            studentsCount: null,
            managerName: null,
            academicYear: null,
            semester: null
        };
    }
}

async function getSchoolName(schoolId) {
    try {
        // جلب اسم المدرسة من صفحة التقارير
        const reportsUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/Index/${schoolId}`;
        const response = await fetch(reportsUrl, { credentials: 'include' });
        const html = await response.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');

        // البحث في الجدول المحدد
        const table = doc.querySelector('#DataTables_Table_0');
        if (table) {
            const firstRow = table.querySelector('tbody tr');
            if (firstRow) {
                const cells = firstRow.querySelectorAll('td');
                // تجاهل الأعمدة المخفية
                const visibleCells = Array.from(cells).filter(cell =>
                    cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                );

                if (visibleCells.length > 0) {
                    const schoolName = visibleCells[0]?.textContent.trim();
                    if (schoolName) {
                        console.log('🏫 [School] تم العثور على اسم المدرسة من الجدول:', schoolName);
                        return schoolName;
                    }
                }
            }
        }

        // البحث البديل في النص العام
        const bodyText = doc.body.textContent;
        const schoolMatch = bodyText.match(/مدرسة\s+([^،\n\r]+)/);
        if (schoolMatch) {
            const schoolName = 'مدرسة ' + schoolMatch[1].trim();
            console.log('🏫 [School] تم العثور على اسم المدرسة من النص:', schoolName);
            return schoolName;
        }

        console.log('⚠️ [School] لم يتم العثور على اسم المدرسة');
        return 'اسم المدرسة غير محدد';

    } catch (error) {
        console.error('❌ [School] خطأ في جلب اسم المدرسة:', error);
        return 'اسم المدرسة غير محدد';
    }
}

async function getMinistryNumber(schoolId) {
    try {
        // جلب الرقم الوزاري من صفحة التقارير
        const reportsUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/Index/${schoolId}`;
        const response = await fetch(reportsUrl, { credentials: 'include' });
        const html = await response.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');

        // البحث في الجدول المحدد أولاً
        const table = doc.querySelector('#DataTables_Table_0');
        if (table) {
            const firstRow = table.querySelector('tbody tr');
            if (firstRow) {
                const cells = firstRow.querySelectorAll('td');
                // تجاهل الأعمدة المخفية
                const visibleCells = Array.from(cells).filter(cell =>
                    cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                );

                // الرقم الوزاري في العمود الثاني من الأعمدة المرئية
                if (visibleCells.length >= 2) {
                    const ministryNumber = visibleCells[1]?.textContent.trim();
                    if (ministryNumber && /^\d+$/.test(ministryNumber)) {
                        console.log('🔢 [Ministry] تم العثور على الرقم الوزاري من الجدول:', ministryNumber);
                        return ministryNumber;
                    }
                }
            }
        }

        // البحث البديل في النص العام
        const bodyText = doc.body.textContent;

        // أنماط مختلفة للبحث عن الرقم الوزاري
        const patterns = [
            /الرقم\s*الوزاري[:\s]*(\d+)/,
            /رقم\s*وزاري[:\s]*(\d+)/,
            /Ministry\s*Number[:\s]*(\d+)/i,
            /رقم[:\s]*(\d{4,})/,  // رقم من 4 أرقام أو أكثر
            /(\d{4,})/  // أي رقم من 4 أرقام أو أكثر
        ];

        for (const pattern of patterns) {
            const match = bodyText.match(pattern);
            if (match) {
                const ministryNumber = match[1].trim();
                console.log('🔢 [Ministry] تم العثور على الرقم الوزاري من النص:', ministryNumber);
                return ministryNumber;
            }
        }

        console.log('⚠️ [Ministry] لم يتم العثور على الرقم الوزاري');
        return null;

    } catch (error) {
        console.error('❌ [Ministry] خطأ في جلب الرقم الوزاري:', error);
        return null;
    }
}

function getManagerName() {
    const text = document.body.textContent;

    // أنماط مختلفة للبحث عن اسم المدير
    const patterns = [
        /مرحبا\s*بك[:\s]+([^،\n\r]+)/,
        /المدير[:\s]+([^،\n\r]+)/,
        /مدير\s*المدرسة[:\s]+([^،\n\r]+)/,
        /أهلا\s*وسهلا[:\s]+([^،\n\r]+)/
    ];

    for (const pattern of patterns) {
        const match = text.match(pattern);
        if (match) {
            const managerName = match[1].trim();
            console.log('👨‍💼 [Manager] تم العثور على اسم المدير:', managerName);
            return managerName;
        }
    }

    console.log('⚠️ [Manager] لم يتم العثور على اسم المدير');
    return 'اسم المدير غير محدد';
}

async function getStudentsCountFromTable(schoolId) {
    try {
        // جلب عدد الطلاب من صفحة التقارير
        const reportsUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/Index/${schoolId}`;
        const response = await fetch(reportsUrl, { credentials: 'include' });
        const html = await response.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');

        // البحث في الجدول المحدد
        const table = doc.querySelector('#DataTables_Table_0');
        if (table) {
            const firstRow = table.querySelector('tbody tr');
            if (firstRow) {
                const cells = firstRow.querySelectorAll('td');
                // تجاهل الأعمدة المخفية
                const visibleCells = Array.from(cells).filter(cell =>
                    cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                );

                // عدد الطلاب في العمود الخامس من الأعمدة المرئية
                if (visibleCells.length >= 5) {
                    const studentsCount = visibleCells[4]?.textContent.trim();
                    if (studentsCount && /^\d+$/.test(studentsCount)) {
                        console.log('📊 [Students Count] تم العثور على عدد الطلاب من الجدول:', studentsCount);
                        return parseInt(studentsCount);
                    }
                }
            }
        }

        console.log('⚠️ [Students Count] لم يتم العثور على عدد الطلاب من الجدول');
        return null;

    } catch (error) {
        console.error('❌ [Students Count] خطأ في جلب عدد الطلاب:', error);
        return null;
    }
}

async function getTeachersCountFromTable(schoolId) {
    try {
        // جلب عدد المعلمين من صفحة التقارير
        const reportsUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/Index/${schoolId}`;
        const response = await fetch(reportsUrl, { credentials: 'include' });
        const html = await response.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');

        // البحث في الجدول المحدد
        const table = doc.querySelector('#DataTables_Table_0');
        if (table) {
            const firstRow = table.querySelector('tbody tr');
            if (firstRow) {
                const cells = firstRow.querySelectorAll('td');
                // تجاهل الأعمدة المخفية
                const visibleCells = Array.from(cells).filter(cell =>
                    cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                );

                // عدد المعلمين في العمود الرابع من الأعمدة المرئية
                if (visibleCells.length >= 4) {
                    const teachersCount = visibleCells[3]?.textContent.trim();
                    if (teachersCount && /^\d+$/.test(teachersCount)) {
                        console.log('👨‍🏫 [Teachers Count] تم العثور على عدد المعلمين من الجدول:', teachersCount);
                        return parseInt(teachersCount);
                    }
                }
            }
        }

        console.log('⚠️ [Teachers Count] لم يتم العثور على عدد المعلمين من الجدول');
        return null;

    } catch (error) {
        console.error('❌ [Teachers Count] خطأ في جلب عدد المعلمين:', error);
        return null;
    }
}

async function saveDataToServer(data) {
    try {
        console.log('💾 [Save] إرسال البيانات للخادم:', {
            schoolId: data.schoolId,
            schoolName: data.schoolName,
            managerName: data.managerName,
            studentsCount: data.students?.length || 0
        });

        // تحضير بيانات الطلاب مع جميع الحقول
        const studentsData = (data.students || []).map(student => ({
            name: student.name || '',
            national_id: student.nationalId || '',
            student_phone: student.studentPhone || '',
            parent_phone: student.parentPhone || '',
            username: student.username || '',
            class_label: student.classLabel || '',
            grade: student.grade || '',
            grade_level: student.grade || '',
            section: student.section || ''
        }));

        console.log('📊 [Save] عينة من بيانات الطلاب:', studentsData.slice(0, 2));

        const requestBody = {
            type: 'SCHOOL_DATA',
            action: 'force_save',
            schoolId: data.schoolId,
            schoolName: data.schoolName,
            managerName: data.managerName,
            ministryNumber: data.ministryNumber,
            academicYear: data.academicYear,
            semester: data.semester,
            studentsCount: studentsData.length,
            students: studentsData,
            force_update: true,
            source: 'madrasati_extension_complete',
            timestamp: new Date().toISOString(),
            debug_info: {
                total_students: studentsData.length,
                has_national_id: studentsData.filter(s => s.national_id).length,
                has_class_label: studentsData.filter(s => s.class_label).length,
                has_student_phone: studentsData.filter(s => s.student_phone).length
            }
        };

        console.log('📤 [Save] إرسال البيانات:', {
            schoolId: requestBody.schoolId,
            schoolName: requestBody.schoolName,
            studentsCount: requestBody.studentsCount,
            sampleStudent: studentsData[0]
        });

        const response = await fetch('https://kushoofapp.com/js/api/receive-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Force-Save': 'true'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ [Save] خطأ HTTP:', response.status, response.statusText, errorText);
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        console.log('💾 [Save] استجابة الخادم:', result);

        if (result.success) {
            // حفظ البيانات في localStorage أيضاً
            localStorage.setItem('kushoof_school_data', JSON.stringify({
                schoolId: data.schoolId,
                schoolName: data.schoolName,
                managerName: data.managerName,
                ministryNumber: data.ministryNumber,
                studentsCount: data.students?.length || 0,
                students: data.students || [],
                savedAt: new Date().toISOString()
            }));
            console.log('✅ [Save] تم حفظ البيانات في localStorage أيضاً');
        }

        return result.success;

    } catch (error) {
        console.error('❌ [Save] خطأ في الحفظ:', error);
        console.error('❌ [Save] تفاصيل الخطأ:', {
            message: error.message,
            stack: error.stack,
            schoolId: data.schoolId,
            studentsCount: studentsData.length
        });
        return false;
    }
}

function showLoadingMessage(message) {
    hideLoadingMessage();
    const loading = document.createElement('div');
    loading.id = 'kushoof-loading';
    loading.innerHTML = `
        <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 30px 40px;
            border-radius: 15px;
            z-index: 999999;
            text-align: center;
            font-family: Arial, sans-serif;
            font-size: 18px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        ">
            <div style="margin-bottom: 15px;">${message}</div>
            <div style="font-size: 14px; color: #ccc;">يرجى الانتظار...</div>
        </div>
    `;
    document.body.appendChild(loading);
}

function hideLoadingMessage() {
    const loading = document.getElementById('kushoof-loading');
    if (loading) {
        loading.remove();
    }
}

// إضافة اختصار لوحة المفاتيح
document.addEventListener('keydown', function(event) {
    if (event.ctrlKey && event.shiftKey && event.key === 'K') {
        event.preventDefault();
        console.log('⌨️ [Keyboard] تم استخدام اختصار لوحة المفاتيح');
        showKushoofMenu();
    }
});

console.log('✅ [Kushoof] تم تحميل جميع الدوال بنجاح');
console.log('🎯 [Kushoof] طرق الوصول:');
console.log('   1. الزر العائم في أعلى يمين الصفحة');
console.log('   2. اختصار لوحة المفاتيح: Ctrl + Shift + K');
