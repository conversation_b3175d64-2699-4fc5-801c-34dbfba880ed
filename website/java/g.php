<?php
/**
 * API بيانات WhatsApp - kushoofapp.com/java/g.php
 * يرسل بيانات JSON للاستخدام في WhatsApp Web
 */

// إعداد headers للاستجابة
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// استقبال المعاملات
$session_id = isset($_GET['s']) ? $_GET['s'] : '';

// التحقق من صحة المعاملات
if (empty($session_id)) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف الجلسة مفقود']);
    exit;
}

// هنا يتم جلب البيانات من قاعدة البيانات بناءً على معرف الجلسة
// مثال على البيانات (يجب استبدالها بقاعدة البيانات الحقيقية)

$session_data = [
    'school_id' => '123456',
    'teacher_name' => 'أ. محمد أحمد السالم',
    'subject' => 'الرياضيات',
    'class' => '3أ',
    'students' => [
        ['name' => 'أحمد محمد العلي', 'phone' => '966501234567'],
        ['name' => 'فاطمة سالم الأحمد', 'phone' => '966507654321'],
        ['name' => 'محمد عبدالله الحسن', 'phone' => '966509876543'],
        ['name' => 'نورا علي السالم', 'phone' => '966512345678']
    ],
    'message_template' => 'عزيزي ولي أمر الطالب ***، نود إعلامكم بأن الطالب ### غائب اليوم. نرجو التواصل معنا.'
];

// إعداد البيانات للإرسال
$students_names = [];
$students_phones = [];

foreach ($session_data['students'] as $student) {
    $students_names[] = $student['name'];
    $students_phones[] = $student['phone'];
}

// تاريخ اليوم
$arabic_days = [
    'Sunday' => 'الأحد',
    'Monday' => 'الاثنين',
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة',
    'Saturday' => 'السبت'
];
$day_name = $arabic_days[date('l')] ?? 'اليوم';
$formatted_date = $day_name . ' ' . date('d/m/Y');

// إعداد الاستجابة
$response_data = [
    'n' => $students_names,
    'p' => $students_phones,
    'day' => $formatted_date,
    'school_id' => $session_data['school_id'],
    'teacher' => $session_data['teacher_name'],
    'subject' => $session_data['subject'],
    'class' => $session_data['class'],
    'sm' => $session_data['message_template'],
    'session_id' => $session_id,
    'timestamp' => date('Y-m-d H:i:s')
];

// تسجيل الطلب
$log_entry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'session_id' => $session_id,
    'school_id' => $session_data['school_id'],
    'students_count' => count($students_names),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
];

file_put_contents(__DIR__ . '/../logs/whatsapp_requests.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

// إرسال الاستجابة
echo 'kushoof' . json_encode($response_data, JSON_UNESCAPED_UNICODE);

?>
