<?php
/**
 * API نظام نور - kushoofapp.com/java/noor.php
 * يرسل كود JavaScript للتكامل مع نظام نور التعليمي
 */

// إعداد headers للاستجابة
header('Content-Type: application/javascript; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// استقبال المعاملات
$key = isset($_GET['k']) ? $_GET['k'] : '';

// التحقق من صحة المفتاح
if ($key !== 'vir3') {
    http_response_code(403);
    echo "// خطأ: مفتاح غير صحيح";
    exit;
}

// هنا يتم جلب البيانات من قاعدة البيانات الخاصة بنظام نور
// مثال على البيانات (يجب استبدالها بقاعدة البيانات الحقيقية)

$noor_data = [
    'schools' => [
        [
            'id' => 'noor_001',
            'name' => 'مدرسة الملك عبدالعزيز الابتدائية',
            'region' => 'الرياض',
            'education_office' => 'تعليم الرياض'
        ]
    ],
    'students' => [
        ['name' => 'سعد محمد الدوسري', 'id' => '1234567890', 'grade' => 'الصف الثالث', 'phone' => '966501234567'],
        ['name' => 'لجين عبدالله القحطاني', 'id' => '1234567891', 'grade' => 'الصف الثالث', 'phone' => '966507654321'],
        ['name' => 'فيصل أحمد العتيبي', 'id' => '1234567892', 'grade' => 'الصف الرابع', 'phone' => '966509876543']
    ],
    'teachers' => [
        ['name' => 'أ. محمد سعد الغامدي', 'subject' => 'الرياضيات', 'phone' => '966550123456'],
        ['name' => 'أ. نورا علي الزهراني', 'subject' => 'اللغة العربية', 'phone' => '966551234567']
    ]
];

// تاريخ اليوم
$arabic_days = [
    'Sunday' => 'الأحد',
    'Monday' => 'الاثنين', 
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة',
    'Saturday' => 'السبت'
];
$day_name = $arabic_days[date('l')] ?? 'اليوم';
$formatted_date = $day_name . ' ' . date('d/m/Y');

// إنشاء كود JavaScript لنظام نور
$js_code = "
// كود نظام نور - Kushoof Integration
console.log('🎓 تم تحميل تكامل نظام نور مع Kushoof');

// بيانات نظام نور
var noor_kushoof_data = {
    'system': 'noor',
    'schools': " . json_encode($noor_data['schools'], JSON_UNESCAPED_UNICODE) . ",
    'students': " . json_encode($noor_data['students'], JSON_UNESCAPED_UNICODE) . ",
    'teachers': " . json_encode($noor_data['teachers'], JSON_UNESCAPED_UNICODE) . ",
    'date': '{$formatted_date}',
    'timestamp': '" . date('Y-m-d H:i:s') . "'
};

// دالة البحث عن عناصر نظام نور
function findNoorElements() {
    var noorElements = [
        document.querySelector('.header-noor'),
        document.querySelector('#noor-main'),
        document.querySelector('.noor-content'),
        document.getElementById('MainContent')
    ];
    
    var foundElement = null;
    for (var i = 0; i < noorElements.length; i++) {
        if (noorElements[i]) {
            foundElement = noorElements[i];
            break;
        }
    }
    
    if (foundElement) {
        console.log('✅ تم العثور على عناصر نظام نور');
        injectNoorKushoofInterface(foundElement);
    } else {
        console.log('⏳ البحث عن عناصر نظام نور...');
        setTimeout(findNoorElements, 3000);
    }
}

// دالة حقن واجهة Kushoof في نظام نور
function injectNoorKushoofInterface(targetElement) {
    // حقن CSS خاص بنظام نور
    var noorStyle = document.createElement('style');
    noorStyle.textContent = \`
        .noor-kushoof-panel {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }
        .noor-kushoof-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .noor-action-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .noor-action-btn:hover {
            background: rgba(255,255,255,0.3);
        }
    \`;
    document.head.appendChild(noorStyle);
    
    // إنشاء واجهة نظام نور
    var noorInterface = document.createElement('div');
    noorInterface.className = 'noor-kushoof-panel';
    noorInterface.innerHTML = \`
        <div class='noor-kushoof-title'>
            🎓 نظام نور - تكامل Kushoof
        </div>
        <p>📅 {$formatted_date}</p>
        <p>👥 الطلاب: \` + noor_kushoof_data.students.length + \` | 👨‍🏫 المعلمين: \` + noor_kushoof_data.teachers.length + \`</p>
        
        <div>
            <button class='noor-action-btn' onclick='exportNoorData()'>📊 تصدير البيانات</button>
            <button class='noor-action-btn' onclick='openNoorWhatsApp()'>📱 فتح WhatsApp</button>
            <button class='noor-action-btn' onclick='hideNoorPanel()'>❌ إخفاء</button>
        </div>
    \`;
    
    // إدراج الواجهة
    targetElement.insertBefore(noorInterface, targetElement.firstChild);
    
    console.log('✅ تم حقن واجهة Kushoof في نظام نور');
}

// دوال التفاعل مع نظام نور
function exportNoorData() {
    console.log('📊 تصدير بيانات نظام نور');
    
    var dataStr = JSON.stringify(noor_kushoof_data, null, 2);
    var dataBlob = new Blob([dataStr], {type: 'application/json'});
    var url = URL.createObjectURL(dataBlob);
    
    var a = document.createElement('a');
    a.href = url;
    a.download = 'noor_kushoof_data_' + new Date().getTime() + '.json';
    a.click();
    
    URL.revokeObjectURL(url);
}

function openNoorWhatsApp() {
    console.log('📱 فتح WhatsApp مع بيانات نظام نور');
    
    var whatsappData = {
        'n': noor_kushoof_data.students.map(function(s) { return s.name; }),
        'p': noor_kushoof_data.students.map(function(s) { return s.phone; }),
        'day': noor_kushoof_data.date,
        'source': 'noor_system'
    };
    
    var whatsappWindow = window.open('https://web.whatsapp.com', '_blank');
    
    setTimeout(function() {
        if (whatsappWindow && !whatsappWindow.closed) {
            whatsappWindow.name = 'kushoof' + JSON.stringify(whatsappData);
        }
    }, 1000);
}

function hideNoorPanel() {
    var panel = document.querySelector('.noor-kushoof-panel');
    if (panel) {
        panel.style.display = 'none';
        console.log('❌ تم إخفاء لوحة نظام نور');
    }
}

// بدء عملية البحث والحقن
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', findNoorElements);
} else {
    findNoorElements();
}

console.log('🎯 تم تحميل تكامل نظام نور مع Kushoof');
";

// إرسال كود JavaScript
echo $js_code;

// تسجيل الطلب
$log_entry = date('Y-m-d H:i:s') . " - Noor integration loaded, Key: {$key}\n";
file_put_contents(__DIR__ . '/../logs/noor_requests.log', $log_entry, FILE_APPEND | LOCK_EX);

?>
