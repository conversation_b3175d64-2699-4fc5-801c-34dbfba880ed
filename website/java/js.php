<?php
/**
 * API بيانات المدارس - kushoofapp.com/java/js.php
 * يرسل كود JavaScript مع بيانات الطلاب للحقن في موقع مدرستي
 */

// إعداد headers للاستجابة
header('Content-Type: application/javascript; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// استقبال المعاملات
$version = isset($_GET['version']) ? $_GET['version'] : '258';
$school_id = isset($_GET['id']) ? $_GET['id'] : '';
$key = isset($_GET['k']) ? $_GET['k'] : '';

// التحقق من صحة المعاملات
if (empty($school_id) || empty($key)) {
    http_response_code(400);
    echo "// خطأ: معاملات مفقودة";
    exit;
}

// هنا يتم جلب البيانات من قاعدة البيانات الحقيقية
// بناءً على معرف المدرسة

// مثال على البيانات (يجب استبدالها بقاعدة البيانات الحقيقية)
$students_data = [
    'names' => ['أحمد محمد', 'فاطمة علي', 'محمد سالم'],
    'phones' => ['966501234567', '966507654321', '966509876543'],
    'classes' => ['3أ', '3أ', '3ب']
];

// تاريخ اليوم
$arabic_days = [
    'Sunday' => 'الأحد',
    'Monday' => 'الاثنين',
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة',
    'Saturday' => 'السبت'
];
$day_name = $arabic_days[date('l')] ?? 'اليوم';
$formatted_date = $day_name . ' ' . date('d/m/Y');

// إنشاء كود JavaScript للحقن
$js_code = "
// كود محقون من kushoofapp.com/java/js.php
console.log('🚀 تم تحميل بيانات المدرسة: {$school_id}');

// بيانات الطلاب
var kushoof_data = {
    'n': ['" . implode("', '", array_map('urlencode', $students_data['names'])) . "'],
    'p': ['" . implode("', '", $students_data['phones']) . "'],
    'c': ['" . implode("', '", array_map('urlencode', $students_data['classes'])) . "'],
    'day': '{$formatted_date}',
    'school_id': '{$school_id}',
    'version': '{$version}',
    'timestamp': '" . date('Y-m-d H:i:s') . "'
};

// تخزين البيانات في window.name للاستخدام في WhatsApp
if (typeof window !== 'undefined') {
    window.name = 'kushoof' + JSON.stringify(kushoof_data);
    console.log('📊 تم تخزين بيانات ' + kushoof_data.n.length + ' طالب');
}

// دالة البحث عن header وحقن الواجهة
function findHeaderAndInject() {
    var headers = document.getElementsByTagName('header');
    
    if (headers.length > 0) {
        console.log('✅ تم العثور على header - جاري حقن واجهة Kushoof');
        injectKushoofInterface();
    } else {
        console.log('⏳ البحث عن header...');
        setTimeout(findHeaderAndInject, 2000);
    }
}

// دالة حقن واجهة Kushoof
function injectKushoofInterface() {
    // حقن CSS
    var style = document.createElement('style');
    style.textContent = \`
        .kushoof-interface {
            background: linear-gradient(135deg, #128c7e 0%, #075e54 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .kushoof-btn {
            background: #4682b4;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .kushoof-btn:hover {
            background: #5a9bd4;
        }
        .kushoof-student-list {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .kushoof-student {
            padding: 5px;
            margin: 2px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
            font-size: 12px;
        }
        .kushoof-invalid {
            color: #ffcccb;
            text-decoration: line-through;
        }
    \`;
    document.head.appendChild(style);
    
    // إنشاء HTML الواجهة
    var interfaceHTML = \`
        <div class='kushoof-interface'>
            <h3>🚀 Kushoof WhatsApp</h3>
            <p>📅 {$formatted_date} | 👥 عدد الطلاب: \` + kushoof_data.n.length + \`</p>
            
            <div class='kushoof-student-list'>
    \`;
    
    for (var i = 0; i < kushoof_data.n.length; i++) {
        var studentClass = kushoof_data.p[i].length < 12 ? 'kushoof-student kushoof-invalid' : 'kushoof-student';
        var statusIcon = kushoof_data.p[i].length < 12 ? '❌' : '✅';
        
        interfaceHTML += \`
            <div class='\` + studentClass + \`'>
                \` + statusIcon + \` \` + (i + 1) + \`. \` + decodeURIComponent(kushoof_data.n[i]) + \`
                <br><small>📱 \` + kushoof_data.p[i] + \` | 🏫 \` + decodeURIComponent(kushoof_data.c[i]) + \`</small>
            </div>
        \`;
    }
    
    interfaceHTML += \`
            </div>
            
            <div>
                <button class='kushoof-btn' onclick='openWhatsAppWithData()'>
                    📱 فتح WhatsApp
                </button>
                <button class='kushoof-btn' onclick='downloadStudentsData()'>
                    📥 تحميل البيانات
                </button>
                <button class='kushoof-btn' onclick='hideKushoofInterface()'>
                    ❌ إخفاء
                </button>
            </div>
        </div>
    \`;
    
    // إضافة الواجهة إلى الصفحة
    var interfaceDiv = document.createElement('div');
    interfaceDiv.id = 'kushoof-interface';
    interfaceDiv.innerHTML = interfaceHTML;
    
    // إدراج الواجهة في أعلى الصفحة
    document.body.insertBefore(interfaceDiv, document.body.firstChild);
    
    console.log('✅ تم حقن واجهة Kushoof بنجاح');
}

// دالة فتح WhatsApp مع البيانات
function openWhatsAppWithData() {
    console.log('📱 فتح WhatsApp مع البيانات...');
    var whatsappWindow = window.open('https://web.whatsapp.com', '_blank');
    
    // تمرير البيانات للنافذة الجديدة
    setTimeout(function() {
        if (whatsappWindow && !whatsappWindow.closed) {
            whatsappWindow.name = window.name;
        }
    }, 1000);
}

// دالة تحميل البيانات
function downloadStudentsData() {
    var dataStr = JSON.stringify(kushoof_data, null, 2);
    var dataBlob = new Blob([dataStr], {type: 'application/json'});
    var url = URL.createObjectURL(dataBlob);
    
    var a = document.createElement('a');
    a.href = url;
    a.download = 'kushoof_students_' + kushoof_data.school_id + '_' + new Date().getTime() + '.json';
    a.click();
    
    URL.revokeObjectURL(url);
    console.log('📥 تم تحميل بيانات الطلاب');
}

// دالة إخفاء الواجهة
function hideKushoofInterface() {
    var interface = document.getElementById('kushoof-interface');
    if (interface) {
        interface.style.display = 'none';
        console.log('❌ تم إخفاء واجهة Kushoof');
    }
}

// بدء عملية البحث والحقن
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', findHeaderAndInject);
} else {
    findHeaderAndInject();
}

console.log('🎯 تم تحميل سكريپت Kushoof - الإصدار {$version}');
";

// إرسال كود JavaScript
echo $js_code;

// تسجيل الطلب في ملف log
$log_entry = date('Y-m-d H:i:s') . " - School ID: {$school_id}, Version: {$version}\n";
file_put_contents(__DIR__ . '/../logs/requests.log', $log_entry, FILE_APPEND | LOCK_EX);

?>
