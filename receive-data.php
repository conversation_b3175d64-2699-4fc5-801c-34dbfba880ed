<?php
/**
 * API لحفظ بيانات المدارس والطلاب
 * يحفظ البيانات في قاعدة البيانات وملفات مشفرة
 * الرابط: https://kushoofapp.com/js/api/receive-data.php
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Force-Save, Authorization, X-Requested-With');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kushoofa_db';
$username = 'kushoofa_db';
$password = 'Ali123456@@';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // تسجيل للتشخيص
    error_log("API Request: " . $input);
    error_log("Parsed Data: " . print_r($data, true));

    if (!$data) {
        throw new Exception('بيانات غير صالحة - JSON parsing failed');
    }

    // دعم كل من action و type للتوافق مع الإضافة الجديدة
    $action = $data['action'] ?? '';
    $type = $data['type'] ?? '';

    // إذا كان النوع SCHOOL_DATA، فهذا يعني حفظ البيانات
    if ($type === 'SCHOOL_DATA' || $action === 'force_save') {
        error_log("Force save requested for school: " . ($data['schoolId'] ?? 'unknown'));
        $result = saveSchoolDataFromExtension($pdo, $data);
    } else {
        switch ($action) {
            case 'save':
                $result = saveSchoolData($pdo, $data);
                break;

            case 'load':
                $result = loadSchoolData($pdo, $data);
                break;

            case 'list':
                $result = listSchools($pdo);
                break;

            case 'test':
                $result = testConnection();
                break;

            default:
                throw new Exception('إجراء غير مدعوم: ' . ($action ?: $type ?: 'غير محدد'));
        }
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // تسجيل الخطأ للتشخيص
    error_log("API Error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'error_details' => [
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'type' => get_class($e)
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * حفظ بيانات المدرسة من الإضافة الجديدة
 */
function saveSchoolDataFromExtension($pdo, $data) {
    $schoolId = $data['schoolId'] ?? '';
    $schoolName = $data['schoolName'] ?? 'غير محدد';
    $managerName = $data['managerName'] ?? 'غير محدد';
    $ministryNumber = $data['ministryNumber'] ?? '';
    $students = $data['students'] ?? [];
    $studentsCount = $data['studentsCount'] ?? count($students);
    $forceUpdate = $data['force_update'] ?? false;

    error_log("Saving school data: ID=$schoolId, Name=$schoolName, Manager=$managerName, Students=" . count($students));

    if (!$schoolId) {
        throw new Exception('معرف المدرسة مطلوب');
    }

    // بدء المعاملة
    $pdo->beginTransaction();

    try {
        // حفظ معلومات المدرسة الأساسية
        $stmt = $pdo->prepare("
            INSERT INTO schools (school_id, school_name, manager_name, ministry_number, extracted_at, total_students, extraction_method, url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                school_name = VALUES(school_name),
                manager_name = VALUES(manager_name),
                ministry_number = VALUES(ministry_number),
                extracted_at = VALUES(extracted_at),
                total_students = VALUES(total_students),
                extraction_method = VALUES(extraction_method),
                updated_at = CURRENT_TIMESTAMP
        ");

        $stmt->execute([
            $schoolId,
            $schoolName,
            $managerName,
            $ministryNumber,
            date('Y-m-d H:i:s'),
            $studentsCount,
            'extension_obfuscated',
            'https://schools.madrasati.sa'
        ]);

        // حذف الطلاب القدامى
        $stmt = $pdo->prepare("DELETE FROM students WHERE school_id = ?");
        $stmt->execute([$schoolId]);

        // حفظ بيانات الطلاب
        if (!empty($students)) {
            $stmt = $pdo->prepare("
                INSERT INTO students (school_id, student_name, parent_phone, student_phone, username, national_id, class_label, grade, section, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ");

            foreach ($students as $student) {
                $stmt->execute([
                    $schoolId,
                    $student['name'] ?? '',
                    $student['parent_phone'] ?? '',
                    $student['student_phone'] ?? '',
                    $student['username'] ?? '',
                    $student['national_id'] ?? '',
                    $student['class_label'] ?? '',
                    $student['grade'] ?? '',
                    $student['section'] ?? ''
                ]);
            }
        }

        // حفظ البيانات في ملف مشفر
        saveEncryptedFile($schoolId, $data);

        // تسجيل العملية
        logOperation($pdo, $schoolId, 'save_extension', 'تم حفظ بيانات من الإضافة المشوشة - ' . count($students) . ' طالب', count($students));

        // تأكيد المعاملة
        $pdo->commit();

        return [
            'success' => true,
            'message' => 'تم حفظ البيانات بنجاح من الإضافة',
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => [
                'schoolId' => $schoolId,
                'schoolName' => $schoolName,
                'managerName' => $managerName,
                'studentsCount' => count($students),
                'source' => 'extension_obfuscated'
            ]
        ];

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * حفظ بيانات المدرسة (الدالة الأصلية)
 */
function saveSchoolData($pdo, $requestData) {
    $schoolData = $requestData['data'] ?? [];
    $schoolId = $requestData['schoolId'] ?? '';
    
    if (!$schoolId || !$schoolData) {
        throw new Exception('بيانات المدرسة مطلوبة');
    }
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    try {
        // حفظ معلومات المدرسة الأساسية
        $stmt = $pdo->prepare("
            INSERT INTO schools (school_id, school_name, manager_name, ministry_number, extracted_at, total_students, students_count_from_table, teachers_count, academic_year, semester, extraction_method, url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                school_name = VALUES(school_name),
                manager_name = VALUES(manager_name),
                ministry_number = VALUES(ministry_number),
                extracted_at = VALUES(extracted_at),
                total_students = VALUES(total_students),
                students_count_from_table = VALUES(students_count_from_table),
                teachers_count = VALUES(teachers_count),
                academic_year = VALUES(academic_year),
                semester = VALUES(semester),
                extraction_method = VALUES(extraction_method),
                url = VALUES(url),
                updated_at = CURRENT_TIMESTAMP
        ");

        $stmt->execute([
            $schoolId,
            $schoolData['schoolName'] ?? 'غير محدد',
            $schoolData['managerName'] ?? 'غير محدد',
            $schoolData['ministryNumber'] ?? extractMinistryNumber($schoolData['schoolName'] ?? ''),
            $schoolData['extractedAt'] ?? date('Y-m-d H:i:s'),
            count($schoolData['students'] ?? []),
            $schoolData['studentsCountFromTable'] ?? null,
            $schoolData['teachersCount'] ?? null,
            $schoolData['academicYear'] ?? null,
            $schoolData['semester'] ?? null,
            $schoolData['extractionMethod'] ?? 'unknown',
            $schoolData['url'] ?? ''
        ]);
        
        // حذف الطلاب القدامى
        $stmt = $pdo->prepare("DELETE FROM students WHERE school_id = ?");
        $stmt->execute([$schoolId]);
        
        // حفظ بيانات الطلاب
        if (!empty($schoolData['students'])) {
            $stmt = $pdo->prepare("
                INSERT INTO students (school_id, student_name, parent_phone, student_phone, username, national_id, class_label, grade, section, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ");

            foreach ($schoolData['students'] as $student) {
                $stmt->execute([
                    $schoolId,
                    $student['name'] ?? '',
                    $student['parentPhone'] ?? '',
                    $student['studentPhone'] ?? '',
                    $student['username'] ?? '',
                    $student['nationalId'] ?? '',
                    $student['classLabel'] ?? '',
                    $student['grade'] ?? '',
                    $student['section'] ?? ''
                ]);
            }
        }
        
        // حفظ البيانات في ملف مشفر
        saveEncryptedFile($schoolId, $schoolData);
        
        // تسجيل العملية
        logOperation($pdo, $schoolId, 'save', 'تم حفظ بيانات ' . count($schoolData['students'] ?? []) . ' طالب', count($schoolData['students'] ?? []));
        
        // تأكيد المعاملة
        $pdo->commit();
        
        return [
            'success' => true,
            'message' => 'تم حفظ البيانات بنجاح',
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => [
                'schoolId' => $schoolId,
                'studentsCount' => count($schoolData['students'] ?? []),
                'schoolName' => $schoolData['schoolName'] ?? 'غير محدد'
            ]
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * تحميل بيانات المدرسة
 */
function loadSchoolData($pdo, $requestData) {
    $schoolId = $requestData['schoolId'] ?? '';
    
    if (!$schoolId) {
        throw new Exception('معرف المدرسة مطلوب');
    }
    
    // جلب معلومات المدرسة
    $stmt = $pdo->prepare("SELECT * FROM schools WHERE school_id = ?");
    $stmt->execute([$schoolId]);
    $school = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$school) {
        return [
            'success' => false,
            'message' => 'لم يتم العثور على بيانات المدرسة',
            'data' => null
        ];
    }
    
    // جلب بيانات الطلاب
    $stmt = $pdo->prepare("SELECT * FROM students WHERE school_id = ? ORDER BY student_name");
    $stmt->execute([$schoolId]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تسجيل العملية
    logOperation($pdo, $schoolId, 'load', 'تم تحميل بيانات المدرسة', count($students));
    
    return [
        'success' => true,
        'message' => 'تم تحميل البيانات بنجاح',
        'data' => [
            'school' => $school,
            'students' => $students,
            'studentsCount' => count($students)
        ]
    ];
}

/**
 * قائمة جميع المدارس
 */
function listSchools($pdo) {
    $stmt = $pdo->query("
        SELECT school_id, school_name, manager_name, ministry_number, total_students, extracted_at 
        FROM schools 
        ORDER BY extracted_at DESC
    ");
    $schools = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'success' => true,
        'message' => 'تم جلب قائمة المدارس بنجاح',
        'data' => $schools,
        'count' => count($schools)
    ];
}

/**
 * اختبار الاتصال
 */
function testConnection() {
    return [
        'success' => true,
        'message' => 'الاتصال يعمل بشكل صحيح',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => $_SERVER['SERVER_NAME'] ?? 'unknown',
        'endpoint' => 'https://kushoofapp.com/js/api/receive-data.php'
    ];
}

/**
 * استخراج الرقم الوزاري من اسم المدرسة (كنسخة احتياطية)
 */
function extractMinistryNumber($schoolName) {
    // البحث عن رقم من 3 أرقام أو أكثر في اسم المدرسة
    if (preg_match('/(\d{3,})/', $schoolName, $matches)) {
        return $matches[1];
    }

    // البحث عن أي رقم كنسخة احتياطية
    if (preg_match('/(\d+)/', $schoolName, $matches)) {
        return $matches[1];
    }

    return null;
}

/**
 * حفظ البيانات في ملف مشفر
 */
function saveEncryptedFile($schoolId, $data) {
    $dataDir = __DIR__ . '/../secure/schools/';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
    }
    
    // تشفير البيانات
    $key = hash('sha256', $schoolId . 'kushoof_secret_key_2024', true);
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt(json_encode($data, JSON_UNESCAPED_UNICODE), 'AES-256-CBC', $key, 0, $iv);
    
    // حفظ البيانات المشفرة
    $encryptedData = base64_encode($iv . $encrypted);
    file_put_contents($dataDir . $schoolId . '.enc', $encryptedData);
    
    // حفظ نسخة احتياطية بتاريخ
    $backupFile = $dataDir . $schoolId . '_' . date('Y-m-d_H-i-s') . '.enc';
    file_put_contents($backupFile, $encryptedData);
}

/**
 * قراءة البيانات من الملف المشفر
 */
function loadEncryptedFile($schoolId) {
    $dataDir = __DIR__ . '/../secure/schools/';
    $filePath = $dataDir . $schoolId . '.enc';
    
    if (!file_exists($filePath)) {
        return null;
    }
    
    // قراءة وفك تشفير البيانات
    $encryptedData = base64_decode(file_get_contents($filePath));
    $iv = substr($encryptedData, 0, 16);
    $encrypted = substr($encryptedData, 16);
    
    $key = hash('sha256', $schoolId . 'kushoof_secret_key_2024', true);
    $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    
    return json_decode($decrypted, true);
}

/**
 * تسجيل العمليات
 */
function logOperation($pdo, $schoolId, $operationType, $details, $studentsCount = 0) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO operation_logs (school_id, operation_type, operation_details, students_count, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $stmt->execute([
            $schoolId,
            $operationType,
            $details,
            $studentsCount,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 255)
        ]);
    } catch (Exception $e) {
        // تجاهل أخطاء التسجيل - لا نريد أن تؤثر على العملية الأساسية
        error_log("خطأ في تسجيل العملية: " . $e->getMessage());
    }
}
?>
