# 🚀 مشروع Kushoof - إضافة Chrome للتكامل مع موقع مدرستي

مشروع Kushoof هو إضافة Chrome تتكامل مع موقع مدرستي وWhatsApp Web لإرسال الرسائل للطلاب وأولياء الأمور.

## 📁 هيكل المشروع المنظم

```
kushoof-project/
├── extension/                  # ملفات الإضافة
│   ├── manifest.json          # إعدادات الإضافة
│   ├── content-script.js      # السكريپت الرئيسي
│   └── icons/                 # أيقونات الإضافة
│       └── generate_icons.html
│
├── website/                   # ملفات الموقع
│   ├── .htaccess             # إعادة توجيه وحماية
│   ├── java/                 # مجلد APIs الموحد
│   │   ├── js.php           # API بيانات المدارس
│   │   ├── g.php            # API بيانات WhatsApp
│   │   └── noor.php         # API نظام نور
│   └── logs/                # ملفات السجلات
│
├── test/                     # ملفات الاختبار والمحاكاة
│   ├── setup.html           # دليل التشغيل
│   ├── madrasati-simulation.html
│   └── php-simulation/      # خادم المحاكاة
│
└── README.md                # هذا الملف
```

## 🎯 المكونات الرئيسية

### 📱 الإضافة (extension/)

#### `manifest.json`
- إعدادات الإضافة الأساسية
- صلاحيات الوصول للمواقع
- تحديد المواقع المدعومة

#### `content-script.js`
- السكريپت الرئيسي الذي يتم حقنه في الصفحات
- يفحص نوع الموقع (مدرستي/WhatsApp/نور)
- يستدعي APIs المناسبة من الخادم
- يحقن الواجهات في الصفحات

#### `icons/`
- أيقونات الإضافة بأحجام مختلفة (16px - 128px)
- مولد الأيقونات التلقائي

### 🌐 الموقع (website/)

#### `java/js.php` - API بيانات المدارس
```php
// المعاملات المطلوبة:
// ?version=258&id=SCHOOL_ID&k=K

// الوظيفة:
- جلب بيانات الطلاب من قاعدة البيانات
- إرسال كود JavaScript للحقن في موقع مدرستي
- إنشاء واجهة Kushoof كاملة
```

#### `java/g.php` - API بيانات WhatsApp
```php
// المعاملات المطلوبة:
// ?s=SESSION_ID

// الوظيفة:
- جلب البيانات المخزنة للجلسة
- إرسال بيانات JSON للاستخدام في WhatsApp
- دعم قوالب الرسائل المختلفة
```

#### `java/noor.php` - API نظام نور
```php
// المعاملات المطلوبة:
// ?k=vir3

// الوظيفة:
- التكامل مع نظام نور التعليمي
- إرسال كود JavaScript خاص بنور
- واجهة مخصصة لنظام نور
```

#### `.htaccess`
- إعادة توجيه المسارات القديمة للتوافق العكسي
- حماية الملفات الحساسة
- تحسين الأداء وضغط الملفات

## 🚀 كيفية التشغيل

### 1. إعداد الإضافة

#### أ) إنشاء الأيقونات:
```bash
# افتح في المتصفح
open extension/icons/generate_icons.html
# ستتم تحميل 8 أيقونات تلقائياً
# انقل الأيقونات إلى مجلد extension/icons/
```

#### ب) تحميل الإضافة في Chrome:
1. اذهب إلى `chrome://extensions/`
2. فعّل "Developer mode"
3. اضغط "Load unpacked"
4. اختر مجلد `extension/`

### 2. إعداد الموقع

#### أ) رفع الملفات للخادم:
```bash
# انسخ محتويات مجلد website/ إلى خادم الويب
cp -r website/* /path/to/webserver/
```

#### ب) إعداد قاعدة البيانات:
- قم بتحديث ملفات PHP لتتصل بقاعدة البيانات الحقيقية
- استبدل البيانات الوهمية ببيانات حقيقية

#### ج) إعداد الصلاحيات:
```bash
# تأكد من صلاحيات الكتابة لمجلد logs
chmod 755 logs/
chmod 644 logs/*.log
```

## 🔄 كيف يعمل النظام

### 1. على موقع مدرستي:
```
المستخدم يزور موقع مدرستي
↓
content-script.js يتم تشغيله
↓
استخراج معرف المدرسة من URL
↓
طلب إلى kushoofapp.com/java/js.php
↓
الموقع يرسل كود JavaScript مع بيانات الطلاب
↓
حقن واجهة Kushoof في الصفحة
↓
عرض قائمة الطلاب وأدوات التحكم
```

### 2. على WhatsApp Web:
```
فتح WhatsApp مع البيانات
↓
content-script.js يفحص window.name
↓
إذا وجدت بيانات kushoof
↓
حقن واجهة التحكم في WhatsApp
↓
إمكانية الإرسال التلقائي
```

## 🛠️ التطوير والاختبار

### استخدام ملفات الاختبار:
```bash
# تشغيل خادم المحاكاة
cd test/php-simulation/
php -S localhost:8080

# فتح دليل التشغيل
open test/setup.html

# اختبار محاكاة مدرستي
open test/madrasati-simulation.html
```

## 🔒 الأمان

### حماية الملفات:
- ملفات `.log` محمية من الوصول المباشر
- Headers أمان مضافة في `.htaccess`
- التحقق من المعاملات في جميع APIs

### التحقق من البيانات:
- فحص معرفات المدارس
- التحقق من مفاتيح الوصول
- تسجيل جميع الطلبات

## 📊 المراقبة والسجلات

### ملفات السجل:
- `logs/requests.log` - طلبات API المدارس
- `logs/whatsapp_requests.log` - طلبات API WhatsApp
- `logs/noor_requests.log` - طلبات API نور

### معلومات مسجلة:
- وقت الطلب
- معرف المدرسة/الجلسة
- عدد الطلاب
- عنوان IP

## 🔧 التخصيص

### إضافة مدرسة جديدة:
```php
// في java/js.php
// قم بتحديث استعلام قاعدة البيانات
$query = "SELECT * FROM students WHERE school_id = ?";
```

### تخصيص الواجهة:
```javascript
// في content-script.js
// قم بتعديل دالة buildKushoofInterface()
```

### إضافة موقع جديد:
```javascript
// في content-script.js
// أضف شرط جديد في checkCurrentSite()
```

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- راجع ملفات السجل للأخطاء
- تحقق من console المتصفح
- تأكد من صحة إعدادات قاعدة البيانات

## 📝 الترخيص

هذا المشروع مخصص للاستخدام التعليمي والتطوير.

---

**🎯 ملاحظة:** تأكد من تحديث البيانات الوهمية في ملفات PHP ببيانات حقيقية من قاعدة البيانات قبل الاستخدام في الإنتاج.
