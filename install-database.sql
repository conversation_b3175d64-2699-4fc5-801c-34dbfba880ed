-- إعد<PERSON> قاعدة البيانات لمصمم الكشوف
-- الرابط: https://kushoofapp.com/js/

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS kushoofa_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE kushoofa_db;

-- جدول المدارس
CREATE TABLE IF NOT EXISTS schools (
    id INT AUTO_INCREMENT PRIMARY KEY,
    school_id VARCHAR(32) NOT NULL UNIQUE COMMENT 'معرف المدرسة من مدرستي',
    school_name VARCHAR(255) NOT NULL COMMENT 'اسم المدرسة',
    manager_name VARCHAR(255) DEFAULT NULL COMMENT 'اسم مدير المدرسة',
    ministry_number VARCHAR(50) DEFAULT NULL COMMENT 'الرقم الوزاري للمدرسة',
    total_students INT DEFAULT 0 COMMENT 'إجمالي عدد الطلاب',
    students_count_from_table INT DEFAULT NULL COMMENT 'عدد الطلاب من جدول التقارير',
    teachers_count INT DEFAULT NULL COMMENT 'عدد المعلمين',
    academic_year VARCHAR(10) DEFAULT NULL COMMENT 'العام الدراسي',
    semester VARCHAR(50) DEFAULT NULL COMMENT 'الفصل الدراسي',
    extraction_method VARCHAR(100) DEFAULT NULL COMMENT 'طريقة الاستخراج المستخدمة',
    url TEXT DEFAULT NULL COMMENT 'رابط المدرسة في مدرستي',
    extracted_at DATETIME DEFAULT NULL COMMENT 'تاريخ آخر استخراج',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    
    INDEX idx_school_id (school_id),
    INDEX idx_school_name (school_name),
    INDEX idx_ministry_number (ministry_number),
    INDEX idx_extracted_at (extracted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المدارس';

-- جدول الطلاب
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    school_id VARCHAR(32) NOT NULL COMMENT 'معرف المدرسة',
    student_name VARCHAR(255) NOT NULL COMMENT 'اسم الطالب',
    parent_phone VARCHAR(20) DEFAULT NULL COMMENT 'رقم هاتف ولي الأمر',
    student_phone VARCHAR(20) DEFAULT NULL COMMENT 'رقم هاتف الطالب',
    username VARCHAR(100) DEFAULT NULL COMMENT 'اسم المستخدم',
    national_id VARCHAR(20) DEFAULT NULL COMMENT 'السجل المدني',
    class_label VARCHAR(100) DEFAULT NULL COMMENT 'الفصل الدراسي الكامل',
    grade VARCHAR(50) DEFAULT NULL COMMENT 'الصف',
    section VARCHAR(10) DEFAULT NULL COMMENT 'الفصل',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإضافة',

    INDEX idx_school_id (school_id),
    INDEX idx_student_name (student_name),
    INDEX idx_class_label (class_label),
    INDEX idx_grade (grade),
    INDEX idx_section (section),
    INDEX idx_parent_phone (parent_phone),
    INDEX idx_national_id (national_id),

    FOREIGN KEY (school_id) REFERENCES schools(school_id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الطلاب';

-- جدول سجل العمليات
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    school_id VARCHAR(32) NOT NULL COMMENT 'معرف المدرسة',
    operation_type ENUM('extract', 'save', 'load', 'export') NOT NULL COMMENT 'نوع العملية',
    operation_details TEXT DEFAULT NULL COMMENT 'تفاصيل العملية',
    students_count INT DEFAULT 0 COMMENT 'عدد الطلاب المعالجين',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT 'عنوان IP',
    user_agent TEXT DEFAULT NULL COMMENT 'معلومات المتصفح',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ العملية',
    
    INDEX idx_school_id (school_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل العمليات';

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT 'مفتاح الإعداد',
    setting_value TEXT DEFAULT NULL COMMENT 'قيمة الإعداد',
    setting_description TEXT DEFAULT NULL COMMENT 'وصف الإعداد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات النظام';

-- إدراج الإعدادات الافتراضية
INSERT INTO system_settings (setting_key, setting_value, setting_description) VALUES
('app_name', 'مصمم الكشوف', 'اسم التطبيق'),
('app_version', '5.0.0', 'إصدار التطبيق'),
('api_endpoint', 'https://kushoofapp.com/js/api/receive-data.php', 'رابط API الرئيسي'),
('admin_dashboard', 'https://kushoofapp.com/js/admin/schools-dashboard.php', 'رابط لوحة الإدارة'),
('designer_url', 'https://kushoofapp.com/js/secure/kushoof-designer.php', 'رابط مصمم الكشوف'),
('encryption_enabled', '1', 'تفعيل التشفير للملفات'),
('backup_enabled', '1', 'تفعيل النسخ الاحتياطية'),
('max_students_per_school', '1000', 'الحد الأقصى لعدد الطلاب في المدرسة'),
('data_retention_days', '365', 'عدد أيام الاحتفاظ بالبيانات'),
('secure_folder_path', '/js/secure/schools/', 'مسار مجلد الملفات المشفرة')
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    updated_at = CURRENT_TIMESTAMP;

-- عرض إحصائيات قاعدة البيانات
CREATE VIEW IF NOT EXISTS schools_stats AS
SELECT 
    s.school_id,
    s.school_name,
    s.manager_name,
    s.ministry_number,
    s.total_students,
    s.extracted_at,
    COUNT(st.id) as actual_students_count,
    CASE 
        WHEN s.total_students = COUNT(st.id) THEN 'متطابق'
        WHEN s.total_students > COUNT(st.id) THEN 'ناقص'
        ELSE 'زائد'
    END as data_status
FROM schools s
LEFT JOIN students st ON s.school_id = st.school_id
GROUP BY s.school_id, s.school_name, s.manager_name, s.ministry_number, s.total_students, s.extracted_at;

-- عرض إحصائيات العمليات
CREATE VIEW IF NOT EXISTS operations_summary AS
SELECT 
    DATE(created_at) as operation_date,
    operation_type,
    COUNT(*) as operations_count,
    SUM(students_count) as total_students_processed
FROM operation_logs
GROUP BY DATE(created_at), operation_type
ORDER BY operation_date DESC, operation_type;

-- فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_students_composite ON students(school_id, class_label, student_name);
CREATE INDEX IF NOT EXISTS idx_schools_composite ON schools(school_name, ministry_number);
CREATE INDEX IF NOT EXISTS idx_logs_date ON operation_logs(created_at);

-- إجراء مخزن لحذف البيانات القديمة
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanOldData(IN retention_days INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE old_school_id VARCHAR(32);
    DECLARE cur CURSOR FOR 
        SELECT school_id 
        FROM schools 
        WHERE extracted_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO old_school_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- حذف الطلاب
        DELETE FROM students WHERE school_id = old_school_id;
        
        -- حذف المدرسة
        DELETE FROM schools WHERE school_id = old_school_id;
        
        -- تسجيل العملية
        INSERT INTO operation_logs (school_id, operation_type, operation_details, created_at)
        VALUES (old_school_id, 'cleanup', CONCAT('تم حذف البيانات القديمة بعد ', retention_days, ' يوم'), NOW());
        
    END LOOP;
    CLOSE cur;
    
    COMMIT;
    
    SELECT CONCAT('تم حذف البيانات الأقدم من ', retention_days, ' يوم') as result;
END//
DELIMITER ;

-- إجراء مخزن لإحصائيات سريعة
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS GetQuickStats()
BEGIN
    SELECT 
        (SELECT COUNT(*) FROM schools) as total_schools,
        (SELECT COUNT(*) FROM students) as total_students,
        (SELECT COUNT(*) FROM schools WHERE DATE(extracted_at) = CURDATE()) as schools_today,
        (SELECT COUNT(*) FROM students WHERE DATE(created_at) = CURDATE()) as students_today,
        (SELECT ROUND(AVG(total_students), 0) FROM schools) as avg_students_per_school,
        (SELECT COUNT(*) FROM operation_logs WHERE DATE(created_at) = CURDATE()) as operations_today;
END//
DELIMITER ;

-- إجراء مخزن للبحث في المدارس
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS SearchSchools(
    IN search_term VARCHAR(255),
    IN search_type ENUM('name', 'ministry_number', 'school_id', 'all')
)
BEGIN
    CASE search_type
        WHEN 'name' THEN
            SELECT * FROM schools WHERE school_name LIKE CONCAT('%', search_term, '%');
        WHEN 'ministry_number' THEN
            SELECT * FROM schools WHERE ministry_number LIKE CONCAT('%', search_term, '%');
        WHEN 'school_id' THEN
            SELECT * FROM schools WHERE school_id LIKE CONCAT('%', search_term, '%');
        WHEN 'all' THEN
            SELECT * FROM schools 
            WHERE school_name LIKE CONCAT('%', search_term, '%')
               OR ministry_number LIKE CONCAT('%', search_term, '%')
               OR school_id LIKE CONCAT('%', search_term, '%');
        ELSE
            SELECT * FROM schools WHERE school_name LIKE CONCAT('%', search_term, '%');
    END CASE;
END//
DELIMITER ;

-- تشغيل إحصائيات سريعة
CALL GetQuickStats();

-- عرض معلومات النظام
SELECT 
    'مصمم الكشوف v5.0.0' as system_name,
    'https://kushoofapp.com/js/' as base_url,
    'https://kushoofapp.com/js/api/receive-data.php' as api_endpoint,
    'https://kushoofapp.com/js/admin/schools-dashboard.php' as admin_dashboard,
    'https://kushoofapp.com/js/secure/kushoof-designer.php' as designer_url,
    NOW() as installation_time;

-- عرض هيكل الجداول
SHOW TABLES;

-- عرض الإعدادات
SELECT * FROM system_settings ORDER BY setting_key;

-- رسالة نجاح التثبيت
SELECT 
    '✅ تم تثبيت قاعدة البيانات بنجاح!' as status,
    'يمكنك الآن استخدام النظام من خلال الروابط التالية:' as message,
    'https://kushoofapp.com/js/admin/schools-dashboard.php' as admin_panel,
    'https://kushoofapp.com/js/api/receive-data.php' as api_endpoint;
