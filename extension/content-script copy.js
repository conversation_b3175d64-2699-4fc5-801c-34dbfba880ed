// Content Script - إضا<PERSON><PERSON> Kushoof
// يتم حقن هذا الملف في صفحات الويب المحددة

console.log('🚀 Kushoof Content Script تم تحميله');

// دالة مساعدة للتحقق من القيم
const nullthrows = (v) => {
    if (v == null) throw new Error("it's a null");
    return v;
}

// دالة حقن كود JavaScript
function injectCode(src) {
    const script = document.createElement('script');
    script.src = src;
    script.onload = function() { this.remove(); };
    nullthrows(document.head || document.documentElement).appendChild(script);
}

// فحص الموقع الحالي وتحديد الإجراء المناسب
function checkCurrentSite() {
    const currentURL = window.location.href;
    console.log('🔍 فحص الموقع الحالي:', currentURL);
    
    // فحص موقع مدرستي (النسخة الجديدة)
    if (currentURL.substring(0, 63) === 'https://schools.madrasati.sa/SchoolManagment/Teachers?SchoolId=') {
        console.log('✅ تم اكتشاف موقع مدرستي (النسخة الجديدة)');
        handleMadrasatiSite();
    }
    
    // فحص موقع مدرستي (النسخة التجريبية)
    else if (currentURL.substring(0, 44) === 'https://beta.madrasati.sa/Teachers?SchoolId=') {
        console.log('✅ تم اكتشاف موقع مدرستي (النسخة التجريبية)');
        handleBetaMadrasatiSite();
    }
    
    // فحص موقع نور
    else if (currentURL === "https://noor.moe.gov.sa/Noor/EduWavek12Portal/HomePage.aspx") {
        console.log('✅ تم اكتشاف موقع نور');
        handleNoorSite();
    }
    
    // فحص WhatsApp Web
    else if (currentURL.substring(0, 24) === "https://web.whatsapp.com") {
        console.log('✅ تم اكتشاف WhatsApp Web');
        handleWhatsAppSite();
    }
    
    else {
        console.log('ℹ️ موقع غير مدعوم:', currentURL);
    }
}

// التعامل مع موقع مدرستي (النسخة الجديدة)
function handleMadrasatiSite() {
    // استخراج معرف المدرسة من URL
    const urlParts = window.location.href.split("=");
    const schoolId = urlParts[1];

    console.log('🏫 معرف المدرسة:', schoolId);

    // جلب السكريپت باستخدام fetch (يتجاوز CSP)
    const scriptUrl = `https://kushoofapp.com/java/js.php?version=258&id=${schoolId}&k=K`;
    console.log('📥 جلب السكريپت:', scriptUrl);

    // جلب البيانات مباشرة وبناء الواجهة محلياً
    fetchDataAndBuildInterface(scriptUrl, schoolId);
}

// التعامل مع موقع مدرستي (النسخة التجريبية)
function handleBetaMadrasatiSite() {
    const urlParts = window.location.href.split("=");
    const schoolId = urlParts[1];

    console.log('🏫 معرف المدرسة (بيتا):', schoolId);

    const scriptUrl = `https://kushoofapp.com/java/js.php?version=280&id=${schoolId}&k=K`;
    console.log('📥 جلب السكريپت (بيتا):', scriptUrl);

    // جلب البيانات مباشرة وبناء الواجهة محلياً
    fetchDataAndBuildInterface(scriptUrl, schoolId);
}

// التعامل مع موقع نور
function handleNoorSite() {
    console.log('🎓 جلب سكريپت نور');
    // استخدام Background Script لجلب وحقن سكريپت نور
    fetchScriptViaBackground(`https://kushoofapp.com/java/noor.php?k=vir3`, 'noor');
}

// التعامل مع WhatsApp Web
function handleWhatsAppSite() {
    console.log('💬 معالجة WhatsApp Web');
    
    // فحص إذا كان هناك بيانات في URL
    const urlSegment = window.location.href.substring(26, 100);
    
    if (urlSegment.length > 30 && urlSegment.indexOf("=") < 0) {
        console.log('🔄 جلب البيانات من kushoofapp.com');
        
        // جلب البيانات من الخادم
        fetch('https://kushoofapp.com/java/g.php?s=' + urlSegment)
            .then(response => response.text())
            .then(data => {
                console.log('📊 تم استلام البيانات:', data);
                window.name = data;
                window.location.href = 'https://web.whatsapp.com';
            })
            .catch(error => {
                console.error('❌ خطأ في جلب البيانات:', error);
            });
    }
    
    // تهيئة واجهة WhatsApp
    initializeWhatsAppInterface();
}

// دالة جلب البيانات وبناء الواجهة محلياً (الحل النهائي)
async function fetchDataAndBuildInterface(url, schoolId) {
    console.log('📊 جلب البيانات من:', url);

    try {
        // جلب البيانات من الخادم
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const scriptContent = await response.text();
        console.log('📥 تم جلب البيانات بنجاح، الحجم:', scriptContent.length, 'حرف');

        // استخراج البيانات من السكريپت
        const studentsData = extractDataFromScript(scriptContent);

        if (studentsData) {
            console.log('✅ تم استخراج بيانات الطلاب:', studentsData);

            // تخزين البيانات في window.name للاستخدام لاحقاً
            window.name = 'kushoof' + JSON.stringify(studentsData);

            // بدء تهيئة الواجهة
            initializeKushoofInterface();
        } else {
            console.error('❌ فشل في استخراج البيانات من السكريپت');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error);

        // إنشاء بيانات تجريبية في حالة الفشل
        const fallbackData = createFallbackData(schoolId);
        window.name = 'kushoof' + JSON.stringify(fallbackData);
        console.log('🔄 استخدام بيانات تجريبية');
        initializeKushoofInterface();
    }
}

// دالة استخراج البيانات من السكريپت
function extractDataFromScript(scriptContent) {
    try {
        console.log('🔍 البحث عن بيانات kushoof_data في السكريپت...');

        // طريقة 1: البحث عن kushoof_data مع الأقواس المجعدة
        let dataMatch = scriptContent.match(/var kushoof_data\s*=\s*({[\s\S]*?});/);

        if (!dataMatch) {
            // طريقة 2: البحث بنمط أوسع
            dataMatch = scriptContent.match(/kushoof_data\s*=\s*({[\s\S]*?});/);
        }

        if (dataMatch) {
            console.log('✅ تم العثور على kushoof_data');

            // تنظيف وتحليل البيانات
            let dataString = dataMatch[1];

            // تنظيف البيانات
            dataString = dataString
                .replace(/'/g, '"')  // استبدال الأقواس المفردة
                .replace(/,\s*}/g, '}')  // إزالة الفواصل الزائدة
                .replace(/,\s*]/g, ']'); // إزالة الفواصل الزائدة في المصفوفات

            console.log('📝 البيانات المستخرجة:', dataString.substring(0, 200) + '...');

            // تحليل JSON
            const data = JSON.parse(dataString);

            // فك تشفير أسماء الطلاب إذا كانت مشفرة
            if (data.n && Array.isArray(data.n)) {
                data.n = data.n.map(name => {
                    try {
                        return decodeURIComponent(name);
                    } catch (e) {
                        return name; // إذا فشل فك التشفير، استخدم الاسم كما هو
                    }
                });
            }

            // فك تشفير أسماء الفصول إذا وجدت
            if (data.c && Array.isArray(data.c)) {
                data.c = data.c.map(className => {
                    try {
                        return decodeURIComponent(className);
                    } catch (e) {
                        return className;
                    }
                });
            }

            console.log('✅ تم استخراج البيانات بنجاح:', {
                students: data.n ? data.n.length : 0,
                phones: data.p ? data.p.length : 0,
                school_id: data.school_id
            });

            return data;
        }

        // إذا لم نجد البيانات، نحاول استخراج معلومات أساسية
        console.warn('⚠️ لم يتم العثور على kushoof_data، محاولة استخراج بديل');

        // البحث عن أي بيانات طلاب في السكريپت
        const namesMatch = scriptContent.match(/'n':\s*\[(.*?)\]/);
        const phonesMatch = scriptContent.match(/'p':\s*\[(.*?)\]/);

        if (namesMatch && phonesMatch) {
            console.log('✅ تم العثور على بيانات بديلة');

            const names = namesMatch[1].split(',').map(name =>
                name.trim().replace(/['"]/g, '')
            );
            const phones = phonesMatch[1].split(',').map(phone =>
                phone.trim().replace(/['"]/g, '')
            );

            return {
                n: names,
                p: phones,
                day: 'اليوم',
                school_id: 'extracted',
                version: '258'
            };
        }

        return null;

    } catch (error) {
        console.error('❌ خطأ في استخراج البيانات:', error);
        return null;
    }
}

// دالة إنشاء بيانات تجريبية
function createFallbackData(schoolId) {
    const today = new Date();
    const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = arabicDays[today.getDay()];
    const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

    return {
        n: ['أحمد محمد العلي', 'فاطمة سالم الأحمد', 'محمد عبدالله الحسن', 'نورا علي السالم'],
        p: ['966501234567', '966507654321', '966509876543', '966512345678'],
        day: formattedDate,
        school_id: schoolId,
        version: '258',
        timestamp: new Date().toISOString(),
        fallback: true
    };
}

// دالة جلب السكريپت عبر Background Script (احتياطية)
function fetchScriptViaBackground(url, schoolId) {
    console.log('📨 إرسال طلب لـ Background Script:', url);

    // إرسال رسالة لـ Background Script
    chrome.runtime.sendMessage({
        action: 'fetchAndInjectScript',
        url: url,
        schoolId: schoolId
    }, (response) => {
        if (chrome.runtime.lastError) {
            console.error('❌ خطأ في التواصل مع Background Script:', chrome.runtime.lastError);
            return;
        }

        if (response && response.success) {
            console.log('✅ تم إرسال الطلب بنجاح لـ Background Script');
        } else {
            console.error('❌ فشل في معالجة الطلب:', response?.error);
        }
    });
}

// استقبال إشعارات من main world
window.addEventListener('message', (event) => {
    if (event.data.type === 'KUSHOOF_SCRIPT_LOADED') {
        if (event.data.success) {
            console.log('✅ تم تحميل سكريپت Kushoof بنجاح في main world');
            console.log('🏫 معرف المدرسة:', event.data.schoolId);

            // بدء تهيئة الواجهة بعد تأخير قصير
            setTimeout(() => {
                initializeKushoofInterface();
            }, 1000);
        } else {
            console.error('❌ فشل في تحميل سكريپت Kushoof:', event.data.error);
        }
    }
});

// دالة جلب وتنفيذ السكريپت (احتياطية - لن تستخدم الآن)
async function fetchAndExecuteScript(url) {
    console.log('📜 جلب سكريپت:', url);

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const scriptContent = await response.text();
        console.log('📥 تم جلب السكريپت بنجاح، الحجم:', scriptContent.length, 'حرف');

        // تنفيذ السكريپت باستخدام طرق متعددة (تتجاوز CSP)
        return await injectScriptIntoMainWorld(scriptContent);
        return true;

    } catch (error) {
        console.error('❌ خطأ في جلب السكريپت:', error);
        throw error;
    }
}

// دالة حقن السكريپت في main world (تتجاوز CSP)
async function injectScriptIntoMainWorld(scriptContent) {
    console.log('🔧 محاولة حقن السكريپت بطرق متعددة...');

    // طريقة 1: استخدام data URL
    try {
        const dataUrl = 'data:text/javascript;charset=utf-8,' + encodeURIComponent(scriptContent);
        const script = document.createElement('script');
        script.src = dataUrl;

        return new Promise((resolve, reject) => {
            script.onload = () => {
                console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام data URL');
                script.remove();
                resolve();
            };
            script.onerror = () => {
                console.warn('⚠️ فشل data URL، جاري المحاولة بطريقة أخرى');
                script.remove();

                // طريقة 2: استخدام Function constructor
                try {
                    const executeScript = new Function(scriptContent);
                    executeScript();
                    console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام Function()');
                    resolve();
                } catch (funcError) {
                    console.warn('⚠️ فشل Function()، جاري المحاولة بـ eval()');
                    try {
                        // طريقة 3: استخدام eval كبديل أخير
                        eval(scriptContent);
                        console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام eval()');
                        resolve();
                    } catch (evalError) {
                        console.error('❌ فشل في جميع طرق تنفيذ السكريپت:', evalError);
                        reject(evalError);
                    }
                }
            };

            document.head.appendChild(script);
        });

    } catch (error) {
        console.error('❌ خطأ في إنشاء data URL:', error);

        // طريقة احتياطية مباشرة
        try {
            const executeScript = new Function(scriptContent);
            executeScript();
            console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام Function() (احتياطي)');
            return Promise.resolve();
        } catch (funcError) {
            console.error('❌ فشل في الطريقة الاحتياطية:', funcError);
            return Promise.reject(funcError);
        }
    }
}

// دالة تحميل السكريپت من الخادم (احتياطية)
function loadScript(url, callback) {
    console.log('📜 تحميل سكريپت:', url);

    const head = document.getElementsByTagName('head')[0];
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onreadystatechange = callback;
    script.onload = callback;
    script.onerror = function() {
        console.error('❌ فشل في تحميل السكريپت:', url);
    };
    head.appendChild(script);
}

// تهيئة واجهة Kushoof في موقع مدرستي
function initializeKushoofInterface() {
    console.log('🎨 تهيئة واجهة Kushoof');
    
    // البحث عن header أو عناصر بديلة
    function findHeader() {
        // البحث عن header أولاً
        let targetElement = document.getElementsByTagName("header")[0];

        // إذا لم يوجد header، ابحث عن عناصر بديلة
        if (!targetElement) {
            const alternatives = [
                document.querySelector('.navbar'),
                document.querySelector('.header'),
                document.querySelector('#header'),
                document.querySelector('nav'),
                document.querySelector('.top-bar'),
                document.querySelector('body > div:first-child'),
                document.body
            ];

            for (const element of alternatives) {
                if (element) {
                    targetElement = element;
                    console.log('✅ تم العثور على عنصر بديل:', element.tagName, element.className);
                    break;
                }
            }
        } else {
            console.log('✅ تم العثور على header');
        }

        if (targetElement) {
            injectKushoofInterface(targetElement);
        } else {
            console.log('⏳ البحث عن عناصر مناسبة...');
            setTimeout(findHeader, 2000);
        }
    }
    
    findHeader();
}

// حقن واجهة Kushoof
function injectKushoofInterface(targetElement) {
    console.log('💉 حقن واجهة Kushoof في:', targetElement ? targetElement.tagName : 'عنصر غير محدد');

    // حقن CSS
    injectCSS();

    // استخراج البيانات من window.name
    let studentsData;
    try {
        if (window.name.includes('kushoof')) {
            studentsData = JSON.parse(window.name.split('kushoof')[1]);
            console.log('📊 تم استخراج بيانات الطلاب:', studentsData);
        } else {
            console.warn('⚠️ لم يتم العثور على بيانات kushoof في window.name');
            // إنشاء بيانات تجريبية للاختبار
            studentsData = {
                n: ['طالب تجريبي 1', 'طالب تجريبي 2', 'طالب تجريبي 3'],
                p: ['966501234567', '966507654321', '966509876543'],
                day: 'اليوم التجريبي',
                school_id: 'test_school',
                fallback: true
            };
            console.log('🔄 استخدام بيانات تجريبية محلية');
        }
    } catch (error) {
        console.error('❌ خطأ في استخراج البيانات:', error);
        return;
    }

    // بناء HTML الواجهة
    const interfaceHTML = buildKushoofInterface(studentsData);

    // حقن الواجهة في العنصر المستهدف
    if (targetElement) {
        // إزالة أي واجهة موجودة مسبقاً
        const existingInterface = document.querySelector('.kushoof-interface');
        if (existingInterface) {
            existingInterface.remove();
        }

        if (targetElement.tagName.toLowerCase() === 'header') {
            // استبدال header كاملاً
            targetElement.outerHTML = interfaceHTML;
            console.log('✅ تم استبدال header بواجهة Kushoof');
        } else {
            // إضافة الواجهة في أعلى العنصر مع معرف فريد
            const kushoofContainer = document.createElement('div');
            kushoofContainer.id = 'kushoof-container-' + Date.now();
            kushoofContainer.innerHTML = interfaceHTML;
            kushoofContainer.style.cssText = 'position: relative; z-index: 10000; width: 100%;';

            targetElement.insertBefore(kushoofContainer, targetElement.firstChild);
            console.log('✅ تم إضافة واجهة Kushoof في أعلى الصفحة');
        }

        // إضافة مستمعي الأحداث
        setTimeout(() => {
            addEventListeners();

            // بدء مراقبة DOM لمنع اختفاء الواجهة
            startDOMWatcher();

            // فحص دوري إضافي للتأكد من بقاء الواجهة
            startPeriodicCheck();
        }, 100);
    } else {
        console.error('❌ لم يتم العثور على عنصر مناسب لحقن الواجهة');
    }
}

// حقن CSS
function injectCSS() {
    const style = document.createElement('style');
    style.textContent = `
        .kushoof-btn {
            font-size: 18px;
            width: 80%;
            border-radius: 5px;
            height: 30px;
            background: #4682b4;
            color: white;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        
        .kushoof-option {
            font-weight: bold;
            font-size: 16px;
            color: #006400;
        }
        
        .kushoof-interface {
            background: linear-gradient(135deg, #128c7e 0%, #075e54 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            position: relative !important;
            z-index: 9999 !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 200px;
            display: block !important;
            visibility: visible !important;
        }

        /* Modal Styles */
        .kushoof-modal {
            display: none;
            position: fixed;
            z-index: 99999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .kushoof-modal-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .kushoof-modal-header {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .kushoof-modal-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }

        .kushoof-modal-close {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
        }

        .kushoof-modal-close:hover {
            background: rgba(255,255,255,0.3);
            transform: rotate(90deg);
        }

        .kushoof-modal-body {
            padding: 20px;
            color: white;
            max-height: 60vh;
            overflow-y: auto;
        }

        .kushoof-data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .kushoof-data-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .kushoof-data-card h3 {
            margin: 0 0 15px 0;
            color: #FFD700;
            font-size: 18px;
        }

        .kushoof-student-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 14px;
        }

        .kushoof-student-item:last-child {
            border-bottom: none;
        }

        .kushoof-student-name {
            flex: 1;
            font-weight: bold;
        }

        .kushoof-student-phone {
            flex: 1;
            text-align: center;
            font-family: monospace;
        }

        .kushoof-student-status {
            width: 60px;
            text-align: center;
        }

        .kushoof-refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s;
        }

        .kushoof-refresh-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
    `;
    document.head.appendChild(style);
    console.log('🎨 تم حقن CSS');
}

// بناء HTML واجهة Kushoof
function buildKushoofInterface(data) {
    if (!data) return '';
    
    let optionsHTML = '';
    for (let i = 0; i < data.n.length; i++) {
        const style = data.p[i].length < 12 ? 'style="color:red"' : '';
        optionsHTML += `
            <option class="kushoof-option" ${style} value="${data.p[i]}">
                ${i + 1} - ${decodeURIComponent(data.n[i])}
            </option>
        `;
    }
    
    // معلومات إضافية
    const totalStudents = data.n ? data.n.length : 0;
    const validPhones = data.p ? data.p.filter(phone => phone.length >= 12).length : 0;
    const invalidPhones = totalStudents - validPhones;
    const dataSource = data.fallback ? '(بيانات تجريبية)' : '(بيانات حقيقية)';

    return `
        <div class="kushoof-interface">
            <a id="kushoof-link" href="" style="visibility:hidden"></a>
            <h2>🚀 Kushoof WhatsApp</h2>
            <div style="font-size: 12px; opacity: 0.8; margin-bottom: 10px;">
                📅 ${data.day || 'اليوم'} | 🏫 ${data.school_id || 'غير محدد'} ${dataSource}
            </div>
            <hr style="width:70%;">

            <div style="margin: 15px 0; font-size: 13px;">
                <span style="color: #4CAF50;">✅ صحيح: ${validPhones}</span> |
                <span style="color: #f44336;">❌ خطأ: ${invalidPhones}</span> |
                <span style="color: #2196F3;">📊 المجموع: ${totalStudents}</span>
            </div>

            <div style="margin: 20px 0;">
                <button id="start-btn" class="kushoof-btn">بدء الإرسال</button>
                <button id="stop-btn" class="kushoof-btn" style="background:#dc143c;">إيقاف الإرسال</button>
                <button id="whatsapp-btn" class="kushoof-btn" style="background:#25D366;">📱 فتح WhatsApp</button>
                <button id="modal-btn" class="kushoof-btn" style="background:#FF9800;">📊 عرض البيانات</button>
            </div>

            <div>
                <h3>قائمة بيانات الإرسال</h3>
                <select id="students-select" size="4" style="width:90%; padding:10px; font-size:12px;">
                    ${optionsHTML}
                </select>
            </div>

            <div style="margin-top: 20px;">
                <textarea id="message-text" rows="3" style="width:90%; padding:10px; font-size:12px;"
                          placeholder="اكتب نص الرسالة هنا...">عزيزي ولي أمر الطالب، نود إعلامكم بأن الطالب غائب اليوم.</textarea>
                <br>
                <input id="signature-text" style="width:90%; padding:8px; margin-top:10px; font-size:12px;"
                       placeholder="تذييل الرسالة" value="مع تحيات إدارة المدرسة">
            </div>

            <div style="margin-top: 15px; font-size: 11px; opacity: 0.7;">
                💡 اضغط Enter على اسم الطالب للإرسال السريع
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    console.log('🎧 إضافة مستمعي الأحداث');

    // ضمان وجود الدوال في النطاق العام
    ensureGlobalFunctions();

    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    const whatsappBtn = document.getElementById('whatsapp-btn');
    const modalBtn = document.getElementById('modal-btn');
    const studentsSelect = document.getElementById('students-select');

    if (startBtn) {
        startBtn.addEventListener('click', startSending);
    }

    if (stopBtn) {
        stopBtn.addEventListener('click', stopSending);
    }

    if (whatsappBtn) {
        whatsappBtn.addEventListener('click', openWhatsApp);
    }

    if (modalBtn) {
        modalBtn.addEventListener('click', openDataModal);
    }

    if (studentsSelect) {
        studentsSelect.addEventListener('change', onStudentSelect);
    }
    
    // مستمع Enter للإرسال السريع
    document.addEventListener('keypress', function(event) {
        if (event.key === 'Enter' && document.activeElement.id === 'students-select') {
            sendQuickMessage();
        }
    });
}

// تهيئة واجهة WhatsApp
function initializeWhatsAppInterface() {
    console.log('💬 تهيئة واجهة WhatsApp');
    
    // فحص وجود البيانات في window.name
    if (window.name.includes('kushoof')) {
        console.log('📊 تم العثور على بيانات في window.name');
        setupWhatsAppInterface();
    }
}

// إعداد واجهة WhatsApp
function setupWhatsAppInterface() {
    // البحث عن header في WhatsApp
    function findWhatsAppHeader() {
        const headers = document.getElementsByTagName("header");
        
        if (headers.length > 0) {
            console.log('✅ تم العثور على header في WhatsApp');
            injectWhatsAppControls();
        } else {
            console.log('⏳ البحث عن header في WhatsApp...');
            setTimeout(findWhatsAppHeader, 2000);
        }
    }
    
    findWhatsAppHeader();
}

// حقن أدوات التحكم في WhatsApp
function injectWhatsAppControls() {
    console.log('💉 حقن أدوات التحكم في WhatsApp');
    // هنا يتم حقن الواجهة المخصصة في WhatsApp
}

// وظائف التحكم
function startSending() {
    console.log('▶️ بدء الإرسال');
    // تنفيذ منطق الإرسال
}

function stopSending() {
    console.log('⏹️ إيقاف الإرسال');
    // إيقاف عملية الإرسال
}

function onStudentSelect() {
    console.log('👤 تم اختيار طالب');
    // تحديث الرسالة حسب الطالب المختار
}

function sendQuickMessage() {
    console.log('⚡ إرسال سريع');
    const studentsSelect = document.getElementById('students-select');
    const messageText = document.getElementById('message-text');

    if (studentsSelect && studentsSelect.selectedOptions.length > 0) {
        const selectedPhone = studentsSelect.selectedOptions[0].value;
        const message = messageText ? messageText.value : 'رسالة سريعة';

        if (selectedPhone && selectedPhone.length >= 12) {
            const whatsappUrl = `https://wa.me/${selectedPhone}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
            console.log('📱 تم فتح WhatsApp للرقم:', selectedPhone);
        } else {
            alert('رقم الهاتف غير صحيح');
        }
    } else {
        alert('يرجى اختيار طالب أولاً');
    }
}

function openWhatsApp() {
    console.log('📱 فتح WhatsApp Web مع البيانات');

    // فتح WhatsApp Web مع تمرير البيانات
    const whatsappWindow = window.open('https://web.whatsapp.com', '_blank');

    // تمرير البيانات للنافذة الجديدة
    setTimeout(() => {
        if (whatsappWindow && !whatsappWindow.closed) {
            whatsappWindow.name = window.name; // نقل البيانات
            console.log('✅ تم تمرير البيانات لـ WhatsApp Web');
        }
    }, 1000);
}

// دالة ضمان وجود الدوال في النطاق العام
function ensureGlobalFunctions() {
    // إضافة جميع الدوال للنطاق العام
    window.closeDataModal = closeDataModal;
    window.loadModalData = loadModalData;
    window.refreshModalData = refreshModalData;
    window.fetchFromDatabase = fetchFromDatabase;
    window.fetchFromWebsite = fetchFromWebsite;
    window.scrapeFromPage = scrapeFromPage;
    window.exportModalData = exportModalData;

    // إضافة دالة ضمان الدوال نفسها للنطاق العام
    window.ensureGlobalFunctions = ensureGlobalFunctions;

    console.log('✅ تم ضمان وجود جميع الدوال في النطاق العام');
}

// تشغيل الدالة فور تحميل السكريپت
ensureGlobalFunctions();

// دوال الموديل
function openDataModal() {
    console.log('📊 فتح موديل البيانات');

    // إضافة الدوال للنطاق العام أولاً
    ensureGlobalFunctions();

    // إنشاء الموديل إذا لم يكن موجود
    let modal = document.getElementById('kushoof-modal');
    if (!modal) {
        createDataModal();
        modal = document.getElementById('kushoof-modal');
    }

    // عرض الموديل
    modal.style.display = 'block';

    // تحميل البيانات
    loadModalData();
}

function createDataModal() {
    const modalHTML = `
        <div id="kushoof-modal" class="kushoof-modal">
            <div class="kushoof-modal-content">
                <div class="kushoof-modal-header">
                    <h2 class="kushoof-modal-title">📊 بيانات الطلاب - Kushoof</h2>
                    <button id="close-modal-btn" class="kushoof-modal-close">×</button>
                </div>
                <div class="kushoof-modal-body">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="refresh-modal-btn" class="kushoof-refresh-btn">🔄 تحديث البيانات</button>
                        <button id="fetch-database-btn" class="kushoof-refresh-btn" style="background: #4CAF50;">🗄️ جلب من قاعدة البيانات</button>
                        <button id="fetch-website-btn" class="kushoof-refresh-btn" style="background: #2196F3;">🌐 جلب من الخادم</button>
                        <button id="scrape-page-btn" class="kushoof-refresh-btn" style="background: #9C27B0;">🔍 استخراج من الصفحة</button>
                        <button id="export-modal-btn" class="kushoof-refresh-btn" style="background: #FF9800;">📥 تصدير</button>
                    </div>
                    <div id="modal-loading" style="text-align: center; padding: 40px;">
                        <div style="font-size: 18px;">⏳ جاري تحميل البيانات...</div>
                    </div>
                    <div id="modal-content" style="display: none;">
                        <!-- سيتم ملء المحتوى هنا -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة الموديل للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // إضافة مستمع إغلاق عند النقر خارج الموديل
    const modal = document.getElementById('kushoof-modal');
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeDataModal();
        }
    });

    // إضافة مستمعي الأحداث للأزرار
    addModalEventListeners();

    // إضافة الدوال للنطاق العام (مرة أخرى للتأكيد)
    ensureGlobalFunctions();

    console.log('✅ تم إنشاء موديل البيانات');
}

function addModalEventListeners() {
    console.log('🎧 إضافة مستمعي أحداث الموديل');

    // زر الإغلاق
    const closeBtn = document.getElementById('close-modal-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeDataModal);
    }

    // زر تحديث البيانات
    const refreshBtn = document.getElementById('refresh-modal-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshModalData);
    }

    // زر جلب من قاعدة البيانات
    const fetchDbBtn = document.getElementById('fetch-database-btn');
    if (fetchDbBtn) {
        fetchDbBtn.addEventListener('click', fetchFromDatabase);
    }

    // زر جلب من الخادم
    const fetchWebBtn = document.getElementById('fetch-website-btn');
    if (fetchWebBtn) {
        fetchWebBtn.addEventListener('click', fetchFromWebsite);
    }

    // زر استخراج من الصفحة
    const scrapeBtn = document.getElementById('scrape-page-btn');
    if (scrapeBtn) {
        scrapeBtn.addEventListener('click', scrapeFromPage);
    }

    // زر التصدير
    const exportBtn = document.getElementById('export-modal-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportModalData);
    }

    console.log('✅ تم إضافة جميع مستمعي أحداث الموديل');
}

function closeDataModal() {
    const modal = document.getElementById('kushoof-modal');
    if (modal) {
        modal.style.display = 'none';
        console.log('❌ تم إغلاق موديل البيانات');
    }
}

function loadModalData() {
    console.log('📥 تحميل بيانات الموديل');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // محاولة جلب البيانات من window.name أولاً
    let studentsData = null;
    try {
        if (window.name.includes('kushoof')) {
            studentsData = JSON.parse(window.name.split('kushoof')[1]);
        }
    } catch (e) {
        console.warn('⚠️ فشل في جلب البيانات من window.name');
    }

    // إذا لم توجد بيانات، جلب من الخادم
    if (!studentsData) {
        fetchModalDataFromServer();
    } else {
        displayModalData(studentsData);
    }
}

async function fetchModalDataFromServer() {
    console.log('🌐 جلب البيانات من الخادم');

    try {
        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1];

        if (!schoolId) {
            throw new Error('لم يتم العثور على معرف المدرسة');
        }

        // جلب البيانات
        const scriptUrl = `https://kushoofapp.com/java/js.php?version=258&id=${schoolId}&k=K`;
        const response = await fetch(scriptUrl);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const scriptContent = await response.text();
        const studentsData = extractDataFromScript(scriptContent);

        if (studentsData) {
            displayModalData(studentsData);
            console.log('✅ تم جلب البيانات من الخادم بنجاح');
        } else {
            throw new Error('فشل في استخراج البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        displayModalError(error.message);
    }
}

async function fetchModalDataFromDatabase() {
    console.log('🗄️ جلب البيانات من قاعدة البيانات');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    try {
        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1];

        if (!schoolId) {
            throw new Error('لم يتم العثور على معرف المدرسة في URL');
        }

        console.log('🔍 البحث عن بيانات المدرسة:', schoolId);

        // إرسال طلب لقاعدة البيانات
        const response = await fetch('https://kushoofapp.com/js/api/receive-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                action: 'load',
                schoolId: schoolId
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📊 استجابة قاعدة البيانات:', result);

        if (!result.success) {
            throw new Error(result.message || 'فشل في جلب البيانات من قاعدة البيانات');
        }

        // تحويل البيانات إلى التنسيق المطلوب
        const databaseData = convertDatabaseToKushoofFormat(result.data);

        if (databaseData) {
            // حفظ البيانات في window.name
            window.name = 'kushoof' + JSON.stringify(databaseData);
            displayDatabaseData(result.data, databaseData);
            console.log('✅ تم جلب البيانات من قاعدة البيانات بنجاح');
        } else {
            throw new Error('فشل في تحويل البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات من قاعدة البيانات:', error);
        displayModalError('فشل في جلب البيانات من قاعدة البيانات: ' + error.message);
    }
}

function convertDatabaseToKushoofFormat(dbData) {
    try {
        const school = dbData.school;
        const students = dbData.students || [];

        console.log('🔄 تحويل بيانات قاعدة البيانات:', {
            school: school?.school_name,
            studentsCount: students.length
        });

        // تحويل بيانات الطلاب
        const names = [];
        const phones = [];
        const classes = [];

        students.forEach(student => {
            names.push(encodeURIComponent(student.student_name || ''));
            phones.push(student.parent_phone || student.student_phone || '');
            classes.push(encodeURIComponent(student.class_label || student.grade || ''));
        });

        // إنشاء التاريخ
        const today = new Date();
        const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const dayName = arabicDays[today.getDay()];
        const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

        return {
            n: names,
            p: phones,
            c: classes,
            day: formattedDate,
            school_id: school?.school_id || 'unknown',
            school_name: school?.school_name || 'غير محدد',
            manager_name: school?.manager_name || 'غير محدد',
            ministry_number: school?.ministry_number || '',
            version: 'database',
            timestamp: new Date().toISOString(),
            source: 'database',
            total_students: students.length,
            extracted_at: school?.extracted_at,
            updated_at: school?.updated_at
        };

    } catch (error) {
        console.error('❌ خطأ في تحويل البيانات:', error);
        return null;
    }
}

function displayModalData(data) {
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إخفاء شاشة التحميل
    loadingDiv.style.display = 'none';
    contentDiv.style.display = 'block';

    // إحصائيات
    const totalStudents = data.n ? data.n.length : 0;
    const validPhones = data.p ? data.p.filter(phone => phone.length >= 12).length : 0;
    const invalidPhones = totalStudents - validPhones;

    // بناء HTML المحتوى
    let contentHTML = `
        <div class="kushoof-data-grid">
            <div class="kushoof-data-card">
                <h3>📊 الإحصائيات</h3>
                <div style="font-size: 16px; line-height: 1.8;">
                    <div>👥 إجمالي الطلاب: <strong>${totalStudents}</strong></div>
                    <div style="color: #4CAF50;">✅ أرقام صحيحة: <strong>${validPhones}</strong></div>
                    <div style="color: #f44336;">❌ أرقام خاطئة: <strong>${invalidPhones}</strong></div>
                    <div>📅 التاريخ: <strong>${data.day || 'غير محدد'}</strong></div>
                    <div>🏫 المدرسة: <strong>${data.school_id || 'غير محدد'}</strong></div>
                </div>
            </div>

            <div class="kushoof-data-card">
                <h3>ℹ️ معلومات النظام</h3>
                <div style="font-size: 14px; line-height: 1.6;">
                    <div>🔢 الإصدار: ${data.version || 'غير محدد'}</div>
                    <div>⏰ الوقت: ${data.timestamp || 'غير محدد'}</div>
                    <div>🔗 المصدر: ${data.fallback ? 'بيانات تجريبية' : 'بيانات حقيقية'}</div>
                    <div>📡 API: /java/js.php</div>
                </div>
            </div>
        </div>

        <div class="kushoof-data-card">
            <h3>👥 قائمة الطلاب</h3>
            <div style="max-height: 300px; overflow-y: auto;">
    `;

    // إضافة قائمة الطلاب
    if (data.n && data.p) {
        for (let i = 0; i < data.n.length; i++) {
            const name = decodeURIComponent(data.n[i] || '');
            const phone = data.p[i] || '';
            const className = data.c && data.c[i] ? decodeURIComponent(data.c[i]) : '';
            const isValid = phone.length >= 12;
            const statusIcon = isValid ? '✅' : '❌';
            const statusColor = isValid ? '#4CAF50' : '#f44336';

            contentHTML += `
                <div class="kushoof-student-item">
                    <div class="kushoof-student-name">${i + 1}. ${name}</div>
                    <div class="kushoof-student-phone" style="color: ${statusColor};">${phone}</div>
                    <div class="kushoof-student-status" style="color: ${statusColor};">${statusIcon}</div>
                </div>
            `;
        }
    } else {
        contentHTML += '<div style="text-align: center; padding: 20px; opacity: 0.7;">لا توجد بيانات طلاب</div>';
    }

    contentHTML += `
            </div>
        </div>
    `;

    contentDiv.innerHTML = contentHTML;
    console.log('✅ تم عرض البيانات في الموديل');
}

function displayModalError(errorMessage) {
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    loadingDiv.style.display = 'none';
    contentDiv.style.display = 'block';

    contentDiv.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
            <div style="font-size: 18px; margin-bottom: 10px;">خطأ في تحميل البيانات</div>
            <div style="font-size: 14px; opacity: 0.8;">${errorMessage}</div>
            <button class="kushoof-refresh-btn" onclick="loadModalData()" style="margin-top: 20px;">🔄 إعادة المحاولة</button>
        </div>
    `;
}

// دوال إضافية للموديل
function refreshModalData() {
    console.log('🔄 تحديث بيانات الموديل');

    // التأكد من وجود الموديل
    const modal = document.getElementById('kushoof-modal');
    if (!modal) {
        console.error('❌ الموديل غير موجود');
        alert('خطأ: الموديل غير موجود. يرجى إعادة فتح الموديل.');
        return;
    }

    // إظهار قائمة خيارات التحديث
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';

    contentDiv.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <h3 style="color: #FFD700; margin-bottom: 30px;">🔄 اختر مصدر التحديث</h3>

            <div style="display: grid; gap: 15px; max-width: 400px; margin: 0 auto;">
                <button id="load-saved-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px;">
                    💾 من البيانات المحفوظة
                </button>

                <button id="fetch-db-refresh-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px; background: #4CAF50;">
                    🗄️ من قاعدة البيانات
                </button>

                <button id="fetch-web-refresh-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px; background: #2196F3;">
                    🌐 من الخادم
                </button>

                <button id="scrape-refresh-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px; background: #9C27B0;">
                    🔍 من الصفحة الحالية
                </button>
            </div>

            <div style="margin-top: 30px; font-size: 12px; opacity: 0.7;">
                💡 اختر المصدر المناسب لجلب أحدث البيانات
            </div>
        </div>
    `;

        // إضافة مستمعي الأحداث للأزرار الجديدة
        setTimeout(() => {
            console.log('🎧 إضافة مستمعي أحداث أزرار التحديث');

            const loadSavedBtn = document.getElementById('load-saved-btn');
            if (loadSavedBtn) {
                loadSavedBtn.addEventListener('click', loadModalData);
                console.log('✅ تم إضافة مستمع لزر البيانات المحفوظة');
            }

            const fetchDbRefreshBtn = document.getElementById('fetch-db-refresh-btn');
            if (fetchDbRefreshBtn) {
                fetchDbRefreshBtn.addEventListener('click', fetchFromDatabase);
                console.log('✅ تم إضافة مستمع لزر قاعدة البيانات');
            }

            const fetchWebRefreshBtn = document.getElementById('fetch-web-refresh-btn');
            if (fetchWebRefreshBtn) {
                fetchWebRefreshBtn.addEventListener('click', fetchFromWebsite);
                console.log('✅ تم إضافة مستمع لزر الخادم');
            }

            const scrapeRefreshBtn = document.getElementById('scrape-refresh-btn');
            if (scrapeRefreshBtn) {
                scrapeRefreshBtn.addEventListener('click', scrapeFromPage);
                console.log('✅ تم إضافة مستمع لزر استخراج الصفحة');
            }

            console.log('✅ تم إضافة جميع مستمعي أحداث أزرار التحديث');
        }, 100);
    } else {
        console.error('❌ لم يتم العثور على عناصر الموديل');
        alert('خطأ: لم يتم العثور على عناصر الموديل. يرجى إعادة فتح الموديل.');
    }
}

function fetchFromDatabase() {
    console.log('🗄️ جلب البيانات من قاعدة البيانات');
    fetchModalDataFromDatabase();
}

async function fetchModalDataFromDatabase() {
    console.log('🗄️ جلب البيانات من قاعدة البيانات الحقيقية');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    try {
        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1];

        if (!schoolId) {
            throw new Error('لم يتم العثور على معرف المدرسة في URL');
        }

        console.log('🔍 البحث عن بيانات المدرسة:', schoolId);

        // إرسال طلب لقاعدة البيانات عبر background script
        const response = await chrome.runtime.sendMessage({
            action: 'fetchFromDatabase',
            schoolId: schoolId
        });

        console.log('📡 استجابة background script:', response);

        if (!response.success) {
            throw new Error(response.error || 'فشل في الاتصال بقاعدة البيانات');
        }

        const result = response.data;
        console.log('📊 استجابة قاعدة البيانات:', result);

        if (!result.success) {
            throw new Error(result.message || 'فشل في جلب البيانات من قاعدة البيانات');
        }

        // التحقق من وجود البيانات
        if (!result.data || !result.data.school) {
            throw new Error('لا توجد بيانات للمدرسة في قاعدة البيانات');
        }

        // التحقق من وجود البيانات
        if (!result.data || !result.data.school) {
            throw new Error('لا توجد بيانات للمدرسة في قاعدة البيانات');
        }

        // تحويل البيانات إلى التنسيق المطلوب
        const databaseData = convertDatabaseToKushoofFormat(result.data);

        if (databaseData) {
            // حفظ البيانات في window.name
            window.name = 'kushoof' + JSON.stringify(databaseData);
            displayDatabaseData(result.data, databaseData);
            console.log('✅ تم جلب البيانات من قاعدة البيانات بنجاح');
        } else {
            throw new Error('فشل في تحويل البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات من قاعدة البيانات:', error);
        displayModalError('فشل في جلب البيانات من قاعدة البيانات: ' + error.message);
    }
}

function convertDatabaseToKushoofFormat(dbData) {
    try {
        const school = dbData.school;
        const students = dbData.students || [];

        console.log('🔄 تحويل بيانات قاعدة البيانات:', {
            school: school?.school_name,
            studentsCount: students.length
        });

        // تحويل بيانات الطلاب
        const names = [];
        const phones = [];
        const classes = [];

        students.forEach(student => {
            names.push(encodeURIComponent(student.student_name || ''));
            phones.push(student.parent_phone || student.student_phone || '');
            classes.push(encodeURIComponent(student.class_label || student.grade || ''));
        });

        // إنشاء التاريخ
        const today = new Date();
        const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const dayName = arabicDays[today.getDay()];
        const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

        return {
            n: names,
            p: phones,
            c: classes,
            day: formattedDate,
            school_id: school?.school_id || 'unknown',
            school_name: school?.school_name || 'غير محدد',
            manager_name: school?.manager_name || 'غير محدد',
            ministry_number: school?.ministry_number || '',
            version: 'database',
            timestamp: new Date().toISOString(),
            source: 'database',
            total_students: students.length,
            extracted_at: school?.extracted_at,
            updated_at: school?.updated_at
        };

    } catch (error) {
        console.error('❌ خطأ في تحويل البيانات:', error);
        return null;
    }
}

function displayDatabaseData(rawData, convertedData) {
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إخفاء شاشة التحميل
    loadingDiv.style.display = 'none';
    contentDiv.style.display = 'block';

    const school = rawData.school;
    const students = rawData.students || [];

    // إحصائيات
    const totalStudents = students.length;
    const validPhones = students.filter(s => (s.parent_phone || s.student_phone || '').length >= 12).length;
    const invalidPhones = totalStudents - validPhones;

    // بناء HTML المحتوى
    let contentHTML = `
        <div class="kushoof-data-grid">
            <div class="kushoof-data-card">
                <h3>🏫 معلومات المدرسة</h3>
                <div style="font-size: 14px; line-height: 1.8;">
                    <div><strong>اسم المدرسة:</strong> ${school?.school_name || 'غير محدد'}</div>
                    <div><strong>مدير المدرسة:</strong> ${school?.manager_name || 'غير محدد'}</div>
                    <div><strong>رقم الوزارة:</strong> ${school?.ministry_number || 'غير محدد'}</div>
                    <div><strong>معرف المدرسة:</strong> ${school?.school_id || 'غير محدد'}</div>
                    <div><strong>تاريخ الاستخراج:</strong> ${school?.extracted_at || 'غير محدد'}</div>
                    <div><strong>آخر تحديث:</strong> ${school?.updated_at || 'غير محدد'}</div>
                </div>
            </div>

            <div class="kushoof-data-card">
                <h3>📊 إحصائيات الطلاب</h3>
                <div style="font-size: 16px; line-height: 1.8;">
                    <div>👥 إجمالي الطلاب: <strong style="color: #FFD700;">${totalStudents}</strong></div>
                    <div style="color: #4CAF50;">✅ أرقام صحيحة: <strong>${validPhones}</strong></div>
                    <div style="color: #f44336;">❌ أرقام خاطئة: <strong>${invalidPhones}</strong></div>
                    <div>📅 التاريخ: <strong>${convertedData.day}</strong></div>
                    <div>🗄️ المصدر: <strong>قاعدة البيانات</strong></div>
                </div>
            </div>
        </div>

        <div class="kushoof-data-card">
            <h3>👥 قائمة الطلاب من قاعدة البيانات</h3>
            <div style="max-height: 400px; overflow-y: auto;">
                <div style="display: grid; grid-template-columns: 40px 1fr 1fr 120px 60px; gap: 10px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px; margin-bottom: 10px; font-weight: bold;">
                    <div>#</div>
                    <div>اسم الطالب</div>
                    <div>رقم ولي الأمر</div>
                    <div>الصف</div>
                    <div>الحالة</div>
                </div>
    `;

    // إضافة قائمة الطلاب
    if (students.length > 0) {
        students.forEach((student, index) => {
            const name = student.student_name || 'غير محدد';
            const phone = student.parent_phone || student.student_phone || '';
            const className = student.class_label || student.grade || 'غير محدد';
            const isValid = phone.length >= 12;
            const statusIcon = isValid ? '✅' : '❌';
            const statusColor = isValid ? '#4CAF50' : '#f44336';

            contentHTML += `
                <div style="display: grid; grid-template-columns: 40px 1fr 1fr 120px 60px; gap: 10px; padding: 8px; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 13px;">
                    <div style="color: #FFD700;">${index + 1}</div>
                    <div style="font-weight: bold;">${name}</div>
                    <div style="color: ${statusColor}; font-family: monospace;">${phone}</div>
                    <div style="color: #87CEEB;">${className}</div>
                    <div style="color: ${statusColor}; text-align: center;">${statusIcon}</div>
                </div>
            `;
        });
    } else {
        contentHTML += '<div style="text-align: center; padding: 20px; opacity: 0.7;">لا توجد بيانات طلاب في قاعدة البيانات</div>';
    }

    contentHTML += `
            </div>
        </div>

        <div class="kushoof-data-card">
            <h3>🔧 معلومات تقنية</h3>
            <div style="font-size: 12px; line-height: 1.6; font-family: monospace;">
                <div>🌐 API: https://kushoofapp.com/js/api/receive-data.php</div>
                <div>📡 الطريقة: POST</div>
                <div>⏰ وقت الجلب: ${new Date().toLocaleString('ar-SA')}</div>
                <div>🔄 نوع البيانات: JSON من قاعدة البيانات</div>
                <div>📊 حجم البيانات: ${JSON.stringify(rawData).length} حرف</div>
            </div>
        </div>
    `;

    contentDiv.innerHTML = contentHTML;
    console.log('✅ تم عرض البيانات من قاعدة البيانات في الموديل');
}

function fetchFromWebsite() {
    console.log('🌐 جلب البيانات من الخادم');
    fetchModalDataFromServer();
}

function scrapeFromPage() {
    console.log('🔍 استخراج البيانات من الصفحة الحالية');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    try {
        // محاولة استخراج البيانات من عناصر الصفحة
        const scrapedData = extractDataFromPage();

        if (scrapedData && scrapedData.n && scrapedData.n.length > 0) {
            // حفظ البيانات المستخرجة
            window.name = 'kushoof' + JSON.stringify(scrapedData);
            displayModalData(scrapedData);
            console.log('✅ تم استخراج البيانات من الصفحة بنجاح');
        } else {
            throw new Error('لم يتم العثور على بيانات في الصفحة');
        }

    } catch (error) {
        console.error('❌ خطأ في استخراج البيانات من الصفحة:', error);
        displayModalError('فشل في استخراج البيانات من الصفحة: ' + error.message);
    }
}

function extractDataFromPage() {
    console.log('🔍 البحث عن بيانات الطلاب في الصفحة...');

    const students = [];
    const phones = [];
    const classes = [];

    // طريقة 1: البحث في الجداول
    const tables = document.querySelectorAll('table');
    for (const table of tables) {
        const rows = table.querySelectorAll('tr');

        for (const row of rows) {
            const cells = row.querySelectorAll('td, th');
            if (cells.length >= 2) {
                let studentName = '';
                let phoneNumber = '';
                let className = '';

                // البحث عن أسماء الطلاب وأرقام الهواتف
                for (const cell of cells) {
                    const text = cell.textContent.trim();

                    // فحص إذا كان النص يحتوي على رقم هاتف سعودي
                    if (/^(966|0)?[5][0-9]{8}$/.test(text.replace(/\s+/g, ''))) {
                        phoneNumber = text.replace(/\s+/g, '');
                        if (phoneNumber.startsWith('0')) {
                            phoneNumber = '966' + phoneNumber.substring(1);
                        } else if (!phoneNumber.startsWith('966')) {
                            phoneNumber = '966' + phoneNumber;
                        }
                    }

                    // فحص إذا كان النص يحتوي على اسم طالب (نص عربي)
                    if (/[\u0600-\u06FF]/.test(text) && text.length > 3 && text.length < 50) {
                        if (!text.includes('الصف') && !text.includes('المدرسة') && !text.includes('المعلم')) {
                            studentName = text;
                        }
                    }

                    // فحص إذا كان النص يحتوي على اسم صف
                    if (text.includes('الصف') || /^[0-9]+[أ-ي]$/.test(text)) {
                        className = text;
                    }
                }

                // إضافة البيانات إذا وجدت
                if (studentName && phoneNumber) {
                    students.push(studentName);
                    phones.push(phoneNumber);
                    classes.push(className || 'غير محدد');
                }
            }
        }
    }

    // طريقة 2: البحث في القوائم
    if (students.length === 0) {
        const lists = document.querySelectorAll('ul, ol, div[class*="student"], div[class*="list"]');

        for (const list of lists) {
            const items = list.querySelectorAll('li, div, span');

            for (const item of items) {
                const text = item.textContent.trim();

                // البحث عن أنماط مختلفة للبيانات
                const phoneMatch = text.match(/(?:966|0)?[5][0-9]{8}/);
                const nameMatch = text.match(/[\u0600-\u06FF\s]{3,30}/);

                if (phoneMatch && nameMatch) {
                    let phone = phoneMatch[0].replace(/\s+/g, '');
                    if (phone.startsWith('0')) {
                        phone = '966' + phone.substring(1);
                    } else if (!phone.startsWith('966')) {
                        phone = '966' + phone;
                    }

                    students.push(nameMatch[0].trim());
                    phones.push(phone);
                    classes.push('مستخرج من الصفحة');
                }
            }
        }
    }

    // طريقة 3: البحث في النصوص العامة
    if (students.length === 0) {
        const textElements = document.querySelectorAll('p, div, span');

        for (const element of textElements) {
            const text = element.textContent.trim();

            // البحث عن نمط: اسم + رقم هاتف
            const pattern = /([\u0600-\u06FF\s]{3,30})\s*[:\-]?\s*((?:966|0)?[5][0-9]{8})/g;
            let match;

            while ((match = pattern.exec(text)) !== null) {
                let phone = match[2].replace(/\s+/g, '');
                if (phone.startsWith('0')) {
                    phone = '966' + phone.substring(1);
                } else if (!phone.startsWith('966')) {
                    phone = '966' + phone;
                }

                students.push(match[1].trim());
                phones.push(phone);
                classes.push('مستخرج من النص');
            }
        }
    }

    // إنشاء كائن البيانات
    if (students.length > 0) {
        const today = new Date();
        const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const dayName = arabicDays[today.getDay()];
        const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1] || 'extracted_from_page';

        return {
            n: students.map(name => encodeURIComponent(name)),
            p: phones,
            c: classes.map(cls => encodeURIComponent(cls)),
            day: formattedDate,
            school_id: schoolId,
            version: 'scraped',
            timestamp: new Date().toISOString(),
            source: 'page_extraction',
            extracted_count: students.length
        };
    }

    console.log('📊 نتائج الاستخراج:', {
        students: students.length,
        phones: phones.length,
        classes: classes.length
    });

    return null;
}

function exportModalData() {
    console.log('📥 تصدير بيانات الموديل');

    try {
        let data = null;
        if (window.name.includes('kushoof')) {
            data = JSON.parse(window.name.split('kushoof')[1]);
        }

        if (data) {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `kushoof_data_${data.school_id || 'unknown'}_${new Date().getTime()}.json`;
            a.click();

            URL.revokeObjectURL(url);
            console.log('✅ تم تصدير البيانات');
        } else {
            alert('لا توجد بيانات للتصدير');
        }
    } catch (error) {
        console.error('❌ خطأ في تصدير البيانات:', error);
        alert('خطأ في تصدير البيانات');
    }
}

// مراقبة DOM لمنع اختفاء الواجهة
let domWatcher = null;
let kushoofInterfaceData = null;

function startDOMWatcher() {
    console.log('👁️ بدء مراقبة DOM لحماية واجهة Kushoof');

    // حفظ بيانات الواجهة
    if (window.name.includes('kushoof')) {
        try {
            kushoofInterfaceData = JSON.parse(window.name.split('kushoof')[1]);
        } catch (e) {
            console.warn('⚠️ فشل في حفظ بيانات الواجهة');
        }
    }

    // إنشاء MutationObserver
    domWatcher = new MutationObserver((mutations) => {
        let interfaceRemoved = false;

        mutations.forEach((mutation) => {
            // فحص العقد المحذوفة
            mutation.removedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // فحص إذا كانت واجهة Kushoof محذوفة
                    if (node.classList && node.classList.contains('kushoof-interface')) {
                        interfaceRemoved = true;
                        console.log('⚠️ تم حذف واجهة Kushoof - جاري الاستعادة...');
                    }

                    // فحص إذا كان العنصر المحذوف يحتوي على واجهة Kushoof
                    if (node.querySelector && node.querySelector('.kushoof-interface')) {
                        interfaceRemoved = true;
                        console.log('⚠️ تم حذف عنصر يحتوي على واجهة Kushoof - جاري الاستعادة...');
                    }
                }
            });
        });

        // إعادة حقن الواجهة إذا تم حذفها
        if (interfaceRemoved) {
            setTimeout(() => {
                restoreKushoofInterface();
            }, 500);
        }
    });

    // بدء المراقبة
    domWatcher.observe(document.body, {
        childList: true,
        subtree: true
    });
}

function restoreKushoofInterface() {
    // فحص إذا كانت الواجهة موجودة بالفعل
    if (document.querySelector('.kushoof-interface')) {
        console.log('✅ واجهة Kushoof موجودة بالفعل');
        return;
    }

    console.log('🔄 استعادة واجهة Kushoof...');

    // ضمان وجود الدوال في النطاق العام
    ensureGlobalFunctions();

    if (kushoofInterfaceData) {
        // إعادة حقن الواجهة
        const targetElement = findTargetElement();
        if (targetElement) {
            injectKushoofInterface(targetElement);
            console.log('✅ تم استعادة واجهة Kushoof بنجاح');
        }
    } else {
        console.warn('⚠️ لا توجد بيانات محفوظة لاستعادة الواجهة');
    }
}

function findTargetElement() {
    // البحث عن عنصر مناسب لحقن الواجهة
    const alternatives = [
        document.querySelector('.navbar'),
        document.querySelector('.header'),
        document.querySelector('#header'),
        document.querySelector('nav'),
        document.querySelector('.top-bar'),
        document.querySelector('body > div:first-child'),
        document.body
    ];

    for (const element of alternatives) {
        if (element) {
            return element;
        }
    }

    return null;
}

// فحص دوري للتأكد من بقاء الواجهة
let periodicChecker = null;

function startPeriodicCheck() {
    console.log('⏰ بدء الفحص الدوري لواجهة Kushoof');

    periodicChecker = setInterval(() => {
        const interface = document.querySelector('.kushoof-interface');

        if (!interface) {
            console.log('⚠️ الواجهة مفقودة - جاري الاستعادة...');
            restoreKushoofInterface();
        }
    }, 3000); // فحص كل 3 ثوان
}

function stopPeriodicCheck() {
    if (periodicChecker) {
        clearInterval(periodicChecker);
        periodicChecker = null;
        console.log('🛑 تم إيقاف الفحص الدوري');
    }
}

// إيقاف مراقبة DOM عند الحاجة
function stopDOMWatcher() {
    if (domWatcher) {
        domWatcher.disconnect();
        domWatcher = null;
        console.log('🛑 تم إيقاف مراقبة DOM');
    }
}

// بدء فحص الموقع عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        ensureGlobalFunctions();
        checkCurrentSite();
    });
} else {
    ensureGlobalFunctions();
    checkCurrentSite();
}

// مراقبة تغييرات URL
let currentURL = window.location.href;
setInterval(() => {
    if (window.location.href !== currentURL) {
        currentURL = window.location.href;
        console.log('🔄 تغيير URL:', currentURL);
        ensureGlobalFunctions(); // ضمان الدوال عند تغيير URL
        checkCurrentSite();
    }

    // ضمان الدوال كل 5 ثوان كإجراء احتياطي
    if (Math.random() < 0.2) { // 20% احتمال كل ثانية = مرة كل 5 ثوان تقريباً
        ensureGlobalFunctions();
    }
}, 1000);
