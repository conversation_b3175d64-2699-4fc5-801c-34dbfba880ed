// Background Script - Kushoof Extension
// يتعامل مع جلب وحقن السكريپت من الخادم

console.log('🚀 Kushoof Background Script تم تحميله');

// استقبال الرسائل من Content Script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 تم استقبال رسالة:', request.action);

    if (request.action === 'fetchAndInjectScript') {
        handleScriptFetch(request, sender.tab.id, sendResponse);
        return true; // للاستجابة غير المتزامنة
    }

    if (request.action === 'fetchFromDatabase') {
        handleDatabaseFetch(request, sendResponse);
        return true; // للاستجابة غير المتزامنة
    }

    if (request.action === 'saveToDatabase') {
        handleDatabaseSave(request, sendResponse);
        return true; // للاستجابة غير المتزامنة
    }
});

// دالة جلب وحقن السكريپت
async function handleScriptFetch(request, tabId, sendResponse) {
    try {
        console.log('📥 جلب السكريپت من:', request.url);
        
        // جلب السكريپت من الخادم
        const response = await fetch(request.url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const scriptContent = await response.text();
        console.log('✅ تم جلب السكريپت بنجاح، الحجم:', scriptContent.length, 'حرف');
        
        // حقن السكريپت في التبويب باستخدام chrome.scripting
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: executeKushoofScript,
            args: [scriptContent, request.schoolId]
        });
        
        console.log('✅ تم حقن السكريپت بنجاح في التبويب');
        sendResponse({ success: true, message: 'تم حقن السكريپت بنجاح' });
        
    } catch (error) {
        console.error('❌ خطأ في جلب/حقن السكريپت:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// دالة جلب البيانات من قاعدة البيانات
async function handleDatabaseFetch(request, sendResponse) {
    try {
        console.log('🗄️ جلب البيانات من قاعدة البيانات للمدرسة:', request.schoolId);

        // إرسال طلب لقاعدة البيانات
        const response = await fetch('https://kushoofapp.com/js/api/receive-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'load',
                schoolId: request.schoolId
            })
        });

        console.log('📡 حالة الاستجابة:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📊 استجابة قاعدة البيانات:', result);

        sendResponse({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات من قاعدة البيانات:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

// دالة حفظ البيانات في قاعدة البيانات
async function handleDatabaseSave(request, sendResponse) {
    try {
        console.log('💾 حفظ البيانات في قاعدة البيانات:', request.schoolData.schoolId);

        // تحضير بيانات الطلاب بالتنسيق الصحيح
        const studentsData = (request.schoolData.students || []).map(student => ({
            name: student.name || '',
            national_id: student.nationalId || '',
            student_phone: student.studentPhone || '',
            parent_phone: student.parentPhone || '',
            username: student.username || '',
            class_label: student.classLabel || '',
            grade: student.grade || '',
            grade_level: student.grade || '',
            section: student.section || ''
        }));

        // تحضير البيانات بالتنسيق المطلوب من c1.js
        const requestBody = {
            type: 'SCHOOL_DATA',
            action: 'force_save',
            schoolId: request.schoolData.schoolId,
            schoolName: request.schoolData.schoolName,
            managerName: request.schoolData.managerName,
            ministryNumber: request.schoolData.ministryNumber,
            academicYear: request.schoolData.academicYear,
            semester: request.schoolData.semester,
            studentsCount: studentsData.length,
            students: studentsData,
            force_update: true,
            source: 'kushoof_modal_extension',
            timestamp: new Date().toISOString(),
            debug_info: {
                total_students: studentsData.length,
                has_national_id: studentsData.filter(s => s.national_id).length,
                has_class_label: studentsData.filter(s => s.class_label).length,
                has_student_phone: studentsData.filter(s => s.student_phone).length
            }
        };

        console.log('📤 إرسال البيانات بالتنسيق الصحيح:', {
            schoolId: requestBody.schoolId,
            schoolName: requestBody.schoolName,
            studentsCount: requestBody.studentsCount
        });

        // إرسال طلب حفظ البيانات
        const response = await fetch('https://kushoofapp.com/js/api/receive-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Force-Save': 'true'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('📡 حالة استجابة الحفظ:', response.status, response.statusText);

        if (!response.ok) {
            // محاولة قراءة رسالة الخطأ من الخادم
            let errorMessage = `HTTP error! status: ${response.status}`;
            try {
                const errorText = await response.text();
                if (errorText) {
                    console.log('📄 رسالة خطأ الخادم:', errorText);
                    errorMessage += ` - ${errorText}`;
                }
            } catch (e) {
                console.log('⚠️ لا يمكن قراءة رسالة الخطأ');
            }
            throw new Error(errorMessage);
        }

        const result = await response.json();
        console.log('📊 استجابة حفظ البيانات:', result);

        // التحقق من نجاح العملية
        const isSuccess = result && (result.success === true || result.status === 'success');

        sendResponse({
            success: isSuccess,
            data: result,
            message: result?.message || (isSuccess ? 'تم الحفظ بنجاح' : 'فشل في الحفظ'),
            details: result
        });

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

// دالة تنفيذ السكريپت في التبويب (تعمل في main world)
function executeKushoofScript(scriptContent, schoolId) {
    console.log('🎯 تنفيذ سكريپت Kushoof في main world');
    console.log('🏫 معرف المدرسة:', schoolId);
    
    try {
        // تنفيذ السكريپت مباشرة (يعمل في main world)
        eval(scriptContent);
        console.log('✅ تم تنفيذ سكريپت Kushoof بنجاح');
        
        // إشعار Content Script بالنجاح
        window.postMessage({
            type: 'KUSHOOF_SCRIPT_LOADED',
            success: true,
            schoolId: schoolId
        }, '*');
        
    } catch (error) {
        console.error('❌ خطأ في تنفيذ السكريپت:', error);
        
        // إشعار Content Script بالفشل
        window.postMessage({
            type: 'KUSHOOF_SCRIPT_LOADED',
            success: false,
            error: error.message
        }, '*');
    }
}
