// Content Script - إضا<PERSON><PERSON> Kushoof
// يتم حقن هذا الملف في صفحات الويب المحددة

console.log('🚀 Kushoof Content Script تم تحميله');

// دالة مساعدة للتحقق من القيم
const nullthrows = (v) => {
    if (v == null) throw new Error("it's a null");
    return v;
}

// دالة حقن كود JavaScript
function injectCode(src) {
    const script = document.createElement('script');
    script.src = src;
    script.onload = function() { this.remove(); };
    nullthrows(document.head || document.documentElement).appendChild(script);
}

// فحص الموقع الحالي وتحديد الإجراء المناسب
function checkCurrentSite() {
    const currentURL = window.location.href;
    console.log('🔍 فحص الموقع الحالي:', currentURL);
    
    // فحص موقع مدرستي (النسخة الجديدة)
    if (currentURL.substring(0, 63) === 'https://schools.madrasati.sa/SchoolManagment/Teachers?SchoolId=') {
        console.log('✅ تم اكتشاف موقع مدرستي (النسخة الجديدة)');
        handleMadrasatiSite();
    }
    
    // فحص موقع مدرستي (النسخة التجريبية)
    else if (currentURL.substring(0, 44) === 'https://beta.madrasati.sa/Teachers?SchoolId=') {
        console.log('✅ تم اكتشاف موقع مدرستي (النسخة التجريبية)');
        handleBetaMadrasatiSite();
    }
    
    // فحص موقع نور
    else if (currentURL === "https://noor.moe.gov.sa/Noor/EduWavek12Portal/HomePage.aspx") {
        console.log('✅ تم اكتشاف موقع نور');
        handleNoorSite();
    }
    
    // فحص WhatsApp Web
    else if (currentURL.substring(0, 24) === "https://web.whatsapp.com") {
        console.log('✅ تم اكتشاف WhatsApp Web');
        handleWhatsAppSite();
    }
    
    else {
        console.log('ℹ️ موقع غير مدعوم:', currentURL);
    }
}

// التعامل مع موقع مدرستي (النسخة الجديدة)
function handleMadrasatiSite() {
    // استخراج معرف المدرسة من URL
    const urlParts = window.location.href.split("=");
    const schoolId = urlParts[1];

    console.log('🏫 معرف المدرسة:', schoolId);

    // جلب السكريپت باستخدام fetch (يتجاوز CSP)
    const scriptUrl = `https://kushoofapp.com/java/js.php?version=258&id=${schoolId}&k=K`;
    console.log('📥 جلب السكريپت:', scriptUrl);

    // جلب البيانات مباشرة وبناء الواجهة محلياً
    fetchDataAndBuildInterface(scriptUrl, schoolId);
}

// التعامل مع موقع مدرستي (النسخة التجريبية)
function handleBetaMadrasatiSite() {
    const urlParts = window.location.href.split("=");
    const schoolId = urlParts[1];

    console.log('🏫 معرف المدرسة (بيتا):', schoolId);

    const scriptUrl = `https://kushoofapp.com/java/js.php?version=280&id=${schoolId}&k=K`;
    console.log('📥 جلب السكريپت (بيتا):', scriptUrl);

    // جلب البيانات مباشرة وبناء الواجهة محلياً
    fetchDataAndBuildInterface(scriptUrl, schoolId);
}

// التعامل مع موقع نور
function handleNoorSite() {
    console.log('🎓 جلب سكريپت نور');
    // استخدام Background Script لجلب وحقن سكريپت نور
    fetchScriptViaBackground(`https://kushoofapp.com/java/noor.php?k=vir3`, 'noor');
}

// التعامل مع WhatsApp Web
function handleWhatsAppSite() {
    console.log('💬 معالجة WhatsApp Web');
    
    // فحص إذا كان هناك بيانات في URL
    const urlSegment = window.location.href.substring(26, 100);
    
    if (urlSegment.length > 30 && urlSegment.indexOf("=") < 0) {
        console.log('🔄 جلب البيانات من kushoofapp.com');
        
        // جلب البيانات من الخادم
        fetch('https://kushoofapp.com/java/g.php?s=' + urlSegment)
            .then(response => response.text())
            .then(data => {
                console.log('📊 تم استلام البيانات:', data);
                window.name = data;
                window.location.href = 'https://web.whatsapp.com';
            })
            .catch(error => {
                console.error('❌ خطأ في جلب البيانات:', error);
            });
    }
    
    // تهيئة واجهة WhatsApp
    initializeWhatsAppInterface();
}

// دالة جلب البيانات وبناء الواجهة محلياً (الحل النهائي)
async function fetchDataAndBuildInterface(url, schoolId) {
    console.log('📊 جلب البيانات من:', url);

    try {
        // جلب البيانات من الخادم
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const scriptContent = await response.text();
        console.log('📥 تم جلب البيانات بنجاح، الحجم:', scriptContent.length, 'حرف');

        // استخراج البيانات من السكريپت
        const studentsData = extractDataFromScript(scriptContent);

        if (studentsData) {
            console.log('✅ تم استخراج بيانات الطلاب:', studentsData);

            // تخزين البيانات في window.name للاستخدام لاحقاً
            window.name = 'kushoof' + JSON.stringify(studentsData);

            // بدء تهيئة الواجهة
            initializeKushoofInterface();
        } else {
            console.error('❌ فشل في استخراج البيانات من السكريپت');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error);

        // إنشاء بيانات تجريبية في حالة الفشل
        const fallbackData = createFallbackData(schoolId);
        window.name = 'kushoof' + JSON.stringify(fallbackData);
        console.log('🔄 استخدام بيانات تجريبية');
        initializeKushoofInterface();
    }
}

// دالة استخراج البيانات من السكريپت
function extractDataFromScript(scriptContent) {
    try {
        console.log('🔍 البحث عن بيانات kushoof_data في السكريپت...');

        // طريقة 1: البحث عن kushoof_data مع الأقواس المجعدة
        let dataMatch = scriptContent.match(/var kushoof_data\s*=\s*({[\s\S]*?});/);

        if (!dataMatch) {
            // طريقة 2: البحث بنمط أوسع
            dataMatch = scriptContent.match(/kushoof_data\s*=\s*({[\s\S]*?});/);
        }

        if (dataMatch) {
            console.log('✅ تم العثور على kushoof_data');

            // تنظيف وتحليل البيانات
            let dataString = dataMatch[1];

            // تنظيف البيانات
            dataString = dataString
                .replace(/'/g, '"')  // استبدال الأقواس المفردة
                .replace(/,\s*}/g, '}')  // إزالة الفواصل الزائدة
                .replace(/,\s*]/g, ']'); // إزالة الفواصل الزائدة في المصفوفات

            console.log('📝 البيانات المستخرجة:', dataString.substring(0, 200) + '...');

            // تحليل JSON
            const data = JSON.parse(dataString);

            // فك تشفير أسماء الطلاب إذا كانت مشفرة
            if (data.n && Array.isArray(data.n)) {
                data.n = data.n.map(name => {
                    try {
                        return decodeURIComponent(name);
                    } catch (e) {
                        return name; // إذا فشل فك التشفير، استخدم الاسم كما هو
                    }
                });
            }

            // فك تشفير أسماء الفصول إذا وجدت
            if (data.c && Array.isArray(data.c)) {
                data.c = data.c.map(className => {
                    try {
                        return decodeURIComponent(className);
                    } catch (e) {
                        return className;
                    }
                });
            }

            console.log('✅ تم استخراج البيانات بنجاح:', {
                students: data.n ? data.n.length : 0,
                phones: data.p ? data.p.length : 0,
                school_id: data.school_id
            });

            return data;
        }

        // إذا لم نجد البيانات، نحاول استخراج معلومات أساسية
        console.warn('⚠️ لم يتم العثور على kushoof_data، محاولة استخراج بديل');

        // البحث عن أي بيانات طلاب في السكريپت
        const namesMatch = scriptContent.match(/'n':\s*\[(.*?)\]/);
        const phonesMatch = scriptContent.match(/'p':\s*\[(.*?)\]/);

        if (namesMatch && phonesMatch) {
            console.log('✅ تم العثور على بيانات بديلة');

            const names = namesMatch[1].split(',').map(name =>
                name.trim().replace(/['"]/g, '')
            );
            const phones = phonesMatch[1].split(',').map(phone =>
                phone.trim().replace(/['"]/g, '')
            );

            return {
                n: names,
                p: phones,
                day: 'اليوم',
                school_id: 'extracted',
                version: '258'
            };
        }

        return null;

    } catch (error) {
        console.error('❌ خطأ في استخراج البيانات:', error);
        return null;
    }
}

// دالة إنشاء بيانات تجريبية
function createFallbackData(schoolId) {
    const today = new Date();
    const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = arabicDays[today.getDay()];
    const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

    return {
        n: ['أحمد محمد العلي', 'فاطمة سالم الأحمد', 'محمد عبدالله الحسن', 'نورا علي السالم'],
        p: ['966501234567', '966507654321', '966509876543', '966512345678'],
        day: formattedDate,
        school_id: schoolId,
        version: '258',
        timestamp: new Date().toISOString(),
        fallback: true
    };
}

// دالة جلب السكريپت عبر Background Script (احتياطية)
function fetchScriptViaBackground(url, schoolId) {
    console.log('📨 إرسال طلب لـ Background Script:', url);

    // إرسال رسالة لـ Background Script
    chrome.runtime.sendMessage({
        action: 'fetchAndInjectScript',
        url: url,
        schoolId: schoolId
    }, (response) => {
        if (chrome.runtime.lastError) {
            console.error('❌ خطأ في التواصل مع Background Script:', chrome.runtime.lastError);
            return;
        }

        if (response && response.success) {
            console.log('✅ تم إرسال الطلب بنجاح لـ Background Script');
        } else {
            console.error('❌ فشل في معالجة الطلب:', response?.error);
        }
    });
}

// استقبال إشعارات من main world
window.addEventListener('message', (event) => {
    if (event.data.type === 'KUSHOOF_SCRIPT_LOADED') {
        if (event.data.success) {
            console.log('✅ تم تحميل سكريپت Kushoof بنجاح في main world');
            console.log('🏫 معرف المدرسة:', event.data.schoolId);

            // بدء تهيئة الواجهة بعد تأخير قصير
            setTimeout(() => {
                initializeKushoofInterface();
            }, 1000);
        } else {
            console.error('❌ فشل في تحميل سكريپت Kushoof:', event.data.error);
        }
    }
});

// دالة جلب وتنفيذ السكريپت (احتياطية - لن تستخدم الآن)
async function fetchAndExecuteScript(url) {
    console.log('📜 جلب سكريپت:', url);

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const scriptContent = await response.text();
        console.log('📥 تم جلب السكريپت بنجاح، الحجم:', scriptContent.length, 'حرف');

        // تنفيذ السكريپت باستخدام طرق متعددة (تتجاوز CSP)
        return await injectScriptIntoMainWorld(scriptContent);
        return true;

    } catch (error) {
        console.error('❌ خطأ في جلب السكريپت:', error);
        throw error;
    }
}

// دالة حقن السكريپت في main world (تتجاوز CSP)
async function injectScriptIntoMainWorld(scriptContent) {
    console.log('🔧 محاولة حقن السكريپت بطرق متعددة...');

    // طريقة 1: استخدام data URL
    try {
        const dataUrl = 'data:text/javascript;charset=utf-8,' + encodeURIComponent(scriptContent);
        const script = document.createElement('script');
        script.src = dataUrl;

        return new Promise((resolve, reject) => {
            script.onload = () => {
                console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام data URL');
                script.remove();
                resolve();
            };
            script.onerror = () => {
                console.warn('⚠️ فشل data URL، جاري المحاولة بطريقة أخرى');
                script.remove();

                // طريقة 2: استخدام Function constructor
                try {
                    const executeScript = new Function(scriptContent);
                    executeScript();
                    console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام Function()');
                    resolve();
                } catch (funcError) {
                    console.warn('⚠️ فشل Function()، جاري المحاولة بـ eval()');
                    try {
                        // طريقة 3: استخدام eval كبديل أخير
                        eval(scriptContent);
                        console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام eval()');
                        resolve();
                    } catch (evalError) {
                        console.error('❌ فشل في جميع طرق تنفيذ السكريپت:', evalError);
                        reject(evalError);
                    }
                }
            };

            document.head.appendChild(script);
        });

    } catch (error) {
        console.error('❌ خطأ في إنشاء data URL:', error);

        // طريقة احتياطية مباشرة
        try {
            const executeScript = new Function(scriptContent);
            executeScript();
            console.log('✅ تم تنفيذ السكريپت بنجاح باستخدام Function() (احتياطي)');
            return Promise.resolve();
        } catch (funcError) {
            console.error('❌ فشل في الطريقة الاحتياطية:', funcError);
            return Promise.reject(funcError);
        }
    }
}

// دالة تحميل السكريپت من الخادم (احتياطية)
function loadScript(url, callback) {
    console.log('📜 تحميل سكريپت:', url);

    const head = document.getElementsByTagName('head')[0];
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onreadystatechange = callback;
    script.onload = callback;
    script.onerror = function() {
        console.error('❌ فشل في تحميل السكريپت:', url);
    };
    head.appendChild(script);
}

// تهيئة واجهة Kushoof في موقع مدرستي
function initializeKushoofInterface() {
    console.log('🎨 تهيئة واجهة Kushoof');
    
    // البحث عن header أو عناصر بديلة
    function findHeader() {
        // البحث عن header أولاً
        let targetElement = document.getElementsByTagName("header")[0];

        // إذا لم يوجد header، ابحث عن عناصر بديلة
        if (!targetElement) {
            const alternatives = [
                document.querySelector('.navbar'),
                document.querySelector('.header'),
                document.querySelector('#header'),
                document.querySelector('nav'),
                document.querySelector('.top-bar'),
                document.querySelector('body > div:first-child'),
                document.body
            ];

            for (const element of alternatives) {
                if (element) {
                    targetElement = element;
                    console.log('✅ تم العثور على عنصر بديل:', element.tagName, element.className);
                    break;
                }
            }
        } else {
            console.log('✅ تم العثور على header');
        }

        if (targetElement) {
            injectKushoofInterface(targetElement);
        } else {
            console.log('⏳ البحث عن عناصر مناسبة...');
            setTimeout(findHeader, 2000);
        }
    }
    
    findHeader();
}

// حقن واجهة Kushoof
function injectKushoofInterface(targetElement) {
    console.log('💉 حقن واجهة Kushoof في:', targetElement ? targetElement.tagName : 'عنصر غير محدد');

    // حقن CSS
    injectCSS();

    // استخراج البيانات من window.name
    let studentsData;
    try {
        if (window.name.includes('kushoof')) {
            studentsData = JSON.parse(window.name.split('kushoof')[1]);
            console.log('📊 تم استخراج بيانات الطلاب:', studentsData);
        } else {
            console.warn('⚠️ لم يتم العثور على بيانات kushoof في window.name');
            // إنشاء بيانات تجريبية للاختبار
            studentsData = {
                n: ['طالب تجريبي 1', 'طالب تجريبي 2', 'طالب تجريبي 3'],
                p: ['966501234567', '966507654321', '966509876543'],
                day: 'اليوم التجريبي',
                school_id: 'test_school',
                fallback: true
            };
            console.log('🔄 استخدام بيانات تجريبية محلية');
        }
    } catch (error) {
        console.error('❌ خطأ في استخراج البيانات:', error);
        return;
    }

    // بناء HTML الواجهة
    const interfaceHTML = buildKushoofInterface(studentsData);

    // حقن الواجهة في العنصر المستهدف
    if (targetElement) {
        // إزالة أي واجهة موجودة مسبقاً
        const existingInterface = document.querySelector('.kushoof-interface');
        if (existingInterface) {
            existingInterface.remove();
        }

        if (targetElement.tagName.toLowerCase() === 'header') {
            // استبدال header كاملاً
            targetElement.outerHTML = interfaceHTML;
            console.log('✅ تم استبدال header بواجهة Kushoof');
        } else {
            // إضافة الواجهة في أعلى العنصر مع معرف فريد
            const kushoofContainer = document.createElement('div');
            kushoofContainer.id = 'kushoof-container-' + Date.now();
            kushoofContainer.innerHTML = interfaceHTML;
            kushoofContainer.style.cssText = 'position: relative; z-index: 10000; width: 100%;';

            targetElement.insertBefore(kushoofContainer, targetElement.firstChild);
            console.log('✅ تم إضافة واجهة Kushoof في أعلى الصفحة');
        }

        // إضافة مستمعي الأحداث
        setTimeout(() => {
            addEventListeners();

            // بدء مراقبة DOM لمنع اختفاء الواجهة
            startDOMWatcher();

            // فحص دوري إضافي للتأكد من بقاء الواجهة
            startPeriodicCheck();
        }, 100);
    } else {
        console.error('❌ لم يتم العثور على عنصر مناسب لحقن الواجهة');
    }
}

// حقن CSS
function injectCSS() {
    const style = document.createElement('style');
    style.textContent = `
        .kushoof-btn {
            font-size: 18px;
            width: 80%;
            border-radius: 5px;
            height: 30px;
            background: #4682b4;
            color: white;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        
        .kushoof-option {
            font-weight: bold;
            font-size: 16px;
            color: #006400;
        }
        
        .kushoof-interface {
            background: linear-gradient(135deg, #128c7e 0%, #075e54 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            position: relative !important;
            z-index: 9999 !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 200px;
            display: block !important;
            visibility: visible !important;
        }

        /* Modal Styles */
        .kushoof-modal {
            display: none;
            position: fixed;
            z-index: 99999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .kushoof-modal-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .kushoof-modal-header {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .kushoof-modal-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }

        .kushoof-modal-close {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
        }

        .kushoof-modal-close:hover {
            background: rgba(255,255,255,0.3);
            transform: rotate(90deg);
        }

        .kushoof-modal-body {
            padding: 20px;
            color: white;
            max-height: 60vh;
            overflow-y: auto;
        }

        .kushoof-data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .kushoof-data-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .kushoof-data-card h3 {
            margin: 0 0 15px 0;
            color: #FFD700;
            font-size: 18px;
        }

        .kushoof-student-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 14px;
        }

        .kushoof-student-item:last-child {
            border-bottom: none;
        }

        .kushoof-student-name {
            flex: 1;
            font-weight: bold;
        }

        .kushoof-student-phone {
            flex: 1;
            text-align: center;
            font-family: monospace;
        }

        .kushoof-student-status {
            width: 60px;
            text-align: center;
        }

        .kushoof-refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s;
        }

        .kushoof-refresh-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
    `;
    document.head.appendChild(style);
    console.log('🎨 تم حقن CSS');
}

// بناء HTML واجهة Kushoof
function buildKushoofInterface(data) {
    if (!data) return '';
    
    let optionsHTML = '';
    for (let i = 0; i < data.n.length; i++) {
        const style = data.p[i].length < 12 ? 'style="color:red"' : '';
        optionsHTML += `
            <option class="kushoof-option" ${style} value="${data.p[i]}">
                ${i + 1} - ${decodeURIComponent(data.n[i])}
            </option>
        `;
    }
    
    // معلومات إضافية
    const totalStudents = data.n ? data.n.length : 0;
    const validPhones = data.p ? data.p.filter(phone => phone.length >= 12).length : 0;
    const invalidPhones = totalStudents - validPhones;
    const dataSource = data.fallback ? '(بيانات تجريبية)' : '(بيانات حقيقية)';

    return `
        <div class="kushoof-interface">
            <a id="kushoof-link" href="" style="visibility:hidden"></a>
            <h2>🚀 Kushoof WhatsApp</h2>
            <div style="font-size: 12px; opacity: 0.8; margin-bottom: 10px;">
                📅 ${data.day || 'اليوم'} | 🏫 ${data.school_id || 'غير محدد'} ${dataSource}
            </div>
            <hr style="width:70%;">

            <div style="margin: 15px 0; font-size: 13px;">
                <span style="color: #4CAF50;">✅ صحيح: ${validPhones}</span> |
                <span style="color: #f44336;">❌ خطأ: ${invalidPhones}</span> |
                <span style="color: #2196F3;">📊 المجموع: ${totalStudents}</span>
            </div>

            <div style="margin: 20px 0;">
                <button id="start-btn" class="kushoof-btn">بدء الإرسال</button>
                <button id="stop-btn" class="kushoof-btn" style="background:#dc143c;">إيقاف الإرسال</button>
                <button id="whatsapp-btn" class="kushoof-btn" style="background:#25D366;">📱 فتح WhatsApp</button>
                <button id="modal-btn" class="kushoof-btn" style="background:#FF9800;">📊 عرض البيانات</button>
            </div>

            <div>
                <h3>قائمة بيانات الإرسال</h3>
                <select id="students-select" size="4" style="width:90%; padding:10px; font-size:12px;">
                    ${optionsHTML}
                </select>
            </div>

            <div style="margin-top: 20px;">
                <textarea id="message-text" rows="3" style="width:90%; padding:10px; font-size:12px;"
                          placeholder="اكتب نص الرسالة هنا...">عزيزي ولي أمر الطالب، نود إعلامكم بأن الطالب غائب اليوم.</textarea>
                <br>
                <input id="signature-text" style="width:90%; padding:8px; margin-top:10px; font-size:12px;"
                       placeholder="تذييل الرسالة" value="مع تحيات إدارة المدرسة">
            </div>

            <div style="margin-top: 15px; font-size: 11px; opacity: 0.7;">
                💡 اضغط Enter على اسم الطالب للإرسال السريع
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    console.log('🎧 إضافة مستمعي الأحداث');

    // ضمان وجود الدوال في النطاق العام
    ensureGlobalFunctions();

    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    const whatsappBtn = document.getElementById('whatsapp-btn');
    const modalBtn = document.getElementById('modal-btn');
    const studentsSelect = document.getElementById('students-select');

    if (startBtn) {
        startBtn.addEventListener('click', startSending);
    }

    if (stopBtn) {
        stopBtn.addEventListener('click', stopSending);
    }

    if (whatsappBtn) {
        whatsappBtn.addEventListener('click', openWhatsApp);
    }

    if (modalBtn) {
        modalBtn.addEventListener('click', openDataModal);
    }

    if (studentsSelect) {
        studentsSelect.addEventListener('change', onStudentSelect);
    }
    
    // مستمع Enter للإرسال السريع
    document.addEventListener('keypress', function(event) {
        if (event.key === 'Enter' && document.activeElement.id === 'students-select') {
            sendQuickMessage();
        }
    });
}

// تهيئة واجهة WhatsApp
function initializeWhatsAppInterface() {
    console.log('💬 تهيئة واجهة WhatsApp');
    
    // فحص وجود البيانات في window.name
    if (window.name.includes('kushoof')) {
        console.log('📊 تم العثور على بيانات في window.name');
        setupWhatsAppInterface();
    }
}

// إعداد واجهة WhatsApp
function setupWhatsAppInterface() {
    // البحث عن header في WhatsApp
    function findWhatsAppHeader() {
        const headers = document.getElementsByTagName("header");
        
        if (headers.length > 0) {
            console.log('✅ تم العثور على header في WhatsApp');
            injectWhatsAppControls();
        } else {
            console.log('⏳ البحث عن header في WhatsApp...');
            setTimeout(findWhatsAppHeader, 2000);
        }
    }
    
    findWhatsAppHeader();
}

// حقن أدوات التحكم في WhatsApp
function injectWhatsAppControls() {
    console.log('💉 حقن أدوات التحكم في WhatsApp');
    // هنا يتم حقن الواجهة المخصصة في WhatsApp
}

// وظائف التحكم
function startSending() {
    console.log('▶️ بدء الإرسال');
    // تنفيذ منطق الإرسال
}

function stopSending() {
    console.log('⏹️ إيقاف الإرسال');
    // إيقاف عملية الإرسال
}

function onStudentSelect() {
    console.log('👤 تم اختيار طالب');
    // تحديث الرسالة حسب الطالب المختار
}

function sendQuickMessage() {
    console.log('⚡ إرسال سريع');
    const studentsSelect = document.getElementById('students-select');
    const messageText = document.getElementById('message-text');

    if (studentsSelect && studentsSelect.selectedOptions.length > 0) {
        const selectedPhone = studentsSelect.selectedOptions[0].value;
        const message = messageText ? messageText.value : 'رسالة سريعة';

        if (selectedPhone && selectedPhone.length >= 12) {
            const whatsappUrl = `https://wa.me/${selectedPhone}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
            console.log('📱 تم فتح WhatsApp للرقم:', selectedPhone);
        } else {
            alert('رقم الهاتف غير صحيح');
        }
    } else {
        alert('يرجى اختيار طالب أولاً');
    }
}

function openWhatsApp() {
    console.log('📱 فتح WhatsApp Web مع البيانات');

    // فتح WhatsApp Web مع تمرير البيانات
    const whatsappWindow = window.open('https://web.whatsapp.com', '_blank');

    // تمرير البيانات للنافذة الجديدة
    setTimeout(() => {
        if (whatsappWindow && !whatsappWindow.closed) {
            whatsappWindow.name = window.name; // نقل البيانات
            console.log('✅ تم تمرير البيانات لـ WhatsApp Web');
        }
    }, 1000);
}

// دالة ضمان وجود الدوال في النطاق العام
function ensureGlobalFunctions() {
    // إضافة جميع الدوال للنطاق العام
    window.closeDataModal = closeDataModal;
    window.loadModalData = loadModalData;
    window.refreshModalData = refreshModalData;
    window.fetchFromDatabase = fetchFromDatabase;
    window.fetchFromWebsite = fetchFromWebsite;
    window.scrapeFromPage = scrapeFromPage;
    window.exportModalData = exportModalData;
    window.saveCurrentDataToDatabase = saveCurrentDataToDatabase;
    window.openKushoofDesigner = openKushoofDesigner;
    window.extractAndSaveData = extractAndSaveData;

    // دوال مصمم الكشوف
    window.openDesignerInNewTab = openDesignerInNewTab;
    window.openDesignerInWindow = openDesignerInWindow;
    window.copyDesignerLink = copyDesignerLink;

    // إضافة دالة ضمان الدوال نفسها للنطاق العام
    window.ensureGlobalFunctions = ensureGlobalFunctions;

    console.log('✅ تم ضمان وجود جميع الدوال في النطاق العام');
}

// تشغيل الدالة فور تحميل السكريپت
ensureGlobalFunctions();

// دوال الموديل
function openDataModal() {
    console.log('📊 فتح موديل البيانات');

    // إضافة الدوال للنطاق العام أولاً
    ensureGlobalFunctions();

    // إنشاء الموديل إذا لم يكن موجود
    let modal = document.getElementById('kushoof-modal');
    if (!modal) {
        createDataModal();
        modal = document.getElementById('kushoof-modal');
    }

    // عرض الموديل
    modal.style.display = 'block';

    // تحميل البيانات
    loadModalData();
}

function createDataModal() {
    const modalHTML = `
        <div id="kushoof-modal" class="kushoof-modal">
            <div class="kushoof-modal-content">
                <div class="kushoof-modal-header">
                    <h2 class="kushoof-modal-title">📊 بيانات الطلاب - Kushoof</h2>
                    <button id="close-modal-btn" class="kushoof-modal-close">×</button>
                </div>
                <div class="kushoof-modal-body">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="refresh-modal-btn" class="kushoof-refresh-btn">🔄 تحديث البيانات</button>
                        <button id="fetch-database-btn" class="kushoof-refresh-btn" style="background: #4CAF50;">🗄️ جلب من قاعدة البيانات</button>
                        <button id="fetch-website-btn" class="kushoof-refresh-btn" style="background: #2196F3;">🌐 جلب من الخادم</button>
                        <button id="scrape-page-btn" class="kushoof-refresh-btn" style="background: #9C27B0;">🔍 استخراج من الصفحة</button>
                        <button id="export-modal-btn" class="kushoof-refresh-btn" style="background: #FF9800;">📥 تصدير</button>
                        <button id="save-to-db-btn" class="kushoof-refresh-btn" style="background: #28a745;">💾 حفظ في قاعدة البيانات</button>
                        <button id="open-designer-btn" class="kushoof-refresh-btn" style="background: #6f42c1;">🎨 مصمم الكشوف</button>
                        <button id="extract-and-save-btn" class="kushoof-refresh-btn" style="background: #dc3545;">🚀 جلب وحفظ البيانات</button>
                    </div>
                    <div id="modal-loading" style="text-align: center; padding: 40px;">
                        <div style="font-size: 18px;">⏳ جاري تحميل البيانات...</div>
                    </div>
                    <div id="modal-content" style="display: none;">
                        <!-- سيتم ملء المحتوى هنا -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة الموديل للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // إضافة مستمع إغلاق عند النقر خارج الموديل
    const modal = document.getElementById('kushoof-modal');
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeDataModal();
        }
    });

    // إضافة مستمعي الأحداث للأزرار
    addModalEventListeners();

    // إضافة الدوال للنطاق العام (مرة أخرى للتأكيد)
    ensureGlobalFunctions();

    console.log('✅ تم إنشاء موديل البيانات');
}

function addModalEventListeners() {
    console.log('🎧 إضافة مستمعي أحداث الموديل');

    // زر الإغلاق
    const closeBtn = document.getElementById('close-modal-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeDataModal);
    }

    // زر تحديث البيانات
    const refreshBtn = document.getElementById('refresh-modal-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshModalData);
    }

    // زر جلب من قاعدة البيانات
    const fetchDbBtn = document.getElementById('fetch-database-btn');
    if (fetchDbBtn) {
        fetchDbBtn.addEventListener('click', fetchFromDatabase);
    }

    // زر جلب من الخادم
    const fetchWebBtn = document.getElementById('fetch-website-btn');
    if (fetchWebBtn) {
        fetchWebBtn.addEventListener('click', fetchFromWebsite);
    }

    // زر استخراج من الصفحة
    const scrapeBtn = document.getElementById('scrape-page-btn');
    if (scrapeBtn) {
        scrapeBtn.addEventListener('click', scrapeFromPage);
    }

    // زر التصدير
    const exportBtn = document.getElementById('export-modal-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportModalData);
    }

    // زر حفظ في قاعدة البيانات
    const saveToDbBtn = document.getElementById('save-to-db-btn');
    if (saveToDbBtn) {
        saveToDbBtn.addEventListener('click', saveCurrentDataToDatabase);
    }

    // زر مصمم الكشوف
    const openDesignerBtn = document.getElementById('open-designer-btn');
    if (openDesignerBtn) {
        openDesignerBtn.addEventListener('click', openKushoofDesigner);
    }

    // زر جلب وحفظ البيانات
    const extractAndSaveBtn = document.getElementById('extract-and-save-btn');
    if (extractAndSaveBtn) {
        extractAndSaveBtn.addEventListener('click', extractAndSaveData);
    }

    console.log('✅ تم إضافة جميع مستمعي أحداث الموديل');
}

function closeDataModal() {
    const modal = document.getElementById('kushoof-modal');
    if (modal) {
        modal.style.display = 'none';
        console.log('❌ تم إغلاق موديل البيانات');
    }
}

function loadModalData() {
    console.log('📥 تحميل بيانات الموديل');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // محاولة جلب البيانات من window.name أولاً
    let studentsData = null;
    try {
        if (window.name.includes('kushoof')) {
            studentsData = JSON.parse(window.name.split('kushoof')[1]);
        }
    } catch (e) {
        console.warn('⚠️ فشل في جلب البيانات من window.name');
    }

    // إذا لم توجد بيانات، جلب من الخادم
    if (!studentsData) {
        fetchModalDataFromServer();
    } else {
        displayModalData(studentsData);
    }
}

async function fetchModalDataFromServer() {
    console.log('🌐 جلب البيانات من الخادم');

    try {
        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1];

        if (!schoolId) {
            throw new Error('لم يتم العثور على معرف المدرسة');
        }

        // جلب البيانات
        const scriptUrl = `https://kushoofapp.com/java/js.php?version=258&id=${schoolId}&k=K`;
        const response = await fetch(scriptUrl);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const scriptContent = await response.text();
        const studentsData = extractDataFromScript(scriptContent);

        if (studentsData) {
            displayModalData(studentsData);
            console.log('✅ تم جلب البيانات من الخادم بنجاح');
        } else {
            throw new Error('فشل في استخراج البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        displayModalError(error.message);
    }
}

async function fetchModalDataFromDatabase() {
    console.log('🗄️ جلب البيانات من قاعدة البيانات');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    try {
        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1];

        if (!schoolId) {
            throw new Error('لم يتم العثور على معرف المدرسة في URL');
        }

        console.log('🔍 البحث عن بيانات المدرسة:', schoolId);

        // إرسال طلب لقاعدة البيانات
        const response = await fetch('https://kushoofapp.com/js/api/receive-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                action: 'load',
                schoolId: schoolId
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📊 استجابة قاعدة البيانات:', result);

        if (!result.success) {
            throw new Error(result.message || 'فشل في جلب البيانات من قاعدة البيانات');
        }

        // تحويل البيانات إلى التنسيق المطلوب
        const databaseData = convertDatabaseToKushoofFormat(result.data);

        if (databaseData) {
            // حفظ البيانات في window.name
            window.name = 'kushoof' + JSON.stringify(databaseData);
            displayDatabaseData(result.data, databaseData);
            console.log('✅ تم جلب البيانات من قاعدة البيانات بنجاح');
        } else {
            throw new Error('فشل في تحويل البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات من قاعدة البيانات:', error);
        displayModalError('فشل في جلب البيانات من قاعدة البيانات: ' + error.message);
    }
}

function convertDatabaseToKushoofFormat(dbData) {
    try {
        const school = dbData.school;
        const students = dbData.students || [];

        console.log('🔄 تحويل بيانات قاعدة البيانات:', {
            school: school?.school_name,
            studentsCount: students.length
        });

        // تحويل بيانات الطلاب
        const names = [];
        const phones = [];
        const classes = [];

        students.forEach(student => {
            names.push(encodeURIComponent(student.student_name || ''));
            phones.push(student.parent_phone || student.student_phone || '');
            classes.push(encodeURIComponent(student.class_label || student.grade || ''));
        });

        // إنشاء التاريخ
        const today = new Date();
        const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const dayName = arabicDays[today.getDay()];
        const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

        return {
            n: names,
            p: phones,
            c: classes,
            day: formattedDate,
            school_id: school?.school_id || 'unknown',
            school_name: school?.school_name || 'غير محدد',
            manager_name: school?.manager_name || 'غير محدد',
            ministry_number: school?.ministry_number || '',
            version: 'database',
            timestamp: new Date().toISOString(),
            source: 'database',
            total_students: students.length,
            extracted_at: school?.extracted_at,
            updated_at: school?.updated_at
        };

    } catch (error) {
        console.error('❌ خطأ في تحويل البيانات:', error);
        return null;
    }
}

function displayModalData(data) {
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إخفاء شاشة التحميل
    loadingDiv.style.display = 'none';
    contentDiv.style.display = 'block';

    // إحصائيات
    const totalStudents = data.n ? data.n.length : 0;
    const validPhones = data.p ? data.p.filter(phone => phone.length >= 12).length : 0;
    const invalidPhones = totalStudents - validPhones;

    // بناء HTML المحتوى
    let contentHTML = `
        <div class="kushoof-data-grid">
            <div class="kushoof-data-card">
                <h3>📊 الإحصائيات</h3>
                <div style="font-size: 16px; line-height: 1.8;">
                    <div>👥 إجمالي الطلاب: <strong>${totalStudents}</strong></div>
                    <div style="color: #4CAF50;">✅ أرقام صحيحة: <strong>${validPhones}</strong></div>
                    <div style="color: #f44336;">❌ أرقام خاطئة: <strong>${invalidPhones}</strong></div>
                    <div>📅 التاريخ: <strong>${data.day || 'غير محدد'}</strong></div>
                    <div>🏫 المدرسة: <strong>${data.school_id || 'غير محدد'}</strong></div>
                </div>
            </div>

            <div class="kushoof-data-card">
                <h3>ℹ️ معلومات النظام</h3>
                <div style="font-size: 14px; line-height: 1.6;">
                    <div>🔢 الإصدار: ${data.version || 'غير محدد'}</div>
                    <div>⏰ الوقت: ${data.timestamp || 'غير محدد'}</div>
                    <div>🔗 المصدر: ${data.fallback ? 'بيانات تجريبية' : 'بيانات حقيقية'}</div>
                    <div>📡 API: /java/js.php</div>
                </div>
            </div>
        </div>

        <div class="kushoof-data-card">
            <h3>👥 قائمة الطلاب</h3>
            <div style="max-height: 300px; overflow-y: auto;">
    `;

    // إضافة قائمة الطلاب
    if (data.n && data.p) {
        for (let i = 0; i < data.n.length; i++) {
            const name = decodeURIComponent(data.n[i] || '');
            const phone = data.p[i] || '';
            const className = data.c && data.c[i] ? decodeURIComponent(data.c[i]) : `الصف ${i + 1}`;
            const isValid = phone.length >= 12;
            const statusIcon = isValid ? '✅' : '❌';
            const statusColor = isValid ? '#4CAF50' : '#f44336';

            contentHTML += `
                <div class="kushoof-student-item">
                    <div class="kushoof-student-name">${i + 1}. ${name}</div>
                    <div class="kushoof-student-phone" style="color: ${statusColor};">${phone}</div>
                    <div class="kushoof-student-class" style="color: #87CEEB;">${className}</div>
                    <div class="kushoof-student-status" style="color: ${statusColor};">${statusIcon}</div>
                </div>
            `;
        }
    } else {
        contentHTML += '<div style="text-align: center; padding: 20px; opacity: 0.7;">لا توجد بيانات طلاب</div>';
    }

    contentHTML += `
            </div>
        </div>
    `;

    contentDiv.innerHTML = contentHTML;
    console.log('✅ تم عرض البيانات في الموديل');
}

function displayModalError(errorMessage) {
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    loadingDiv.style.display = 'none';
    contentDiv.style.display = 'block';

    contentDiv.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
            <div style="font-size: 18px; margin-bottom: 10px;">خطأ في تحميل البيانات</div>
            <div style="font-size: 14px; opacity: 0.8;">${errorMessage}</div>
            <button class="kushoof-refresh-btn" onclick="loadModalData()" style="margin-top: 20px;">🔄 إعادة المحاولة</button>
        </div>
    `;
}

// دوال إضافية للموديل
function refreshModalData() {
    console.log('🔄 تحديث بيانات الموديل');

    // التأكد من وجود الموديل
    const modal = document.getElementById('kushoof-modal');
    if (!modal) {
        console.error('❌ الموديل غير موجود');
        alert('خطأ: الموديل غير موجود. يرجى إعادة فتح الموديل.');
        return;
    }

    // إظهار قائمة خيارات التحديث
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';

    contentDiv.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <h3 style="color: #FFD700; margin-bottom: 30px;">🔄 اختر مصدر التحديث</h3>

            <div style="display: grid; gap: 15px; max-width: 400px; margin: 0 auto;">
                <button id="load-saved-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px;">
                    💾 من البيانات المحفوظة
                </button>

                <button id="fetch-db-refresh-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px; background: #4CAF50;">
                    🗄️ من قاعدة البيانات
                </button>

                <button id="fetch-web-refresh-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px; background: #2196F3;">
                    🌐 من الخادم
                </button>

                <button id="scrape-refresh-btn" class="kushoof-refresh-btn" style="padding: 15px; font-size: 16px; background: #9C27B0;">
                    🔍 من الصفحة الحالية
                </button>
            </div>

            <div style="margin-top: 30px; font-size: 12px; opacity: 0.7;">
                💡 اختر المصدر المناسب لجلب أحدث البيانات
            </div>
        </div>
    `;

        // إضافة مستمعي الأحداث للأزرار الجديدة
        setTimeout(() => {
            console.log('🎧 إضافة مستمعي أحداث أزرار التحديث');

            const loadSavedBtn = document.getElementById('load-saved-btn');
            if (loadSavedBtn) {
                loadSavedBtn.addEventListener('click', loadModalData);
                console.log('✅ تم إضافة مستمع لزر البيانات المحفوظة');
            }

            const fetchDbRefreshBtn = document.getElementById('fetch-db-refresh-btn');
            if (fetchDbRefreshBtn) {
                fetchDbRefreshBtn.addEventListener('click', fetchFromDatabase);
                console.log('✅ تم إضافة مستمع لزر قاعدة البيانات');
            }

            const fetchWebRefreshBtn = document.getElementById('fetch-web-refresh-btn');
            if (fetchWebRefreshBtn) {
                fetchWebRefreshBtn.addEventListener('click', fetchFromWebsite);
                console.log('✅ تم إضافة مستمع لزر الخادم');
            }

            const scrapeRefreshBtn = document.getElementById('scrape-refresh-btn');
            if (scrapeRefreshBtn) {
                scrapeRefreshBtn.addEventListener('click', scrapeFromPage);
                console.log('✅ تم إضافة مستمع لزر استخراج الصفحة');
            }

            console.log('✅ تم إضافة جميع مستمعي أحداث أزرار التحديث');
        }, 100);
    } else {
        console.error('❌ لم يتم العثور على عناصر الموديل');
        alert('خطأ: لم يتم العثور على عناصر الموديل. يرجى إعادة فتح الموديل.');
    }
}

function fetchFromDatabase() {
    console.log('🗄️ جلب البيانات من قاعدة البيانات');
    fetchModalDataFromDatabase();
}

async function fetchModalDataFromDatabase() {
    console.log('🗄️ جلب البيانات من قاعدة البيانات الحقيقية');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    try {
        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1];

        if (!schoolId) {
            throw new Error('لم يتم العثور على معرف المدرسة في URL');
        }

        console.log('🔍 البحث عن بيانات المدرسة:', schoolId);

        // إرسال طلب لقاعدة البيانات عبر background script
        const response = await chrome.runtime.sendMessage({
            action: 'fetchFromDatabase',
            schoolId: schoolId
        });

        console.log('📡 استجابة background script:', response);

        if (!response.success) {
            throw new Error(response.error || 'فشل في الاتصال بقاعدة البيانات');
        }

        const result = response.data;
        console.log('📊 استجابة قاعدة البيانات:', result);

        if (!result.success) {
            throw new Error(result.message || 'فشل في جلب البيانات من قاعدة البيانات');
        }

        // التحقق من وجود البيانات
        if (!result.data || !result.data.school) {
            throw new Error('لا توجد بيانات للمدرسة في قاعدة البيانات');
        }

        // التحقق من وجود البيانات
        if (!result.data || !result.data.school) {
            throw new Error('لا توجد بيانات للمدرسة في قاعدة البيانات');
        }

        // تحويل البيانات إلى التنسيق المطلوب
        const databaseData = convertDatabaseToKushoofFormat(result.data);

        if (databaseData) {
            // حفظ البيانات في window.name
            window.name = 'kushoof' + JSON.stringify(databaseData);
            displayDatabaseData(result.data, databaseData);
            console.log('✅ تم جلب البيانات من قاعدة البيانات بنجاح');
        } else {
            throw new Error('فشل في تحويل البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في جلب البيانات من قاعدة البيانات:', error);
        displayModalError('فشل في جلب البيانات من قاعدة البيانات: ' + error.message);
    }
}

function convertDatabaseToKushoofFormat(dbData) {
    try {
        const school = dbData.school;
        const students = dbData.students || [];

        console.log('🔄 تحويل بيانات قاعدة البيانات:', {
            school: school?.school_name,
            studentsCount: students.length
        });

        // تحويل بيانات الطلاب
        const names = [];
        const phones = [];
        const classes = [];

        students.forEach(student => {
            names.push(encodeURIComponent(student.student_name || ''));
            phones.push(student.parent_phone || student.student_phone || '');
            classes.push(encodeURIComponent(student.class_label || student.grade || ''));
        });

        // إنشاء التاريخ
        const today = new Date();
        const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const dayName = arabicDays[today.getDay()];
        const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

        return {
            n: names,
            p: phones,
            c: classes,
            day: formattedDate,
            school_id: school?.school_id || 'unknown',
            school_name: school?.school_name || 'غير محدد',
            manager_name: school?.manager_name || 'غير محدد',
            ministry_number: school?.ministry_number || '',
            version: 'database',
            timestamp: new Date().toISOString(),
            source: 'database',
            total_students: students.length,
            extracted_at: school?.extracted_at,
            updated_at: school?.updated_at
        };

    } catch (error) {
        console.error('❌ خطأ في تحويل البيانات:', error);
        return null;
    }
}

function displayDatabaseData(rawData, convertedData) {
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إخفاء شاشة التحميل
    loadingDiv.style.display = 'none';
    contentDiv.style.display = 'block';

    const school = rawData.school;
    const students = rawData.students || [];

    // إحصائيات
    const totalStudents = students.length;
    const validPhones = students.filter(s => (s.parent_phone || s.student_phone || '').length >= 12).length;
    const invalidPhones = totalStudents - validPhones;

    // بناء HTML المحتوى
    let contentHTML = `
        <div class="kushoof-data-grid">
            <div class="kushoof-data-card">
                <h3>🏫 معلومات المدرسة</h3>
                <div style="font-size: 14px; line-height: 1.8;">
                    <div><strong>اسم المدرسة:</strong> ${school?.school_name || 'غير محدد'}</div>
                    <div><strong>مدير المدرسة:</strong> ${school?.manager_name || 'غير محدد'}</div>
                    <div><strong>رقم الوزارة:</strong> ${school?.ministry_number || 'غير محدد'}</div>
                    <div><strong>معرف المدرسة:</strong> ${school?.school_id || 'غير محدد'}</div>
                    <div><strong>تاريخ الاستخراج:</strong> ${school?.extracted_at || 'غير محدد'}</div>
                    <div><strong>آخر تحديث:</strong> ${school?.updated_at || 'غير محدد'}</div>
                </div>
            </div>

            <div class="kushoof-data-card">
                <h3>📊 إحصائيات الطلاب</h3>
                <div style="font-size: 16px; line-height: 1.8;">
                    <div>👥 إجمالي الطلاب: <strong style="color: #FFD700;">${totalStudents}</strong></div>
                    <div style="color: #4CAF50;">✅ أرقام صحيحة: <strong>${validPhones}</strong></div>
                    <div style="color: #f44336;">❌ أرقام خاطئة: <strong>${invalidPhones}</strong></div>
                    <div>📅 التاريخ: <strong>${convertedData.day}</strong></div>
                    <div>🗄️ المصدر: <strong>قاعدة البيانات</strong></div>
                </div>
            </div>
        </div>

        <div class="kushoof-data-card">
            <h3>👥 قائمة الطلاب من قاعدة البيانات</h3>
            <div style="max-height: 400px; overflow-y: auto;">
                <div style="display: grid; grid-template-columns: 40px 1fr 1fr 120px 60px; gap: 10px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px; margin-bottom: 10px; font-weight: bold;">
                    <div>#</div>
                    <div>اسم الطالب</div>
                    <div>رقم ولي الأمر</div>
                    <div>الصف</div>
                    <div>الحالة</div>
                </div>
    `;

    // إضافة قائمة الطلاب
    if (students.length > 0) {
        students.forEach((student, index) => {
            const name = student.student_name || 'غير محدد';
            const phone = student.parent_phone || student.student_phone || '';
            const className = student.class_label || student.grade || 'غير محدد';
            const isValid = phone.length >= 12;
            const statusIcon = isValid ? '✅' : '❌';
            const statusColor = isValid ? '#4CAF50' : '#f44336';

            contentHTML += `
                <div style="display: grid; grid-template-columns: 40px 1fr 1fr 120px 60px; gap: 10px; padding: 8px; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 13px;">
                    <div style="color: #FFD700;">${index + 1}</div>
                    <div style="font-weight: bold;">${name}</div>
                    <div style="color: ${statusColor}; font-family: monospace;">${phone}</div>
                    <div style="color: #87CEEB;">${className}</div>
                    <div style="color: ${statusColor}; text-align: center;">${statusIcon}</div>
                </div>
            `;
        });
    } else {
        contentHTML += '<div style="text-align: center; padding: 20px; opacity: 0.7;">لا توجد بيانات طلاب في قاعدة البيانات</div>';
    }

    contentHTML += `
            </div>
        </div>

        <div class="kushoof-data-card">
            <h3>🔧 معلومات تقنية</h3>
            <div style="font-size: 12px; line-height: 1.6; font-family: monospace;">
                <div>🌐 API: https://kushoofapp.com/js/api/receive-data.php</div>
                <div>📡 الطريقة: POST</div>
                <div>⏰ وقت الجلب: ${new Date().toLocaleString('ar-SA')}</div>
                <div>🔄 نوع البيانات: JSON من قاعدة البيانات</div>
                <div>📊 حجم البيانات: ${JSON.stringify(rawData).length} حرف</div>
            </div>
        </div>
    `;

    contentDiv.innerHTML = contentHTML;
    console.log('✅ تم عرض البيانات من قاعدة البيانات في الموديل');
}

function fetchFromWebsite() {
    console.log('🌐 جلب البيانات من الخادم');
    fetchModalDataFromServer();
}

function scrapeFromPage() {
    console.log('🔍 استخراج البيانات من الصفحة الحالية');

    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('modal-content');

    // إظهار شاشة التحميل
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';

    // استخدام setTimeout لتجنب التعليق
    setTimeout(() => {
        try {
            // محاولة استخراج البيانات من عناصر الصفحة
            const scrapedData = extractDataFromPageOptimized();

            if (scrapedData && scrapedData.n && scrapedData.n.length > 0) {
                // حفظ البيانات المستخرجة
                window.name = 'kushoof' + JSON.stringify(scrapedData);
                displayModalData(scrapedData);
                console.log('✅ تم استخراج البيانات من الصفحة بنجاح');
            } else {
                throw new Error('لم يتم العثور على بيانات في الصفحة الحالية');
            }

        } catch (error) {
            console.error('❌ خطأ في استخراج البيانات من الصفحة:', error);
            displayModalError('فشل في استخراج البيانات من الصفحة: ' + error.message);
        }
    }, 100);
}

function extractDataFromPageOptimized() {
    console.log('🔍 البحث المحسن عن بيانات الطلاب في الصفحة...');

    const students = [];
    const phones = [];
    const classes = [];

    try {
        // طريقة محسنة: البحث في النص الكامل للصفحة
        const pageText = document.body.innerText;

        // البحث عن أنماط الأسماء العربية مع أرقام الهواتف
        const phonePattern = /(?:966|0)?[5][0-9]{8}/g;
        const arabicNamePattern = /[\u0600-\u06FF\s]{3,30}/g;

        // جلب جميع أرقام الهواتف
        const foundPhones = pageText.match(phonePattern) || [];

        // تنظيف أرقام الهواتف
        const cleanPhones = foundPhones.map(phone => {
            let cleanPhone = phone.replace(/\s+/g, '');
            if (cleanPhone.startsWith('0')) {
                cleanPhone = '966' + cleanPhone.substring(1);
            } else if (!cleanPhone.startsWith('966')) {
                cleanPhone = '966' + cleanPhone;
            }
            return cleanPhone;
        }).slice(0, 10); // أخذ أول 10 أرقام فقط لتجنب التعليق

        // جلب الأسماء العربية
        const foundNames = pageText.match(arabicNamePattern) || [];

        // تنظيف الأسماء
        const cleanNames = foundNames
            .filter(name => {
                const trimmed = name.trim();
                return trimmed.length > 3 &&
                       trimmed.length < 50 &&
                       !trimmed.includes('المدرسة') &&
                       !trimmed.includes('المعلم') &&
                       !trimmed.includes('الصف') &&
                       !trimmed.includes('الفصل');
            })
            .slice(0, 10); // أخذ أول 10 أسماء فقط

        // دمج البيانات
        const maxCount = Math.min(cleanNames.length, cleanPhones.length, 10);

        for (let i = 0; i < maxCount; i++) {
            if (cleanNames[i] && cleanPhones[i]) {
                students.push(cleanNames[i].trim());
                phones.push(cleanPhones[i]);
                classes.push(`الصف ${i + 1}`);
            }
        }

        console.log(`📊 تم العثور على ${students.length} طالب من الصفحة`);

    } catch (error) {
        console.error('❌ خطأ في الاستخراج المحسن:', error);

        // طريقة احتياطية بسيطة
        try {
            // البحث في الجداول فقط (محدود)
            const tables = document.querySelectorAll('table');
            let tableCount = 0;

            for (const table of tables) {
                if (tableCount >= 3) break; // فحص 3 جداول فقط

                const rows = table.querySelectorAll('tr');
                let rowCount = 0;

                for (const row of rows) {
                    if (rowCount >= 10) break; // فحص 10 صفوف فقط

                    const text = row.textContent;
                    const phoneMatch = text.match(/(?:966|0)?[5][0-9]{8}/);
                    const nameMatch = text.match(/[\u0600-\u06FF\s]{3,30}/);

                    if (phoneMatch && nameMatch) {
                        let phone = phoneMatch[0].replace(/\s+/g, '');
                        if (phone.startsWith('0')) {
                            phone = '966' + phone.substring(1);
                        } else if (!phone.startsWith('966')) {
                            phone = '966' + phone;
                        }

                        students.push(nameMatch[0].trim());
                        phones.push(phone);
                        classes.push('مستخرج من جدول');
                    }

                    rowCount++;
                }
                tableCount++;
            }
        } catch (fallbackError) {
            console.error('❌ خطأ في الطريقة الاحتياطية:', fallbackError);
        }
    }

    // إنشاء كائن البيانات إذا وجدت بيانات
    if (students.length > 0) {
        const today = new Date();
        const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const dayName = arabicDays[today.getDay()];
        const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

        // استخراج معرف المدرسة من URL
        const urlParts = window.location.href.split("=");
        const schoolId = urlParts[1] || 'extracted_from_page';

        const extractedData = {
            n: students.map(name => encodeURIComponent(name)),
            p: phones,
            c: classes.map(cls => encodeURIComponent(cls)),
            day: formattedDate,
            school_id: schoolId,
            school_name: 'مدرسة مستخرجة من الصفحة',
            manager_name: 'مدير غير محدد',
            ministry_number: null,
            version: 'scraped_optimized',
            timestamp: new Date().toISOString(),
            source: 'page_extraction_optimized',
            extracted_count: students.length,
            total_students: students.length
        };

        console.log('✅ تم إنشاء البيانات المستخرجة:', extractedData);
        return extractedData;
    }

    // إذا لم توجد بيانات، إنشاء بيانات تجريبية
    console.log('⚠️ لم يتم العثور على بيانات في الصفحة، إنشاء بيانات تجريبية...');

    const today = new Date();
    const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = arabicDays[today.getDay()];
    const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

    const urlParts = window.location.href.split("=");
    const schoolId = urlParts[1] || 'demo_school';

    return {
        n: [
            encodeURIComponent('أحمد محمد علي'),
            encodeURIComponent('فاطمة أحمد سالم'),
            encodeURIComponent('محمد عبدالله حسن')
        ],
        p: ['966501234567', '966507654321', '966509876543'],
        c: [
            encodeURIComponent('الصف الأول'),
            encodeURIComponent('الصف الثاني'),
            encodeURIComponent('الصف الثالث')
        ],
        day: formattedDate,
        school_id: schoolId,
        school_name: 'مدرسة تجريبية من الصفحة',
        manager_name: 'مدير تجريبي',
        ministry_number: '12345',
        version: 'demo_from_page',
        timestamp: new Date().toISOString(),
        source: 'demo_page_extraction',
        extracted_count: 3,
        total_students: 3
    };
}

function exportModalData() {
    console.log('📥 تصدير بيانات الموديل');

    try {
        let data = null;
        if (window.name.includes('kushoof')) {
            data = JSON.parse(window.name.split('kushoof')[1]);
        }

        if (data) {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `kushoof_data_${data.school_id || 'unknown'}_${new Date().getTime()}.json`;
            a.click();

            URL.revokeObjectURL(url);
            console.log('✅ تم تصدير البيانات');
        } else {
            alert('لا توجد بيانات للتصدير');
        }
    } catch (error) {
        console.error('❌ خطأ في تصدير البيانات:', error);
        alert('خطأ في تصدير البيانات');
    }
}

// ==== دوال متقدمة من c1.js ====

// دالة حفظ البيانات الحالية في قاعدة البيانات
async function saveCurrentDataToDatabase() {
    console.log('💾 حفظ البيانات الحالية في قاعدة البيانات');

    try {
        // جلب البيانات الحالية
        let currentData = null;
        if (window.name.includes('kushoof')) {
            currentData = JSON.parse(window.name.split('kushoof')[1]);
        }

        if (!currentData) {
            alert('❌ لا توجد بيانات للحفظ\n\nيرجى جلب البيانات أولاً');
            return;
        }

        // إظهار شاشة التحميل
        const loadingDiv = document.getElementById('modal-loading');
        const contentDiv = document.getElementById('modal-content');

        if (loadingDiv) loadingDiv.style.display = 'block';
        if (contentDiv) contentDiv.style.display = 'none';

        // تحضير البيانات للحفظ
        const schoolData = {
            schoolId: currentData.school_id,
            schoolName: currentData.school_name || 'غير محدد',
            managerName: currentData.manager_name || 'غير محدد',
            ministryNumber: currentData.ministry_number || null,
            extractedAt: new Date().toISOString(),
            students: [],
            totalFound: currentData.n ? currentData.n.length : 0,
            extractionMethod: 'kushoof_modal_export'
        };

        // تحويل البيانات إلى تنسيق الطلاب
        if (currentData.n && currentData.p) {
            for (let i = 0; i < currentData.n.length; i++) {
                const student = {
                    name: decodeURIComponent(currentData.n[i] || ''),
                    parentPhone: currentData.p[i] || '',
                    studentPhone: '',
                    classLabel: currentData.c && currentData.c[i] ? decodeURIComponent(currentData.c[i]) : '',
                    grade: currentData.c && currentData.c[i] ? decodeURIComponent(currentData.c[i]) : '',
                    section: '',
                    username: '',
                    nationalId: ''
                };
                schoolData.students.push(student);
            }
        }

        console.log('📊 البيانات المحضرة للحفظ:', schoolData);

        // إرسال البيانات للخادم
        const response = await chrome.runtime.sendMessage({
            action: 'saveToDatabase',
            schoolData: schoolData
        });

        // إخفاء شاشة التحميل
        if (loadingDiv) loadingDiv.style.display = 'none';
        if (contentDiv) contentDiv.style.display = 'block';

        if (response.success) {
            alert(`✅ تم حفظ البيانات بنجاح!

🏫 معلومات المدرسة:
   • الاسم: ${schoolData.schoolName}
   • المدير: ${schoolData.managerName}
   • الرقم الوزاري: ${schoolData.ministryNumber || 'غير متوفر'}

📊 بيانات الطلاب:
   • العدد: ${schoolData.totalFound}
   • تاريخ الحفظ: ${new Date().toLocaleString('ar-SA')}

💾 تم الحفظ في قاعدة البيانات بنجاح!`);

            console.log('✅ تم حفظ البيانات في قاعدة البيانات بنجاح');
        } else {
            throw new Error(response.error || 'فشل في حفظ البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);

        // إخفاء شاشة التحميل
        const loadingDiv = document.getElementById('modal-loading');
        const contentDiv = document.getElementById('modal-content');
        if (loadingDiv) loadingDiv.style.display = 'none';
        if (contentDiv) contentDiv.style.display = 'block';

        alert('❌ فشل في حفظ البيانات: ' + error.message);
    }
}

// دالة فتح مصمم الكشوف المدمج الكامل (بنفس تصميم c1.js)
function openKushoofDesigner() {
    console.log('🎨 فتح مصمم الكشوف المدمج الكامل');

    const schoolId = getSchoolIdFromUrl();
    if (!schoolId) {
        alert('❌ لم يتم العثور على معرف المدرسة\n\nتأكد من أنك في صفحة مدرسة صحيحة في منصة مدرستي');
        return;
    }

    // جلب البيانات الحالية
    const schoolData = prepareSchoolDataForDesigner(schoolId);

    // إنشاء مصمم الكشوف المدمج الكامل
    const modal = document.createElement('div');
    modal.id = 'kushoof-designer-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.95);
        z-index: 999999;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    modal.innerHTML = createFullDesignerHTML(schoolData);
    document.body.appendChild(modal);

    console.log('✅ تم إنشاء مصمم الكشوف المدمج الكامل');

    // إضافة event listeners
    setTimeout(() => {
        setupFullDesignerEventListeners(schoolData);
    }, 100);
}

// دالة إنشاء HTML لمصمم الكشوف الكامل (بنفس تصميم c1.js)
function createFullDesignerHTML(schoolData) {
    return `
        <div style="
            background: white;
            width: 95%;
            height: 90%;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        ">
            <!-- Header الجميل -->
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px 30px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            ">
                <div>
                    <h1 style="margin: 0; font-size: 28px; font-weight: bold;">🎨 مصمم الكشوف المتقدم</h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 16px;">
                        ${schoolData.schoolName} • ${schoolData.studentsCount} طالب • ${schoolData.managerName}
                    </p>
                </div>
                <button onclick="document.getElementById('kushoof-designer-modal').remove()" style="
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: none;
                    padding: 12px 16px;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 20px;
                    font-weight: bold;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">✕</button>
            </div>

            <!-- Content Area -->
            <div style="
                flex: 1;
                display: flex;
                overflow: hidden;
            ">
                <!-- Sidebar المتقدم -->
                <div style="
                    width: 350px;
                    background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
                    border-right: 1px solid #dee2e6;
                    padding: 25px;
                    overflow-y: auto;
                    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
                ">
                    <h3 style="margin: 0 0 25px 0; color: #495057; font-size: 20px; border-bottom: 2px solid #667eea; padding-bottom: 10px;">⚙️ إعدادات التصميم</h3>

                    <!-- نوع الكشف -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #495057; font-size: 16px;">📋 نوع الكشف:</label>
                        <select id="report-type" style="
                            width: 100%;
                            padding: 12px 15px;
                            border: 2px solid #ced4da;
                            border-radius: 8px;
                            font-size: 15px;
                            background: white;
                            transition: border-color 0.3s ease;
                        " onchange="this.style.borderColor='#667eea'">
                            <option value="attendance">📝 كشف حضور وغياب</option>
                            <option value="grades">📊 كشف درجات الطلاب</option>
                            <option value="contacts">📞 كشف أرقام الهواتف</option>
                            <option value="students-list">👥 قائمة الطلاب</option>
                            <option value="behavior">🌟 كشف السلوك</option>
                            <option value="activities">🎯 كشف الأنشطة</option>
                        </select>
                    </div>

                    <!-- عنوان الكشف -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #495057; font-size: 16px;">📝 عنوان الكشف:</label>
                        <input type="text" id="report-title" value="كشف حضور وغياب" style="
                            width: 100%;
                            padding: 12px 15px;
                            border: 2px solid #ced4da;
                            border-radius: 8px;
                            font-size: 15px;
                            transition: border-color 0.3s ease;
                        " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#ced4da'">
                    </div>

                    <!-- معلومات المدرسة -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #495057; font-size: 16px;">🏫 اسم المدرسة:</label>
                        <input type="text" id="school-name" value="${schoolData.schoolName}" style="
                            width: 100%;
                            padding: 12px 15px;
                            border: 2px solid #ced4da;
                            border-radius: 8px;
                            font-size: 15px;
                            transition: border-color 0.3s ease;
                        " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#ced4da'">
                    </div>

                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #495057; font-size: 16px;">👨‍💼 اسم المدير:</label>
                        <input type="text" id="manager-name" value="${schoolData.managerName}" style="
                            width: 100%;
                            padding: 12px 15px;
                            border: 2px solid #ced4da;
                            border-radius: 8px;
                            font-size: 15px;
                            transition: border-color 0.3s ease;
                        " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#ced4da'">
                    </div>

                    <!-- إعدادات إضافية -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #495057; font-size: 16px;">🎨 لون الرأس:</label>
                        <select id="header-color" style="
                            width: 100%;
                            padding: 12px 15px;
                            border: 2px solid #ced4da;
                            border-radius: 8px;
                            font-size: 15px;
                            background: white;
                        ">
                            <option value="blue">🔵 أزرق</option>
                            <option value="green">🟢 أخضر</option>
                            <option value="purple">🟣 بنفسجي</option>
                            <option value="orange">🟠 برتقالي</option>
                        </select>
                    </div>

                    <!-- أزرار التحكم المتقدمة -->
                    <div style="margin-top: 35px;">
                        <button id="preview-btn" style="
                            width: 100%;
                            padding: 15px;
                            background: linear-gradient(135deg, #28a745, #20c997);
                            color: white;
                            border: none;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            margin-bottom: 12px;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(40, 167, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.3)'">👁️ معاينة فورية</button>

                        <button id="print-btn" style="
                            width: 100%;
                            padding: 15px;
                            background: linear-gradient(135deg, #007bff, #0056b3);
                            color: white;
                            border: none;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            margin-bottom: 12px;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 123, 255, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0, 123, 255, 0.3)'">🖨️ طباعة مباشرة</button>

                        <button id="export-pdf-btn" style="
                            width: 100%;
                            padding: 15px;
                            background: linear-gradient(135deg, #dc3545, #c82333);
                            color: white;
                            border: none;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            margin-bottom: 12px;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(220, 53, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(220, 53, 69, 0.3)'">📄 تصدير PDF</button>

                        <button id="save-template-btn" style="
                            width: 100%;
                            padding: 15px;
                            background: linear-gradient(135deg, #6f42c1, #5a2d91);
                            color: white;
                            border: none;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(111, 66, 193, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(111, 66, 193, 0.3)'">💾 حفظ كقالب</button>
                    </div>
                </div>

                <!-- Preview Area المتقدم -->
                <div style="
                    flex: 1;
                    background: #f8f9fa;
                    padding: 25px;
                    overflow-y: auto;
                ">
                    <div style="
                        background: white;
                        border-radius: 10px;
                        padding: 20px;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                        margin-bottom: 20px;
                    ">
                        <h3 style="margin: 0 0 15px 0; color: #495057; font-size: 18px;">📊 إحصائيات سريعة</h3>
                        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                            <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 15px; border-radius: 8px; min-width: 120px; text-align: center;">
                                <div style="font-size: 24px; font-weight: bold;">${schoolData.studentsCount}</div>
                                <div style="font-size: 14px; opacity: 0.9;">طالب</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; border-radius: 8px; min-width: 120px; text-align: center;">
                                <div style="font-size: 24px; font-weight: bold;">1446</div>
                                <div style="font-size: 14px; opacity: 0.9;">العام الدراسي</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 15px; border-radius: 8px; min-width: 120px; text-align: center;">
                                <div style="font-size: 24px; font-weight: bold;">الثالث</div>
                                <div style="font-size: 14px; opacity: 0.9;">الفصل</div>
                            </div>
                        </div>
                    </div>

                    <div id="report-preview" style="
                        background: white;
                        border: 1px solid #dee2e6;
                        border-radius: 10px;
                        padding: 40px;
                        min-height: 600px;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                        position: relative;
                    ">
                        <!-- سيتم إنشاء المعاينة هنا -->
                        <div style="text-align: center; color: #6c757d; padding: 50px;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📋</div>
                            <h3>اختر نوع الكشف لبدء المعاينة</h3>
                            <p>سيتم عرض الكشف هنا بعد اختيار النوع والإعدادات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// دالة إنشاء HTML لمصمم الكشوف
function createDesignerModalHTML(schoolData) {
    return `
        <div style="
            background: white;
            width: 95%;
            height: 90%;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        ">
            <!-- Header -->
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            ">
                <div>
                    <h2 style="margin: 0; font-size: 24px;">🎨 مصمم الكشوف</h2>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">${schoolData.schoolName} - ${schoolData.studentsCount} طالب</p>
                </div>
                <button onclick="document.getElementById('kushoof-designer-modal').remove()" style="
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 18px;
                    font-weight: bold;
                ">✕</button>
            </div>

            <!-- Content -->
            <div style="
                flex: 1;
                display: flex;
                overflow: hidden;
            ">
                <!-- Sidebar -->
                <div style="
                    width: 300px;
                    background: #f8f9fa;
                    border-right: 1px solid #dee2e6;
                    padding: 20px;
                    overflow-y: auto;
                ">
                    <h3 style="margin: 0 0 20px 0; color: #495057;">⚙️ إعدادات الكشف</h3>

                    <!-- نوع الكشف -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">نوع الكشف:</label>
                        <select id="report-type" style="
                            width: 100%;
                            padding: 10px;
                            border: 1px solid #ced4da;
                            border-radius: 5px;
                            font-size: 14px;
                        ">
                            <option value="attendance">كشف حضور وغياب</option>
                            <option value="grades">كشف درجات</option>
                            <option value="contacts">كشف أرقام الهواتف</option>
                            <option value="students-list">قائمة الطلاب</option>
                        </select>
                    </div>

                    <!-- عنوان الكشف -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">عنوان الكشف:</label>
                        <input type="text" id="report-title" value="كشف حضور وغياب" style="
                            width: 100%;
                            padding: 10px;
                            border: 1px solid #ced4da;
                            border-radius: 5px;
                            font-size: 14px;
                        ">
                    </div>

                    <!-- معلومات المدرسة -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">اسم المدرسة:</label>
                        <input type="text" id="school-name" value="${schoolData.schoolName}" style="
                            width: 100%;
                            padding: 10px;
                            border: 1px solid #ced4da;
                            border-radius: 5px;
                            font-size: 14px;
                        ">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">اسم المدير:</label>
                        <input type="text" id="manager-name" value="${schoolData.managerName}" style="
                            width: 100%;
                            padding: 10px;
                            border: 1px solid #ced4da;
                            border-radius: 5px;
                            font-size: 14px;
                        ">
                    </div>

                    <!-- أزرار التحكم -->
                    <div style="margin-top: 30px;">
                        <button id="preview-btn" style="
                            width: 100%;
                            padding: 12px;
                            background: #28a745;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            margin-bottom: 10px;
                        ">👁️ معاينة</button>

                        <button id="print-btn" style="
                            width: 100%;
                            padding: 12px;
                            background: #007bff;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            margin-bottom: 10px;
                        ">🖨️ طباعة</button>

                        <button id="export-pdf-btn" style="
                            width: 100%;
                            padding: 12px;
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                        ">📄 تصدير PDF</button>
                    </div>
                </div>

                <!-- Preview Area -->
                <div style="
                    flex: 1;
                    background: white;
                    padding: 20px;
                    overflow-y: auto;
                ">
                    <div id="report-preview" style="
                        background: white;
                        border: 1px solid #dee2e6;
                        border-radius: 5px;
                        padding: 30px;
                        min-height: 500px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    ">
                        <!-- سيتم إنشاء المعاينة هنا -->
                    </div>
                </div>
            </div>
        </div>
    `;
}

// دالة فتح المصمم في تبويب جديد
function openDesignerInNewTab(schoolId) {
    console.log('🌐 فتح مصمم الكشوف في تبويب جديد');

    const url = `https://kushoofapp.com/js/designer.php?sid=${schoolId}&ref=madrasati&load_from_db=true`;
    const newTab = window.open(url, '_blank');

    if (newTab) {
        console.log('✅ تم فتح التبويب الجديد');

        // إرسال البيانات بعد فترة
        setTimeout(() => {
            sendDataToDesignerTab(newTab, schoolId);
        }, 3000);

        // إغلاق الموديل
        document.getElementById('designer-modal')?.remove();
    } else {
        alert('❌ فشل في فتح التبويب الجديد\n\nتأكد من السماح للنوافذ المنبثقة في المتصفح');
    }
}

// دالة فتح المصمم في نافذة منفصلة
function openDesignerInWindow(schoolId) {
    console.log('🪟 فتح مصمم الكشوف في نافذة منفصلة');

    const features = 'width=1200,height=800,resizable=yes,scrollbars=yes,toolbar=no,menubar=no,location=no,status=no';
    const url = `https://kushoofapp.com/js/designer.php?sid=${schoolId}&ref=madrasati&load_from_db=true`;

    const newWindow = window.open(url, 'KushoofDesigner', features);

    if (newWindow) {
        // تحديد الحجم والموضع
        setTimeout(() => {
            newWindow.resizeTo(1200, 800);
            newWindow.moveTo(
                (screen.width - 1200) / 2,
                (screen.height - 800) / 2
            );
        }, 500);

        console.log('✅ تم فتح النافذة المنفصلة');

        // إرسال البيانات بعد فترة
        setTimeout(() => {
            sendDataToDesignerWindow(newWindow, schoolId);
        }, 3000);

        // إغلاق الموديل
        document.getElementById('designer-modal')?.remove();
    } else {
        alert('❌ فشل في فتح النافذة\n\nتأكد من السماح للنوافذ المنبثقة في المتصفح');
    }
}

// دالة نسخ رابط المصمم
function copyDesignerLink(schoolId) {
    console.log('📋 نسخ رابط مصمم الكشوف');

    const url = `https://kushoofapp.com/js/designer.php?sid=${schoolId}&ref=madrasati&load_from_db=true`;

    // نسخ الرابط للحافظة
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(url).then(() => {
            console.log('✅ تم نسخ الرابط بنجاح');
            alert('✅ تم نسخ رابط مصمم الكشوف بنجاح!\n\nيمكنك الآن لصقه في تبويب جديد.');
        }).catch(err => {
            console.error('❌ فشل في نسخ الرابط:', err);
            fallbackCopyToClipboard(url);
        });
    } else {
        fallbackCopyToClipboard(url);
    }
}

// دالة نسخ احتياطية
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            console.log('✅ تم نسخ الرابط بالطريقة الاحتياطية');
            alert('✅ تم نسخ رابط مصمم الكشوف بنجاح!\n\nيمكنك الآن لصقه في تبويب جديد.');
        } else {
            throw new Error('فشل في النسخ');
        }
    } catch (err) {
        console.error('❌ فشل في النسخ الاحتياطي:', err);
        alert('📋 انسخ هذا الرابط يدوياً:\n\n' + text);
    } finally {
        document.body.removeChild(textArea);
    }
}

// دالة جلب عدد الطلاب الحالي
function getCurrentStudentsCount() {
    try {
        if (window.name.includes('kushoof')) {
            const currentData = JSON.parse(window.name.split('kushoof')[1]);
            return currentData.n ? currentData.n.length : 0;
        }

        const storedData = localStorage.getItem('kushoof_backup_data');
        if (storedData) {
            const parsedData = JSON.parse(storedData);
            return parsedData.schoolData?.totalFound || 0;
        }
    } catch (error) {
        console.warn('⚠️ فشل في جلب عدد الطلاب:', error);
    }
    return 0;
}

// دالة إرسال البيانات للتبويب الجديد
function sendDataToDesignerTab(designerTab, schoolId) {
    if (designerTab && !designerTab.closed) {
        console.log('📤 إرسال بيانات المدرسة للتبويب الجديد');

        const schoolData = prepareSchoolDataForDesigner(schoolId);

        // محاولة إرسال البيانات عدة مرات
        let attempts = 0;
        const maxAttempts = 5;

        const sendData = () => {
            try {
                designerTab.postMessage(schoolData, 'https://kushoofapp.com');
                console.log('📤 تم إرسال البيانات للتبويب:', schoolData);
            } catch (error) {
                console.warn(`⚠️ فشل في الإرسال (محاولة ${attempts + 1}):`, error);

                if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(sendData, 2000);
                }
            }
        };

        sendData();
    }
}

// دالة إرسال البيانات للنافذة المنفصلة
function sendDataToDesignerWindow(designerWindow, schoolId) {
    if (designerWindow && !designerWindow.closed) {
        console.log('📤 إرسال بيانات المدرسة للنافذة المنفصلة');

        const schoolData = prepareSchoolDataForDesigner(schoolId);

        // محاولة إرسال البيانات عدة مرات
        let attempts = 0;
        const maxAttempts = 5;

        const sendData = () => {
            try {
                designerWindow.postMessage(schoolData, 'https://kushoofapp.com');
                console.log('📤 تم إرسال البيانات للنافذة:', schoolData);
            } catch (error) {
                console.warn(`⚠️ فشل في الإرسال (محاولة ${attempts + 1}):`, error);

                if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(sendData, 2000);
                }
            }
        };

        sendData();
    }
}

// دالة تحضير بيانات المدرسة للمصمم
function prepareSchoolDataForDesigner(schoolId) {
    let schoolData = {
        schoolId: schoolId,
        schoolName: 'مدرسة تجريبية',
        managerName: 'مدير المدرسة',
        ministryNumber: '12345',
        studentsCount: 0,
        origin: 'kushoof_modal'
    };

    // محاولة جلب البيانات من الموديل الحالي
    try {
        if (window.name.includes('kushoof')) {
            const currentData = JSON.parse(window.name.split('kushoof')[1]);
            schoolData = {
                schoolId: schoolId,
                schoolName: currentData.school_name || schoolData.schoolName,
                managerName: currentData.manager_name || schoolData.managerName,
                ministryNumber: currentData.ministry_number || schoolData.ministryNumber,
                studentsCount: currentData.n ? currentData.n.length : schoolData.studentsCount,
                origin: 'kushoof_modal_current'
            };
            console.log('📊 بيانات من الموديل الحالي:', schoolData);
        }
    } catch (error) {
        console.warn('⚠️ فشل في قراءة البيانات من الموديل');
    }

    // محاولة جلب البيانات من النسخة الاحتياطية
    try {
        const storedData = localStorage.getItem('kushoof_backup_data');
        if (storedData) {
            const parsedData = JSON.parse(storedData);
            schoolData = {
                schoolId: schoolId,
                schoolName: parsedData.schoolData?.schoolName || schoolData.schoolName,
                managerName: parsedData.schoolData?.managerName || schoolData.managerName,
                ministryNumber: parsedData.schoolData?.ministryNumber || schoolData.ministryNumber,
                studentsCount: parsedData.schoolData?.totalFound || schoolData.studentsCount,
                origin: 'madrasati_stored'
            };
            console.log('📊 بيانات من النسخة الاحتياطية:', schoolData);
        }
    } catch (error) {
        console.warn('⚠️ فشل في قراءة البيانات المحفوظة:', error);
    }

    return schoolData;
}

// دالة إعداد event listeners لمصمم الكشوف الكامل
function setupFullDesignerEventListeners(schoolData) {
    console.log('🔧 إعداد event listeners لمصمم الكشوف الكامل');

    // زر المعاينة الفورية
    const previewBtn = document.getElementById('preview-btn');
    if (previewBtn) {
        previewBtn.addEventListener('click', () => generateAdvancedReportPreview(schoolData));
        console.log('✅ تم ربط زر المعاينة الفورية');
    }

    // زر الطباعة المباشرة
    const printBtn = document.getElementById('print-btn');
    if (printBtn) {
        printBtn.addEventListener('click', () => printAdvancedReport());
        console.log('✅ تم ربط زر الطباعة المباشرة');
    }

    // زر تصدير PDF
    const exportPdfBtn = document.getElementById('export-pdf-btn');
    if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', () => exportAdvancedToPDF());
        console.log('✅ تم ربط زر تصدير PDF');
    }

    // زر حفظ كقالب
    const saveTemplateBtn = document.getElementById('save-template-btn');
    if (saveTemplateBtn) {
        saveTemplateBtn.addEventListener('click', () => saveAsTemplate());
        console.log('✅ تم ربط زر حفظ القالب');
    }

    // تغيير نوع الكشف
    const reportType = document.getElementById('report-type');
    if (reportType) {
        reportType.addEventListener('change', (e) => {
            updateAdvancedReportTitle(e.target.value);
            generateAdvancedReportPreview(schoolData);
        });
        console.log('✅ تم ربط قائمة نوع الكشف');
    }

    // تغيير عنوان الكشف
    const reportTitle = document.getElementById('report-title');
    if (reportTitle) {
        reportTitle.addEventListener('input', () => generateAdvancedReportPreview(schoolData));
        console.log('✅ تم ربط حقل عنوان الكشف');
    }

    // تغيير اسم المدرسة
    const schoolName = document.getElementById('school-name');
    if (schoolName) {
        schoolName.addEventListener('input', () => generateAdvancedReportPreview(schoolData));
        console.log('✅ تم ربط حقل اسم المدرسة');
    }

    // تغيير اسم المدير
    const managerName = document.getElementById('manager-name');
    if (managerName) {
        managerName.addEventListener('input', () => generateAdvancedReportPreview(schoolData));
        console.log('✅ تم ربط حقل اسم المدير');
    }

    // تغيير لون الرأس
    const headerColor = document.getElementById('header-color');
    if (headerColor) {
        headerColor.addEventListener('change', () => generateAdvancedReportPreview(schoolData));
        console.log('✅ تم ربط قائمة لون الرأس');
    }

    // إنشاء المعاينة الأولية
    generateAdvancedReportPreview(schoolData);
}

// دالة تحديث عنوان الكشف المتقدم
function updateAdvancedReportTitle(reportType) {
    const titleInput = document.getElementById('report-title');
    if (!titleInput) return;

    const titles = {
        'attendance': 'كشف حضور وغياب الطلاب',
        'grades': 'كشف درجات الطلاب',
        'contacts': 'كشف أرقام هواتف أولياء الأمور',
        'students-list': 'قائمة الطلاب الكاملة',
        'behavior': 'كشف تقييم السلوك',
        'activities': 'كشف الأنشطة والمشاركات'
    };

    titleInput.value = titles[reportType] || 'كشف الطلاب';
}

// دالة إنشاء معاينة الكشف المتقدمة
function generateAdvancedReportPreview(schoolData) {
    console.log('📋 إنشاء معاينة الكشف المتقدمة');

    const previewArea = document.getElementById('report-preview');
    if (!previewArea) return;

    const reportType = document.getElementById('report-type')?.value || 'attendance';
    const reportTitle = document.getElementById('report-title')?.value || 'كشف الطلاب';
    const schoolName = document.getElementById('school-name')?.value || schoolData.schoolName;
    const managerName = document.getElementById('manager-name')?.value || schoolData.managerName;
    const headerColor = document.getElementById('header-color')?.value || 'blue';

    let reportHTML = '';

    switch (reportType) {
        case 'attendance':
            reportHTML = generateAdvancedAttendanceReport(reportTitle, schoolName, managerName, headerColor, schoolData);
            break;
        case 'grades':
            reportHTML = generateAdvancedGradesReport(reportTitle, schoolName, managerName, headerColor, schoolData);
            break;
        case 'contacts':
            reportHTML = generateAdvancedContactsReport(reportTitle, schoolName, managerName, headerColor, schoolData);
            break;
        case 'students-list':
            reportHTML = generateAdvancedStudentsListReport(reportTitle, schoolName, managerName, headerColor, schoolData);
            break;
        case 'behavior':
            reportHTML = generateBehaviorReport(reportTitle, schoolName, managerName, headerColor, schoolData);
            break;
        case 'activities':
            reportHTML = generateActivitiesReport(reportTitle, schoolName, managerName, headerColor, schoolData);
            break;
        default:
            reportHTML = generateAdvancedAttendanceReport(reportTitle, schoolName, managerName, headerColor, schoolData);
    }

    previewArea.innerHTML = reportHTML;
    console.log('✅ تم إنشاء معاينة الكشف المتقدمة');
}

// دوال إنشاء الكشوف المتقدمة (مؤقتة - تستخدم الدوال الموجودة)
function generateAdvancedAttendanceReport(title, schoolName, managerName, headerColor, schoolData) {
    return generateAttendanceReport(title, schoolName, managerName, schoolData);
}

function generateAdvancedGradesReport(title, schoolName, managerName, headerColor, schoolData) {
    return generateGradesReport(title, schoolName, managerName, schoolData);
}

function generateAdvancedContactsReport(title, schoolName, managerName, headerColor, schoolData) {
    return generateContactsReport(title, schoolName, managerName, schoolData);
}

function generateAdvancedStudentsListReport(title, schoolName, managerName, headerColor, schoolData) {
    return generateStudentsListReport(title, schoolName, managerName, schoolData);
}

function generateBehaviorReport(title, schoolName, managerName, headerColor, schoolData) {
    return generateStudentsListReport(title, schoolName, managerName, schoolData);
}

function generateActivitiesReport(title, schoolName, managerName, headerColor, schoolData) {
    return generateStudentsListReport(title, schoolName, managerName, schoolData);
}

// دوال الوظائف المتقدمة
function printAdvancedReport() {
    printReport();
}

function exportAdvancedToPDF() {
    exportToPDF();
}

function saveAsTemplate() {
    console.log('💾 حفظ كقالب');
    alert('🎯 ميزة حفظ القوالب ستكون متاحة قريباً!\n\nيمكنك حالياً استخدام تصدير PDF أو الطباعة.');
}

// دالة إعداد event listeners لمصمم الكشوف
function setupDesignerEventListeners(schoolData) {
    console.log('🔧 إعداد event listeners لمصمم الكشوف');

    // زر المعاينة
    const previewBtn = document.getElementById('preview-btn');
    if (previewBtn) {
        previewBtn.addEventListener('click', () => generateReportPreview(schoolData));
        console.log('✅ تم ربط زر المعاينة');
    }

    // زر الطباعة
    const printBtn = document.getElementById('print-btn');
    if (printBtn) {
        printBtn.addEventListener('click', () => printReport());
        console.log('✅ تم ربط زر الطباعة');
    }

    // زر تصدير PDF
    const exportPdfBtn = document.getElementById('export-pdf-btn');
    if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', () => exportToPDF());
        console.log('✅ تم ربط زر تصدير PDF');
    }

    // تغيير نوع الكشف
    const reportType = document.getElementById('report-type');
    if (reportType) {
        reportType.addEventListener('change', (e) => {
            updateReportTitle(e.target.value);
            generateReportPreview(schoolData);
        });
        console.log('✅ تم ربط قائمة نوع الكشف');
    }

    // تغيير عنوان الكشف
    const reportTitle = document.getElementById('report-title');
    if (reportTitle) {
        reportTitle.addEventListener('input', () => generateReportPreview(schoolData));
        console.log('✅ تم ربط حقل عنوان الكشف');
    }

    // تغيير اسم المدرسة
    const schoolName = document.getElementById('school-name');
    if (schoolName) {
        schoolName.addEventListener('input', () => generateReportPreview(schoolData));
        console.log('✅ تم ربط حقل اسم المدرسة');
    }

    // تغيير اسم المدير
    const managerName = document.getElementById('manager-name');
    if (managerName) {
        managerName.addEventListener('input', () => generateReportPreview(schoolData));
        console.log('✅ تم ربط حقل اسم المدير');
    }

    // إنشاء المعاينة الأولية
    generateReportPreview(schoolData);
}

// دالة تحديث عنوان الكشف حسب النوع
function updateReportTitle(reportType) {
    const titleInput = document.getElementById('report-title');
    if (!titleInput) return;

    const titles = {
        'attendance': 'كشف حضور وغياب',
        'grades': 'كشف درجات الطلاب',
        'contacts': 'كشف أرقام الهواتف',
        'students-list': 'قائمة الطلاب'
    };

    titleInput.value = titles[reportType] || 'كشف الطلاب';
}

// دالة إنشاء معاينة الكشف
function generateReportPreview(schoolData) {
    console.log('📋 إنشاء معاينة الكشف');

    const previewArea = document.getElementById('report-preview');
    if (!previewArea) return;

    const reportType = document.getElementById('report-type')?.value || 'attendance';
    const reportTitle = document.getElementById('report-title')?.value || 'كشف الطلاب';
    const schoolName = document.getElementById('school-name')?.value || schoolData.schoolName;
    const managerName = document.getElementById('manager-name')?.value || schoolData.managerName;

    let reportHTML = '';

    switch (reportType) {
        case 'attendance':
            reportHTML = generateAttendanceReport(reportTitle, schoolName, managerName, schoolData);
            break;
        case 'grades':
            reportHTML = generateGradesReport(reportTitle, schoolName, managerName, schoolData);
            break;
        case 'contacts':
            reportHTML = generateContactsReport(reportTitle, schoolName, managerName, schoolData);
            break;
        case 'students-list':
            reportHTML = generateStudentsListReport(reportTitle, schoolName, managerName, schoolData);
            break;
        default:
            reportHTML = generateAttendanceReport(reportTitle, schoolName, managerName, schoolData);
    }

    previewArea.innerHTML = reportHTML;
    console.log('✅ تم إنشاء معاينة الكشف');
}

// دالة إنشاء كشف الحضور والغياب
function generateAttendanceReport(title, schoolName, managerName, schoolData) {
    const today = new Date();
    const dateStr = today.toLocaleDateString('ar-SA');

    let studentsRows = '';

    // جلب بيانات الطلاب من window.name أو البيانات المحفوظة
    let students = [];
    try {
        if (window.name.includes('kushoof')) {
            const currentData = JSON.parse(window.name.split('kushoof')[1]);
            if (currentData.n && currentData.n.length > 0) {
                for (let i = 0; i < currentData.n.length; i++) {
                    students.push({
                        name: decodeURIComponent(currentData.n[i] || ''),
                        class: decodeURIComponent(currentData.c[i] || ''),
                        phone: currentData.p[i] || ''
                    });
                }
            }
        }
    } catch (error) {
        console.warn('⚠️ فشل في قراءة بيانات الطلاب من window.name');
    }

    // إذا لم توجد بيانات، استخدم بيانات تجريبية
    if (students.length === 0) {
        students = [
            { name: 'أحمد محمد علي', class: 'الأول-1', phone: '966501234567' },
            { name: 'فاطمة عبدالله', class: 'الأول-1', phone: '966507654321' },
            { name: 'محمد سالم', class: 'الأول-2', phone: '966509876543' },
            { name: 'نورا خالد', class: 'الأول-2', phone: '966502468135' },
            { name: 'عبدالله فهد', class: 'الثاني-1', phone: '966508642097' }
        ];
    }

    students.forEach((student, index) => {
        studentsRows += `
            <tr>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${index + 1}</td>
                <td style="border: 1px solid #000; padding: 8px;">${student.name}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${student.class}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 50px;"></td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 50px;"></td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 100px;"></td>
            </tr>
        `;
    });

    return `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 24px; color: #333;">${title}</h1>
            <h2 style="margin: 10px 0; font-size: 20px; color: #666;">${schoolName}</h2>
            <p style="margin: 5px 0; color: #888;">مدير المدرسة: ${managerName}</p>
            <p style="margin: 5px 0; color: #888;">التاريخ: ${dateStr}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">م</th>
                    <th style="border: 1px solid #000; padding: 10px;">اسم الطالب</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">الفصل</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">حاضر</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">غائب</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                ${studentsRows}
            </tbody>
        </table>

        <div style="margin-top: 30px; display: flex; justify-content: space-between;">
            <div>
                <p><strong>إجمالي الطلاب:</strong> ${students.length}</p>
                <p><strong>الحاضرون:</strong> ___</p>
                <p><strong>الغائبون:</strong> ___</p>
            </div>
            <div style="text-align: left;">
                <p>توقيع المعلم: ________________</p>
                <p>التاريخ: ${dateStr}</p>
            </div>
        </div>
    `;
}

// دالة إنشاء كشف الدرجات
function generateGradesReport(title, schoolName, managerName, schoolData) {
    const today = new Date();
    const dateStr = today.toLocaleDateString('ar-SA');

    let students = getStudentsData();

    let studentsRows = '';
    students.forEach((student, index) => {
        studentsRows += `
            <tr>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${index + 1}</td>
                <td style="border: 1px solid #000; padding: 8px;">${student.name}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${student.class}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 60px;"></td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 60px;"></td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 60px;"></td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 60px;"></td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 80px;"></td>
            </tr>
        `;
    });

    return `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 24px; color: #333;">${title}</h1>
            <h2 style="margin: 10px 0; font-size: 20px; color: #666;">${schoolName}</h2>
            <p style="margin: 5px 0; color: #888;">مدير المدرسة: ${managerName}</p>
            <p style="margin: 5px 0; color: #888;">التاريخ: ${dateStr}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">م</th>
                    <th style="border: 1px solid #000; padding: 10px;">اسم الطالب</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">الفصل</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">الاختبار الأول</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">الاختبار الثاني</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">أعمال السنة</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">الاختبار النهائي</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                ${studentsRows}
            </tbody>
        </table>
    `;
}

// دالة إنشاء كشف أرقام الهواتف
function generateContactsReport(title, schoolName, managerName, schoolData) {
    const today = new Date();
    const dateStr = today.toLocaleDateString('ar-SA');

    let students = getStudentsData();

    let studentsRows = '';
    students.forEach((student, index) => {
        studentsRows += `
            <tr>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${index + 1}</td>
                <td style="border: 1px solid #000; padding: 8px;">${student.name}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${student.class}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${student.phone}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 100px;"></td>
            </tr>
        `;
    });

    return `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 24px; color: #333;">${title}</h1>
            <h2 style="margin: 10px 0; font-size: 20px; color: #666;">${schoolName}</h2>
            <p style="margin: 5px 0; color: #888;">مدير المدرسة: ${managerName}</p>
            <p style="margin: 5px 0; color: #888;">التاريخ: ${dateStr}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">م</th>
                    <th style="border: 1px solid #000; padding: 10px;">اسم الطالب</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">الفصل</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">رقم الهاتف</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                ${studentsRows}
            </tbody>
        </table>
    `;
}

// دالة إنشاء قائمة الطلاب
function generateStudentsListReport(title, schoolName, managerName, schoolData) {
    const today = new Date();
    const dateStr = today.toLocaleDateString('ar-SA');

    let students = getStudentsData();

    let studentsRows = '';
    students.forEach((student, index) => {
        studentsRows += `
            <tr>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${index + 1}</td>
                <td style="border: 1px solid #000; padding: 8px;">${student.name}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center;">${student.class}</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 100px;"></td>
            </tr>
        `;
    });

    return `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 24px; color: #333;">${title}</h1>
            <h2 style="margin: 10px 0; font-size: 20px; color: #666;">${schoolName}</h2>
            <p style="margin: 5px 0; color: #888;">مدير المدرسة: ${managerName}</p>
            <p style="margin: 5px 0; color: #888;">التاريخ: ${dateStr}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">م</th>
                    <th style="border: 1px solid #000; padding: 10px;">اسم الطالب</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">الفصل</th>
                    <th style="border: 1px solid #000; padding: 10px; text-align: center;">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                ${studentsRows}
            </tbody>
        </table>

        <div style="margin-top: 30px;">
            <p><strong>إجمالي الطلاب:</strong> ${students.length}</p>
        </div>
    `;
}

// دالة مساعدة لجلب بيانات الطلاب
function getStudentsData() {
    let students = [];

    try {
        if (window.name.includes('kushoof')) {
            const currentData = JSON.parse(window.name.split('kushoof')[1]);
            if (currentData.n && currentData.n.length > 0) {
                for (let i = 0; i < currentData.n.length; i++) {
                    students.push({
                        name: decodeURIComponent(currentData.n[i] || ''),
                        class: decodeURIComponent(currentData.c[i] || ''),
                        phone: currentData.p[i] || ''
                    });
                }
            }
        }
    } catch (error) {
        console.warn('⚠️ فشل في قراءة بيانات الطلاب من window.name');
    }

    // إذا لم توجد بيانات، استخدم بيانات تجريبية
    if (students.length === 0) {
        students = [
            { name: 'أحمد محمد علي', class: 'الأول-1', phone: '966501234567' },
            { name: 'فاطمة عبدالله', class: 'الأول-1', phone: '966507654321' },
            { name: 'محمد سالم', class: 'الأول-2', phone: '966509876543' },
            { name: 'نورا خالد', class: 'الأول-2', phone: '966502468135' },
            { name: 'عبدالله فهد', class: 'الثاني-1', phone: '966508642097' }
        ];
    }

    return students;
}

// دالة الطباعة
function printReport() {
    console.log('🖨️ طباعة الكشف');

    const previewArea = document.getElementById('report-preview');
    if (!previewArea) {
        alert('❌ لا توجد معاينة للطباعة');
        return;
    }

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>طباعة الكشف</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            ${previewArea.innerHTML}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
}

// دالة تصدير PDF
function exportToPDF() {
    console.log('📄 تصدير PDF');

    const previewArea = document.getElementById('report-preview');
    if (!previewArea) {
        alert('❌ لا توجد معاينة للتصدير');
        return;
    }

    // إنشاء رابط تحميل
    const reportTitle = document.getElementById('report-title')?.value || 'كشف الطلاب';
    const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>${reportTitle}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #000; padding: 8px; text-align: right; }
                th { background-color: #f8f9fa; }
            </style>
        </head>
        <body>
            ${previewArea.innerHTML}
        </body>
        </html>
    `;

    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${reportTitle}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    alert('✅ تم تصدير الكشف كملف HTML\n\nيمكنك فتحه في المتصفح وطباعته كـ PDF');
}

// دالة إرسال البيانات للمصمم في الموديل
function sendDataToDesigner(schoolId) {
    const iframe = document.getElementById('designer-iframe');
    if (iframe && iframe.contentWindow) {
        console.log('📤 [Content] إرسال بيانات المدرسة للمصمم');

        // جلب البيانات الحقيقية من الذاكرة المحلية
        let realSchoolData = {
            schoolId: schoolId,
            schoolName: 'مدرسة تجريبية',
            managerName: 'مدير المدرسة',
            ministryNumber: '12345',
            studentsCount: 150,
            origin: 'madrasati'
        };

        // محاولة جلب البيانات من الموديل الحالي
        try {
            if (window.name.includes('kushoof')) {
                const currentData = JSON.parse(window.name.split('kushoof')[1]);
                realSchoolData = {
                    schoolId: schoolId,
                    schoolName: currentData.school_name || realSchoolData.schoolName,
                    managerName: currentData.manager_name || realSchoolData.managerName,
                    ministryNumber: currentData.ministry_number || realSchoolData.ministryNumber,
                    studentsCount: currentData.n ? currentData.n.length : realSchoolData.studentsCount,
                    origin: 'kushoof_modal_current'
                };
                console.log('📊 [Content] بيانات من الموديل الحالي:', realSchoolData);
            }
        } catch (error) {
            console.warn('⚠️ [Content] فشل في قراءة البيانات من الموديل');
        }

        // محاولة جلب البيانات من النسخة الاحتياطية
        try {
            const storedData = localStorage.getItem('kushoof_backup_data');
            if (storedData) {
                const parsedData = JSON.parse(storedData);
                console.log('📊 [Content] بيانات محفوظة:', parsedData);

                realSchoolData = {
                    schoolId: schoolId,
                    schoolName: parsedData.schoolData?.schoolName || realSchoolData.schoolName,
                    managerName: parsedData.schoolData?.managerName || realSchoolData.managerName,
                    ministryNumber: parsedData.schoolData?.ministryNumber || realSchoolData.ministryNumber,
                    studentsCount: parsedData.schoolData?.totalFound || realSchoolData.studentsCount,
                    origin: 'madrasati_stored'
                };
            }
        } catch (error) {
            console.warn('⚠️ [Content] فشل في قراءة البيانات المحفوظة:', error);
        }

        console.log('📤 [Content] إرسال البيانات الحقيقية:', realSchoolData);

        iframe.contentWindow.postMessage(realSchoolData, 'https://kushoofapp.com');
    }
}

// ==== دالة جلب وحفظ البيانات المتقدمة من c1.js ====

async function extractAndSaveData() {
    console.log('🚀 بدء عملية جلب وحفظ البيانات المتقدمة');

    try {
        // إظهار شاشة التحميل
        const loadingDiv = document.getElementById('modal-loading');
        const contentDiv = document.getElementById('modal-content');

        if (loadingDiv) {
            loadingDiv.style.display = 'block';
            loadingDiv.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div style="font-size: 24px; margin-bottom: 20px;">🚀</div>
                    <h3 style="color: #FFD700; margin-bottom: 20px;">جلب وحفظ البيانات</h3>
                    <div id="extraction-progress" style="font-size: 16px; line-height: 1.8;">
                        🔍 جاري البحث عن معرف المدرسة...
                    </div>
                    <div style="margin-top: 20px;">
                        <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.2); border-radius: 2px; overflow: hidden;">
                            <div id="progress-bar" style="width: 0%; height: 100%; background: linear-gradient(90deg, #FFD700, #FFA500); transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                </div>
            `;
        }
        if (contentDiv) contentDiv.style.display = 'none';

        // البحث عن معرف المدرسة
        updateProgress('🔍 البحث عن معرف المدرسة...', 10);
        const schoolId = getSchoolIdFromUrl();

        if (!schoolId) {
            throw new Error('لم يتم العثور على معرف المدرسة في URL');
        }

        console.log('🏫 معرف المدرسة:', schoolId);

        // فحص وجود نقطة استئناف
        let resumeData = null;
        try {
            const savedProgress = localStorage.getItem('kushoof_collection_progress');
            if (savedProgress) {
                resumeData = JSON.parse(savedProgress);
                if (resumeData.schoolId === schoolId) {
                    console.log(`📂 تم العثور على نقطة استئناف: ${resumeData.collectedStudents}/${resumeData.totalStudents}`);
                }
            }
        } catch (error) {
            console.warn('⚠️ فشل في قراءة نقطة الاستئناف:', error);
        }

        // تأكيد من المستخدم
        let confirmMessage = `🚀 استخراج وحفظ البيانات المتقدم

سيتم:
✅ جمع جميع بيانات الطلاب من مدرستي
✅ جمع معلومات المدرسة والمدير
✅ حفظ البيانات في قاعدة البيانات
✅ عرض النتائج في الموديل

⏱️ قد يستغرق 10-15 دقيقة حسب عدد الطلاب`;

        if (resumeData && resumeData.collectedStudents > 0) {
            confirmMessage += `

📂 تم العثور على عملية سابقة:
   • تم جمع: ${resumeData.collectedStudents} طالب
   • المجموع: ${resumeData.totalStudents} طالب
   • التاريخ: ${new Date(resumeData.timestamp).toLocaleString('ar-SA')}

🔄 سيتم استكمال الجمع من حيث توقفت`;
        }

        confirmMessage += `

هل تريد المتابعة؟`;

        const confirmed = confirm(confirmMessage);

        if (!confirmed) {
            // إعادة عرض المحتوى العادي
            if (loadingDiv) loadingDiv.style.display = 'none';
            if (contentDiv) contentDiv.style.display = 'block';
            return;
        }

        // بدء عملية الجمع
        updateProgress('📊 جاري جمع بيانات الطلاب من مدرستي...', 20);
        const students = await collectStudentsFromMadrasati(schoolId);

        console.log(`👥 تم جمع ${students.length} طالب من أصل 147 طالب متوقع`);

        if (!students || students.length === 0) {
            throw new Error('لم يتم العثور على بيانات طلاب');
        }

        console.log(`👥 تم جمع ${students.length} طالب`);
        updateProgress(`✅ تم جمع ${students.length} طالب`, 50);

        // جمع معلومات المدرسة
        updateProgress('🏫 جاري جمع معلومات المدرسة...', 60);
        const schoolInfo = await collectSchoolInfo(schoolId);

        // تحضير البيانات للحفظ
        updateProgress('📋 تحضير البيانات للحفظ...', 70);
        const schoolData = {
            schoolId: schoolId,
            schoolName: schoolInfo.schoolName || 'غير محدد',
            managerName: schoolInfo.managerName || 'غير محدد',
            ministryNumber: schoolInfo.ministryNumber || null,
            studentsCountFromTable: schoolInfo.studentsCount || null,
            teachersCount: schoolInfo.teachersCount || null,
            academicYear: schoolInfo.academicYear || null,
            semester: schoolInfo.semester || null,
            extractedAt: new Date().toISOString(),
            students: students,
            url: window.location.href,
            totalFound: students.length,
            extractionMethod: 'kushoof_modal_advanced'
        };

        console.log('📊 البيانات المحضرة:', schoolData);

        // حفظ البيانات في قاعدة البيانات
        updateProgress('💾 حفظ البيانات في قاعدة البيانات...', 80);

        let saveSuccess = false;
        let saveError = null;

        try {
            const saveResponse = await chrome.runtime.sendMessage({
                action: 'saveToDatabase',
                schoolData: schoolData
            });

            if (saveResponse && saveResponse.success) {
                saveSuccess = true;
                console.log('✅ تم حفظ البيانات في قاعدة البيانات بنجاح');
            } else {
                saveError = saveResponse?.error || 'فشل في حفظ البيانات';
                console.warn('⚠️ فشل في حفظ البيانات:', saveError);
            }
        } catch (error) {
            saveError = error.message;
            console.warn('⚠️ خطأ في حفظ البيانات:', saveError);
        }

        // تحويل البيانات لتنسيق الموديل
        updateProgress('🎨 تحضير العرض...', 90);
        const modalData = convertToModalFormat(schoolData);

        // حفظ البيانات في window.name
        window.name = 'kushoof' + JSON.stringify(modalData);

        // حفظ نسخة احتياطية في localStorage
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                schoolData: schoolData,
                modalData: modalData
            };
            localStorage.setItem('kushoof_backup_data', JSON.stringify(backupData));
            console.log('💾 تم حفظ نسخة احتياطية في localStorage');

            // مسح نقطة الاستئناف بعد اكتمال العملية
            localStorage.removeItem('kushoof_collection_progress');
            console.log('🗑️ تم مسح نقطة الاستئناف بعد اكتمال العملية');
        } catch (error) {
            console.warn('⚠️ فشل في حفظ النسخة الاحتياطية:', error);
        }

        // عرض النتائج
        updateProgress('✅ اكتمل!', 100);

        setTimeout(() => {
            displayModalData(modalData);

            // عرض رسالة نجاح
            setTimeout(() => {
                const saveStatusMessage = saveSuccess ?
                    '💾 تم الحفظ في قاعدة البيانات بنجاح!' :
                    `⚠️ تم الاستخراج بنجاح لكن فشل الحفظ: ${saveError}`;

                alert(`🎉 تم الاستخراج بنجاح!

🏫 معلومات المدرسة:
   • الاسم: ${schoolData.schoolName}
   • المدير: ${schoolData.managerName}
   • الرقم الوزاري: ${schoolData.ministryNumber || 'غير متوفر'}
   • العام الدراسي: ${schoolData.academicYear || 'غير متوفر'}
   • الفصل الدراسي: ${schoolData.semester || 'غير متوفر'}

📊 بيانات الطلاب:
   • العدد المستخرج: ${schoolData.totalFound}
   • العدد من الجدول: ${schoolData.studentsCountFromTable || 'غير متوفر'}
   • عدد المعلمين: ${schoolData.teachersCount || 'غير متوفر'}

${saveStatusMessage}

🎯 البيانات متاحة الآن في الموديل!
${saveSuccess ? '🎨 يمكنك فتح مصمم الكشوف لعرض البيانات' : '📥 يمكنك تصدير البيانات كملف JSON'}`);
            }, 1000);
        }, 500);

        console.log('🎉 تم إكمال عملية الاستخراج والحفظ بنجاح');

    } catch (error) {
        console.error('❌ خطأ في عملية الاستخراج والحفظ:', error);

        // إخفاء شاشة التحميل
        const loadingDiv = document.getElementById('modal-loading');
        const contentDiv = document.getElementById('modal-content');
        if (loadingDiv) loadingDiv.style.display = 'none';
        if (contentDiv) contentDiv.style.display = 'block';

        alert('❌ خطأ في عملية الاستخراج والحفظ:\n\n' + error.message);
    }
}

// دالة تحديث شريط التقدم
function updateProgress(message, percentage) {
    const progressElement = document.getElementById('extraction-progress');
    const progressBar = document.getElementById('progress-bar');

    if (progressElement) {
        progressElement.textContent = message;
    }

    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }

    console.log(`📊 ${percentage}% - ${message}`);
}

// دالة جلب معرف المدرسة من URL
function getSchoolIdFromUrl() {
    const currentUrl = window.location.href;
    console.log('🔍 البحث في الرابط:', currentUrl);

    // أنماط مختلفة للبحث عن معرف المدرسة
    const patterns = [
        /SchoolId=([A-F0-9]{32})/i,
        /Index\/([A-F0-9]{32})/i,
        /\/([A-F0-9]{32})/i,
        /schoolId[=\/]([A-F0-9]{32})/i,
        /Schools\/([A-F0-9]{32})/i,
        /ManageStudents\/([A-F0-9]{32})/i
    ];

    for (const pattern of patterns) {
        const match = currentUrl.match(pattern);
        if (match) {
            console.log('✅ تم العثور على معرف المدرسة:', match[1]);
            return match[1];
        }
    }

    console.log('❌ لم يتم العثور على معرف المدرسة');
    return null;
}

// دالة جمع الطلاب من مدرستي (الطريقة الأصلية من c1.js)
async function collectStudentsFromMadrasati(schoolId) {
    console.log('📊 [Collect] بدء جمع الطلاب...');

    if (!schoolId) return [];

    const base = `https://schools.madrasati.sa/SchoolManagment/Students/ManageStudents/${schoolId}`;
    let page = 1;
    let links = [];

    console.log('🔍 [Collect] البحث عن روابط الطلاب...');

    while (true) {
        try {
            console.log(`📄 [Collect] فحص الصفحة ${page}...`);
            updateProgress(`📄 فحص الصفحة ${page} للبحث عن الطلاب...`, 15 + (page * 2));

            const html = await fetch(`${base}?PageNumber=${page}&years=-1&semesters=-1&classrooms=-1`, {
                credentials: 'include'
            }).then(r => r.text());

            const doc = new DOMParser().parseFromString(html, 'text/html');
            const hrefs = Array.from(doc.querySelectorAll("a[href*='StudentInfo']")).map(a => a.href);

            if (!hrefs.length) {
                console.log(`📄 [Collect] لا توجد روابط في الصفحة ${page}, انتهاء البحث`);
                break;
            }

            console.log(`📄 [Collect] تم العثور على ${hrefs.length} رابط في الصفحة ${page}`);
            links.push(...hrefs);
            page++;

            if (page > 50) {
                console.log('⚠️ [Collect] تم الوصول للحد الأقصى من الصفحات (50)');
                break;
            }

        } catch (error) {
            console.error(`❌ [Collect] خطأ في الصفحة ${page}:`, error);
            break;
        }
    }

    links = Array.from(new Set(links));
    console.log(`🔗 [Collect] إجمالي الروابط الفريدة: ${links.length}`);

    if (links.length === 0) {
        throw new Error('لم يتم العثور على أي روابط طلاب');
    }

    const all = [];
    console.log('👥 [Collect] بدء جمع تفاصيل الطلاب...');

    for (let i = 0; i < links.length; i++) {
        try {
            console.log(`👤 [Collect] جمع بيانات الطالب ${i + 1}/${links.length}...`);
            updateProgress(`👤 جمع بيانات الطالب ${i + 1}/${links.length}...`, 25 + (i / links.length) * 25);

            const stu = await fetchStudentDetail(links[i]);
            if (stu) {
                all.push(stu);
                console.log(`✅ [Collect] تم جمع بيانات: ${stu.name} - ${stu.classLabel}`);
            }

            if (i % 10 === 0 && i > 0) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

        } catch (error) {
            console.error(`❌ [Collect] خطأ في جمع بيانات الطالب ${i + 1}:`, error);
        }
    }

    console.log(`✅ [Collect] تم جمع ${all.length} طالب من أصل ${links.length} رابط`);
    return all;
}

// دالة جلب معلومات طالب أساسية
async function fetchStudentBasicInfo(url) {
    try {
        const response = await fetch(url, { credentials: 'include' });
        const html = await response.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');

        const name = doc.querySelector("h2#FullNameId")?.innerText.trim() || "";
        const parentMobile = doc.querySelector("#ParentMobile")?.value.trim() || "";
        const studentMobile = doc.querySelector("#Mobile")?.value.trim() || "";

        // تنظيف أرقام الهواتف
        const parentPhone = normalizePhoneNumber(parentMobile);
        const studentPhone = normalizePhoneNumber(studentMobile);

        if (name) {
            return {
                name: name,
                parentPhone: parentPhone,
                studentPhone: studentPhone,
                classLabel: 'غير محدد',
                grade: 'غير محدد',
                section: '',
                username: '',
                nationalId: ''
            };
        }
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات الطالب:', error);
    }
    return null;
}

// دالة تنظيف رقم الهاتف
function normalizePhoneNumber(phone) {
    if (!phone) return '';

    const cleaned = phone.replace(/[\s\-–—]/g, '').trim();
    if (cleaned.startsWith('0')) {
        return '966' + cleaned.slice(1);
    } else if (!cleaned.startsWith('966')) {
        return '966' + cleaned;
    }
    return cleaned;
}

// دالة إنشاء بيانات طلاب تجريبية
function createDemoStudents() {
    console.log('📝 إنشاء بيانات طلاب تجريبية...');

    const demoNames = [
        'أحمد محمد علي السعدي',
        'فاطمة عبدالله حسن الأحمدي',
        'محمد سالم أحمد القحطاني',
        'نورا خالد عبدالرحمن العتيبي',
        'عبدالله فهد محمد الدوسري'
    ];

    const students = [];

    for (let i = 0; i < demoNames.length; i++) {
        students.push({
            name: demoNames[i],
            parentPhone: `96650${1000000 + Math.floor(Math.random() * 9000000)}`,
            studentPhone: `96650${1000000 + Math.floor(Math.random() * 9000000)}`,
            classLabel: `الصف ${i + 1}`,
            grade: `الصف ${i + 1}`,
            section: String.fromCharCode(65 + i), // A, B, C, etc.
            username: `student${i + 1}`,
            nationalId: `${1000000000 + Math.floor(Math.random() * 999999999)}`
        });
    }

    return students;
}

// دالة جلب تفاصيل طالب واحد (الطريقة الأصلية من c1.js)
async function fetchStudentDetail(url) {
    try {
        const res = await fetch(url, { credentials: 'include' });
        const html = await res.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');

        // البيانات الأساسية
        const name = doc.querySelector("h2#FullNameId")?.innerText.trim() || "";
        const parentMobile = doc.querySelector("#ParentMobile")?.value.trim() || "";
        const parentPhone = normalizePhoneNumber(parentMobile);

        // رقم جوال الطالب من input#Mobile
        const studentMobile = doc.querySelector("#Mobile")?.value.trim() || "";
        const studentPhone = normalizePhoneNumber(studentMobile);

        // اسم المستخدم من label for="MicrosoftUserName"
        let username = "";
        const usernameLabel = doc.querySelector('label[for="MicrosoftUserName"]');
        if (usernameLabel) {
            username = usernameLabel.textContent.trim();
        }
        // محاولة بديلة للبحث عن اسم المستخدم
        if (!username) {
            const usernameInput = doc.querySelector("#UserName, #MicrosoftUserName");
            if (usernameInput) {
                username = usernameInput.value?.trim() || usernameInput.textContent?.trim() || "";
            }
        }

        // السجل المدني
        const nationalId = doc.querySelector("#NationalId")?.value.trim() || "";

        // معلومات الفصل والصف
        const h6 = doc.querySelector("h6.fw-medium");
        let clsRaw = "";
        if (h6) {
            const c = h6.cloneNode(true);
            c.querySelector("small")?.remove();
            clsRaw = c.innerText.trim();
        }
        clsRaw = clsRaw.replace(/[٠-٩]/g, d => String.fromCharCode(d.charCodeAt(0) - 0x0660 + 0x30));
        const parts = clsRaw.match(/^(.+?)[\s-]?(\d+)$/u);
        const classLabel = parts ? `${parts[1].trim()}-${parts[2]}` : clsRaw;

        // استخراج الصف والفصل منفصلين
        const grade = parts ? parts[1].trim() : clsRaw;
        const section = parts ? parts[2] : "";

        console.log(`👤 [Student Detail] ${name}: phone=${studentPhone}, username=${username}, nationalId=${nationalId}`);

        if (name) {
            return {
                name,                    // اسم الطالب
                parentPhone,            // رقم جوال ولي الأمر
                studentPhone,           // رقم جوال الطالب
                username,               // اسم المستخدم
                nationalId,             // السجل المدني
                classLabel,             // الفصل الكامل (مثل: الأول-1)
                grade,                  // الصف (مثل: الأول)
                section                 // الفصل (مثل: 1)
            };
        }
    } catch (e) {
        console.error("fetchStudentDetail failed:", e);
    }
    return null;
}



// دالة جمع معلومات المدرسة
async function collectSchoolInfo(schoolId) {
    console.log('🏫 جمع معلومات المدرسة...');

    const schoolInfo = {
        schoolName: 'غير محدد',
        managerName: 'غير محدد',
        ministryNumber: null,
        studentsCount: null,
        teachersCount: null,
        academicYear: null,
        semester: null
    };

    try {
        // جلب اسم المدير من الصفحة الحالية
        document.querySelectorAll("div.mx-2").forEach(div => {
            const txt = div.innerText.trim();
            if (txt.includes("مرحبا بك")) {
                const span = div.querySelector("span")?.innerText.trim();
                if (span) {
                    schoolInfo.managerName = span;
                    console.log('👨‍💼 تم العثور على اسم المدير:', schoolInfo.managerName);
                }
            }
        });

        // جلب اسم المدرسة من الصفحة الحالية
        document.querySelectorAll("div.mx-2").forEach(div => {
            const txt = div.innerText.trim();
            if (/ابتدائية|متوسطة|ثانوية|روض/.test(txt)) {
                const name = txt.replace(/قائمة\s+المدارس?/, "").trim();
                if (name) {
                    schoolInfo.schoolName = name;
                    console.log('🏫 تم العثور على اسم المدرسة:', schoolInfo.schoolName);
                }
            }
        });

        // جلب بيانات إضافية من صفحة التقارير
        try {
            const reportsUrl = `https://schools.madrasati.sa/SchoolManagmentReports/Schools/Index/${schoolId}`;
            const response = await fetch(reportsUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            const table = doc.querySelector('#DataTables_Table_0');
            if (table) {
                const firstRow = table.querySelector('tbody tr');
                if (firstRow) {
                    const cells = firstRow.querySelectorAll('td');
                    const visibleCells = Array.from(cells).filter(cell =>
                        cell.style.display !== 'none' && !cell.classList.contains('dtr-hidden')
                    );

                    if (visibleCells.length >= 5) {
                        if (schoolInfo.schoolName === 'غير محدد') {
                            schoolInfo.schoolName = visibleCells[0]?.textContent.trim() || 'غير محدد';
                        }
                        schoolInfo.ministryNumber = visibleCells[1]?.textContent.trim() || null;
                        schoolInfo.teachersCount = parseInt(visibleCells[3]?.textContent.trim()) || null;
                        schoolInfo.studentsCount = parseInt(visibleCells[4]?.textContent.trim()) || null;

                        console.log('✅ تم جلب بيانات إضافية من جدول التقارير');
                    }
                }
            }
        } catch (error) {
            console.log('⚠️ خطأ في جلب بيانات التقارير:', error.message);
        }

        // جلب العام الدراسي والفصل
        try {
            const classroomsUrl = `https://schools.madrasati.sa/SchoolStructure/ClassRooms?SchoolId=${schoolId}`;
            const response = await fetch(classroomsUrl, { credentials: 'include' });
            const html = await response.text();
            const doc = new DOMParser().parseFromString(html, 'text/html');

            const info = doc.querySelector("div.row.d-flex.mx-2 small")?.innerText;
            const match = info?.match(/العام الدراسي\s*(\d+).*الفصل الدراسي\s*([^\s]+)/);
            if (match) {
                schoolInfo.academicYear = match[1];
                schoolInfo.semester = match[2];
                console.log('✅ العام الدراسي:', schoolInfo.academicYear, 'الفصل:', schoolInfo.semester);
            }
        } catch (error) {
            console.log('⚠️ خطأ في جلب العام الدراسي:', error.message);
        }

    } catch (error) {
        console.error('❌ خطأ في جمع معلومات المدرسة:', error);
    }

    console.log('🏫 معلومات المدرسة المجمعة:', schoolInfo);
    return schoolInfo;
}

// دالة تحويل البيانات لتنسيق الموديل
function convertToModalFormat(schoolData) {
    console.log('🔄 تحويل البيانات لتنسيق الموديل...');

    const names = [];
    const phones = [];
    const classes = [];

    schoolData.students.forEach(student => {
        names.push(encodeURIComponent(student.name || ''));
        phones.push(student.parentPhone || student.studentPhone || '');
        classes.push(encodeURIComponent(student.classLabel || student.grade || ''));
    });

    const today = new Date();
    const arabicDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = arabicDays[today.getDay()];
    const formattedDate = `${dayName} ${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

    return {
        n: names,
        p: phones,
        c: classes,
        day: formattedDate,
        school_id: schoolData.schoolId,
        school_name: schoolData.schoolName,
        manager_name: schoolData.managerName,
        ministry_number: schoolData.ministryNumber,
        version: 'advanced_extraction',
        timestamp: new Date().toISOString(),
        source: 'madrasati_advanced_extraction',
        total_students: schoolData.totalFound,
        students_from_table: schoolData.studentsCountFromTable,
        teachers_count: schoolData.teachersCount,
        academic_year: schoolData.academicYear,
        semester: schoolData.semester,
        extracted_at: schoolData.extractedAt
    };
}

// مراقبة DOM لمنع اختفاء الواجهة
let domWatcher = null;
let kushoofInterfaceData = null;

function startDOMWatcher() {
    console.log('👁️ بدء مراقبة DOM لحماية واجهة Kushoof');

    // حفظ بيانات الواجهة
    if (window.name.includes('kushoof')) {
        try {
            kushoofInterfaceData = JSON.parse(window.name.split('kushoof')[1]);
        } catch (e) {
            console.warn('⚠️ فشل في حفظ بيانات الواجهة');
        }
    }

    // إنشاء MutationObserver
    domWatcher = new MutationObserver((mutations) => {
        let interfaceRemoved = false;

        mutations.forEach((mutation) => {
            // فحص العقد المحذوفة
            mutation.removedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // فحص إذا كانت واجهة Kushoof محذوفة
                    if (node.classList && node.classList.contains('kushoof-interface')) {
                        interfaceRemoved = true;
                        console.log('⚠️ تم حذف واجهة Kushoof - جاري الاستعادة...');
                    }

                    // فحص إذا كان العنصر المحذوف يحتوي على واجهة Kushoof
                    if (node.querySelector && node.querySelector('.kushoof-interface')) {
                        interfaceRemoved = true;
                        console.log('⚠️ تم حذف عنصر يحتوي على واجهة Kushoof - جاري الاستعادة...');
                    }
                }
            });
        });

        // إعادة حقن الواجهة إذا تم حذفها
        if (interfaceRemoved) {
            setTimeout(() => {
                restoreKushoofInterface();
            }, 500);
        }
    });

    // بدء المراقبة
    domWatcher.observe(document.body, {
        childList: true,
        subtree: true
    });
}

function restoreKushoofInterface() {
    // فحص إذا كانت الواجهة موجودة بالفعل
    if (document.querySelector('.kushoof-interface')) {
        console.log('✅ واجهة Kushoof موجودة بالفعل');
        return;
    }

    console.log('🔄 استعادة واجهة Kushoof...');

    // ضمان وجود الدوال في النطاق العام
    ensureGlobalFunctions();

    if (kushoofInterfaceData) {
        // إعادة حقن الواجهة
        const targetElement = findTargetElement();
        if (targetElement) {
            injectKushoofInterface(targetElement);
            console.log('✅ تم استعادة واجهة Kushoof بنجاح');
        }
    } else {
        console.warn('⚠️ لا توجد بيانات محفوظة لاستعادة الواجهة');
    }
}

function findTargetElement() {
    // البحث عن عنصر مناسب لحقن الواجهة
    const alternatives = [
        document.querySelector('.navbar'),
        document.querySelector('.header'),
        document.querySelector('#header'),
        document.querySelector('nav'),
        document.querySelector('.top-bar'),
        document.querySelector('body > div:first-child'),
        document.body
    ];

    for (const element of alternatives) {
        if (element) {
            return element;
        }
    }

    return null;
}

// فحص دوري للتأكد من بقاء الواجهة
let periodicChecker = null;

function startPeriodicCheck() {
    console.log('⏰ بدء الفحص الدوري لواجهة Kushoof');

    periodicChecker = setInterval(() => {
        const interface = document.querySelector('.kushoof-interface');

        if (!interface) {
            console.log('⚠️ الواجهة مفقودة - جاري الاستعادة...');
            restoreKushoofInterface();
        }
    }, 3000); // فحص كل 3 ثوان
}

function stopPeriodicCheck() {
    if (periodicChecker) {
        clearInterval(periodicChecker);
        periodicChecker = null;
        console.log('🛑 تم إيقاف الفحص الدوري');
    }
}

// إيقاف مراقبة DOM عند الحاجة
function stopDOMWatcher() {
    if (domWatcher) {
        domWatcher.disconnect();
        domWatcher = null;
        console.log('🛑 تم إيقاف مراقبة DOM');
    }
}

// بدء فحص الموقع عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        ensureGlobalFunctions();
        checkCurrentSite();
    });
} else {
    ensureGlobalFunctions();
    checkCurrentSite();
}

// مراقبة تغييرات URL
let currentURL = window.location.href;
setInterval(() => {
    if (window.location.href !== currentURL) {
        currentURL = window.location.href;
        console.log('🔄 تغيير URL:', currentURL);
        ensureGlobalFunctions(); // ضمان الدوال عند تغيير URL
        checkCurrentSite();
    }

    // ضمان الدوال كل 5 ثوان كإجراء احتياطي
    if (Math.random() < 0.2) { // 20% احتمال كل ثانية = مرة كل 5 ثوان تقريباً
        ensureGlobalFunctions();
    }
}, 1000);
